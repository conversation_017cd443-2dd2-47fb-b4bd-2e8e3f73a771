#!/usr/bin/env bash

CMD_PING="ping -c2"
SSH_OPT="-o StrictHostKeyChecking=no"

# items=("dev" "test" "prod")
items=("test")

for item in "${items[@]}"; do
    echo "Current item is: $item"

    # docker-vms
    ## galera_vm_lb
    $CMD_PING digifischdok-"$item"-galera-lb.dsecurecloud.de
    $CMD_PING **************
    ssh-keyscan digifischdok-"$item"-galera-lb.dsecurecloud.de >/dev/null 2>&1
    sshpass -p 'homosapiens@2024' ssh "$SSH_OPT" galera_vm_lb@digifischdok-"$item"-galera-lb.dsecurecloud.de "echo Hello World"

    ## galera_vm_node_a
    $CMD_PING digifischdok-"$item"-galera-node-a.dsecurecloud.de
    $CMD_PING **************
    ssh-keyscan digifischdok-"$item"-galera-node-a.dsecurecloud.de >/dev/null 2>&1
    sshpass -p 'homosapiens@2024' ssh "$SSH_OPT" galera_vm_node_a@digifischdok-"$item"-galera-node-a.dsecurecloud.de "echo Hello World"

    ## galera_vm_node_b
    $CMD_PING digifischdok-"$item"-galera-node-b.dsecurecloud.de
    $CMD_PING **************
    ssh-keyscan digifischdok-"$item"-galera-node-b.dsecurecloud.de >/dev/null 2>&1
    sshpass -p 'homosapiens@2024' ssh "$SSH_OPT" galera_vm_node_b@digifischdok-"$item"-galera-node-b.dsecurecloud.de "echo Hello World"

    ## galera_vm_node_c
    $CMD_PING digifischdok-"$item"-galera-node-c.dsecurecloud.de
    $CMD_PING 141.91.177.174
    ssh-keyscan digifischdok-"$item"-galera-node-c.dsecurecloud.de >/dev/null 2>&1
    sshpass -p 'homosapiens@2024' ssh "$SSH_OPT" galera_vm_node_c@digifischdok-"$item"-galera-node-c.dsecurecloud.de "echo Hello World"

    ## id_dbase_vm
    $CMD_PING digifischdok-"$item"-id_db.dsecurecloud.de
    $CMD_PING 141.91.177.178
    ssh-keyscan digifischdok-"$item"-id_db.dsecurecloud.de >/dev/null 2>&1
    sshpass -p 'homosapiens@2024' ssh "$SSH_OPT" id_dbase_vm@digifischdok-"$item"-id_db.dsecurecloud.de "echo Hello World"

    ## log_node_vm
    $CMD_PING digifischdok-"$item"-log-node.dsecurecloud.de
    $CMD_PING 141.91.177.168
    ssh-keyscan digifischdok-"$item"-log-node.dsecurecloud.de >/dev/null 2>&1
    sshpass -p 'HomoSapiens@2024' ssh "$SSH_OPT" log_node_vm@digifischdok-"$item"-log-node.dsecurecloud.de "echo Hello World"

    ## pub_dbase_vm
    $CMD_PING digifischdok-"$item"-pub_db.dsecurecloud.de
    $CMD_PING 141.91.177.177
    ssh-keyscan digifischdok-"$item"-pub_db.dsecurecloud.de >/dev/null 2>&1
    sshpass -p 'homosapiens@2024' ssh "$SSH_OPT" pub_dbase_vm@digifischdok-"$item"-pub_db.dsecurecloud.de "echo Hello World"

    # databases
    
done