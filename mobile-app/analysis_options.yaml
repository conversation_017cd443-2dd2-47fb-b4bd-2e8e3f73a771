linter:
  rules:
    prefer_single_quotes: true
    sort_constructors_first: true
    prefer_relative_imports: true
    require_trailing_commas: true

analyzer:
  exclude:
    - '**/*.g.dart'
    - '**/*.freezed.dart'
    - '**/*.tailor.dart'
    - '**/*.mocks.dart'
    - '**/*.gr.dart'
    - '**/l10n/*'
    - '**/*.chopper.dart'
    - '**/*.gen.dart'
    - '**/*sample*_repository.dart'
  errors:
    # We ignore this warning so json_annotation doesn't get confused with frozen usage
    invalid_annotation_target: ignore
    prefer_const_constructors: ignore
