import 'package:flutter/material.dart';

import '../../../design.dart';

/// Private class for app fonts from pubspec.yaml
class _AppFonts {
  const _AppFonts._();

  static const TextStyle notoSans =
      TextStyle(fontFamily: 'NotoSans', package: 'design');
}

/// DON'T USE THIS CLASS/TEXT-STYLES DIRECTLY IN YOUR APP. JUST USE IT TO DEFINE YOUR THEMES!
class StyleGuideTextStyles {
  static TextStyle headline = _AppFonts.notoSans.copyWith(
    fontSize: 24.0,
    fontWeight: FontWeight.w700, // Bold
    height: 28.0 /
        24.0, // If Figma defines line-height of 32, use fontSize * x = Figma line-height (28 * x = 32) --> 32 / 28 = 1.14
  );

  static TextStyle highlight = _AppFonts.notoSans.copyWith(
    fontSize: 18.0,
    fontWeight: FontWeight.w400,
    height: 20.5 / 18.0,
  );

  static TextStyle header = _AppFonts.notoSans.copyWith(
    fontSize: 24.0,
    fontWeight: FontWeight.w700,
    height: 39.0 / 24.0,
    color: StyleGuideColors.brandPrimary,
  );

  static TextStyle body = _AppFonts.notoSans.copyWith(
    fontSize: 16.0,
    fontWeight: FontWeight.w400,
    height: 24.0 / 16.0,
  );

  static TextStyle label = _AppFonts.notoSans.copyWith(
    fontSize: 24.0,
    fontWeight: FontWeight.w300,
    height: 32.0 / 24.0,
    fontStyle: FontStyle.italic,
  );

  static TextStyle title = _AppFonts.notoSans.copyWith(
    fontSize: 24.0,
    fontWeight: FontWeight.w300,
    height: 32.0 / 24.0,
  );

  static TextStyle formLabel = _AppFonts.notoSans.copyWith(
    fontSize: 16.0,
    fontWeight: FontWeight.w700,
    height: 21.0 / 16.0,
  );

  static TextStyle logoTitle = _AppFonts.notoSans.copyWith(
    fontSize: 32.0,
    fontWeight: FontWeight.w800,
    height: 43.0 / 32.0,
  );

  static TextStyle rowTableText = _AppFonts.notoSans.copyWith(
    fontSize: 12.0,
    fontWeight: FontWeight.w400,
    height: 24.0 / 12.0,
  );

  static TextStyle signInTitle = _AppFonts.notoSans.copyWith(
    fontSize: 24.0,
    fontWeight: FontWeight.w700,
  );

  static TextStyle signInTitleBig = _AppFonts.notoSans.copyWith(
    fontSize: 48.0,
    fontWeight: FontWeight.w800,
  );

  static TextStyle errorTagText = _AppFonts.notoSans.copyWith(
    fontSize: 16.0,
    fontWeight: FontWeight.w800,
    height: 22.0 / 16.0,
  );
}
