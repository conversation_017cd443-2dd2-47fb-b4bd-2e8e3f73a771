import 'package:flutter/material.dart';

/// Inside this class we define all colors given from the designers style guide for your app. This colors are independent from Flutters dark/light mode.
/// For reference the link https://wiki.adesso-mobile.de/display/Styleguide/Farben this file based on.
/// Always use the same names for your colors as defined inside the style guide.
///
/// DON'T USE THIS CLASS/COLORS DIRECTLY IN YOUR APP. JUST USE IT TO DEFINE YOUR THEMES!
class StyleGuideColors {
  const StyleGuideColors._();

  /// If your style guide has colors without gradations use simple Color class.
  static const Color brandPrimary = Color(0xff163bbf);
  static const Color textSecondary = Color(0xff526071);
  static const Color textPrimary = Color(0xff272D33);
  static const Color background = Color(0xffCDD2DA);
  static const Color lightBackground = Color(0xFFE6E8EC);
  static const Color invalid = Color(0xFFCB3973);
  static const Color textWarning = Color(0xFFAD285F);
  static const Color errorTag = Color.fromRGBO(173, 40, 95, 0.9);
  static const Color missing = Color(0xFF424C59);
  static const Color brandSecondary = Colors.white;
  static const Color infoBackground = Color(0xCC272D33);
  static const Color disabledColor = Color(0xFF78889B);
  static const Color buttonBorder = Color(0xFF9FAFE6);
  static const Color disabledButtonColor = Color.fromRGBO(255, 255, 255, 0.48);
  static const Color indicatorLoadingColor = Colors.black;
  static const Color blueShadow = Color(0x3D163BBF);
  static const Color gradientBackgroundColor = Color(0xfffaf9fd);
  static const Color dividerColor = Color.fromARGB(
    255,
    205,
    210,
    218,
  );
  static const cricleAnimationFillColor = Color.fromRGBO(66, 76, 89, 1);
  static const circleAnimationColor = Color.fromRGBO(255, 255, 255, 0.78);
  static Color menuButtonColor = Color.fromRGBO(255, 255, 255, 0.72);
}

//Color(0xFFCDD2DA)
