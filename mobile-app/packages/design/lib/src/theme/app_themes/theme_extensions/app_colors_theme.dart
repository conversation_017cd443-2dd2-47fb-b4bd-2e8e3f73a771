import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:theme_tailor_annotation/theme_tailor_annotation.dart';

part 'app_colors_theme.tailor.dart';

@TailorMixin(themeGetter: ThemeGetter.onThemeDataProps)
class AppColorsTheme extends ThemeExtension<AppColorsTheme>
    with DiagnosticableTreeMixin, _$AppColorsThemeTailorMixin {
  const AppColorsTheme({
    required this.appBarBackground,
    required this.appBarBackgroundGradient,
    required this.background,
    required this.backgroundGradient,
    required this.invalid,
    required this.lightBackground,
    required this.missing,
    required this.overlayBackground,
    required this.primary,
    required this.borderColor,
    required this.progressIndicator,
    required this.blueShadow,
    required this.secondary,
    required this.textPrimary,
    required this.textSecondary,
    required this.textWarning,
    required this.gradientBackgroundColor,
    required this.borderBackground,
    required this.dividerColor,
    required this.errorTag,
    required this.menuButtonColor,
    required this.circleAnimationColor,
    required this.cricleAnimationFillColor,
    required this.disabledButtonShadow,
    required this.brandSecondary,
    required this.infoBackground,
    required this.disabledButtonColor,
    required this.disabledButtonColorText,
  });

  @override
  final Color appBarBackground;
  @override
  final Color disabledButtonColor;
  @override
  final Color disabledButtonColorText;
  @override
  final LinearGradient appBarBackgroundGradient;
  @override
  final Color background;
  @override
  final LinearGradient backgroundGradient;
  @override
  final List<BoxShadow> disabledButtonShadow;
  @override
  final Color invalid;
  @override
  final Color errorTag;
  @override
  final Color lightBackground;
  @override
  final Color infoBackground;
  @override
  final Color missing;
  @override
  final Color overlayBackground;
  @override
  final Color primary;
  @override
  final Color blueShadow;
  @override
  final Color progressIndicator;
  @override
  final Color secondary;
  @override
  final Color borderBackground;
  @override
  final Color borderColor;
  @override
  final Color menuButtonColor;
  @override
  final Color dividerColor;
  @override
  final Color textPrimary;
  @override
  final Color textSecondary;
  @override
  final Color textWarning;
  @override
  final Color circleAnimationColor;
  @override
  final Color cricleAnimationFillColor;
  @override
  final Color gradientBackgroundColor;
  @override
  final Color brandSecondary;
}
