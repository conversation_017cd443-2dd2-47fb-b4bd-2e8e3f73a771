import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';

import '../../design.dart';

class TemplateStickyBottom extends StatefulWidget {
  const TemplateStickyBottom({
    super.key,
    required this.form,
    required this.button,
    required this.scrollController,
    this.paddingButtonValue,
  });
  final Widget form;
  final Widget button;
  final ScrollController scrollController;
  final EdgeInsetsGeometry? paddingButtonValue;

  @override
  State<TemplateStickyBottom> createState() => _TemplateStickyBottomState();
}

class _TemplateStickyBottomState extends State<TemplateStickyBottom> {
  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.bottomCenter,
      children: [
        Align(
          alignment: Alignment.topCenter,
          child: Padding(
            padding: EdgeInsets.only(
              bottom: formSubmitButtonContainerHeight.toDouble(),
            ),
            child: Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    physics: AlwaysScrollableScrollPhysics(),
                    controller: widget.scrollController,
                    child: Padding(
                      padding: paddingHorizontal24,
                      child: widget.form,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        widget.paddingButtonValue != null
            ? Padding(
                padding: widget.paddingButtonValue!,
                child: SizedBox(
                  height: formSubmitButtonContainerHeight.toDouble(),
                  child: widget.button,
                ),
              )
            : SizedBox(
                height: 40,
                child: widget.button,
              ),
      ],
    );
  }
}
