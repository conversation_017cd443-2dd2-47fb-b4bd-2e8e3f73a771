import 'dart:math' as math;

import 'package:flutter/material.dart';

class CircleTimer extends StatefulWidget {
  const CircleTimer({
    super.key,
    this.crcleAnimationController,
    required this.duration,
    required this.radius,
    this.borderColor,
    required this.circleColor,
    required this.fillColor,
    this.shadowColor = Colors.black,
    this.shadowElevation = 0.0,
    this.onDismissed,
  });

  final CircleAnimationController? crcleAnimationController;
  final Duration duration;
  final double radius;
  final Color circleColor;
  final Color fillColor;
  final Color shadowColor;
  final Color? borderColor;
  final double shadowElevation;
  final VoidCallback? onDismissed;

  @override
  State<CircleTimer> createState() => CircleTimerState();
}

class CircleTimerState extends State<CircleTimer>
    with SingleTickerProviderStateMixin {
  late CircleAnimationController _controller;
  late Animation<double> circleAnimation;

  @override
  void initState() {
    super.initState();

    _initAnimationController();
    _initAnims();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _initAnimationController() {
    if (widget.crcleAnimationController != null) {
      _controller =
          widget.crcleAnimationController as CircleAnimationController;
    } else {
      _controller = CircleAnimationController(
        vsync: this,
      );
    }

    _controller.duration = widget.duration;

    _controller.startAnim = () => _startAnim();
    _controller.stopAnim = () => _stopAnim();
    _controller.resetAnim = () => _resetAnim();
    _controller.addStatusListener((status) {});
  }

  void _initAnims() {
    circleAnimation = Tween<double>(begin: -math.pi / 2, end: (3 * math.pi) / 2)
        .animate(_controller)
      ..addListener(() {
        setState(() {});
      });
  }

  void _startAnim() {
    if (!_controller.isAnimating && mounted) {
      _controller.forward();
    }
  }

  void _stopAnim() {
    if (_controller.isAnimating && mounted) {
      _controller.stop();
    }
  }

  void _resetAnim() {
    if (_controller.isAnimating && mounted) {
      _controller.reset();

      _startAnim();
    }
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.radius * 2,
      height: widget.radius * 2,
      child: CustomPaint(
        painter: CirclePainter(
          radian: circleAnimation.value,
          radius: widget.radius,
          fillColor: widget.fillColor,
          borderColor: widget.borderColor,
          circleColor: widget.circleColor,
        ),
      ),
    );
  }
}

class CirclePainter extends CustomPainter {
  CirclePainter({
    required this.radian,
    required this.radius,
    required this.circleColor,
    required this.fillColor,
    this.borderColor,
  });

  final double radian;
  final double radius;
  final Color circleColor;
  final Color fillColor;
  final Color? borderColor;

  Path hitTestPath = Path();

  @override
  void paint(Canvas canvas, Size size) {
    final Offset center = Offset(size.width / 2, size.height / 2);

    var paint = Paint()
      ..strokeWidth = 3
      ..strokeCap = StrokeCap.butt
      ..style = PaintingStyle.fill;

    hitTestPath
      ..moveTo(center.dx, center.dy)
      ..addOval(Rect.fromCircle(center: center, radius: radius));

    drawBackgroundCirlce(canvas, center, paint);

    drawCircleProgress(canvas, center, paint);

    if (borderColor != null) {
      drawBorder(canvas, center, paint);
    }
  }

  void drawBackgroundCirlce(Canvas canvas, Offset center, Paint paint) {
    paint.color = fillColor;
    var backgroundPath = Path()
      ..moveTo(center.dx, center.dy)
      ..addOval(Rect.fromCircle(center: center, radius: radius));

    canvas.drawPath(backgroundPath, paint);
  }

  void drawCircleProgress(Canvas canvas, Offset center, Paint paint) {
    paint.color = circleColor;
    var circlePath = Path();

    circlePath.moveTo(center.dx, center.dy);
    circlePath.arcTo(
      Rect.fromCircle(center: center, radius: radius),
      -1.57,
      (radian + 1.57),
      false,
    );

    circlePath.close();
    canvas.drawPath(circlePath, paint);
  }

  void drawBorder(Canvas canvas, Offset center, Paint paint) {
    paint.color = borderColor!;
    paint.strokeWidth = 1;
    paint.style = PaintingStyle.stroke;
    canvas.drawCircle(center, radius - (1 / 2), paint);
  }

  @override
  bool shouldRepaint(covariant CirclePainter oldDelegate) =>
      this != oldDelegate;
}

class CircleAnimationController extends AnimationController {
  CircleAnimationController({required super.vsync});
  VoidCallback? startAnim;
  VoidCallback? stopAnim;
  VoidCallback? resetAnim;
}
