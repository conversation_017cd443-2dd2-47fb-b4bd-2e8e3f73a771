name: local_auth
description: Flutter plugin for Android and iOS devices to allow local
  authentication via fingerprint, touch ID, face ID, passcode, pin, or pattern.
repository: https://github.com/flutter/packages/tree/main/packages/local_auth/local_auth
issue_tracker: https://github.com/flutter/flutter/issues?q=is%3Aissue+is%3Aopen+label%3A%22p%3A+local_auth%22
version: 2.3.0
publish_to: 'none'

environment:
  sdk: ^3.1.5

flutter:
  plugin:
    platforms:
      android:
        default_package: local_auth_android
      ios:
        default_package: local_auth_darwin
      macos:
        default_package: local_auth_darwin
      windows:
        default_package: local_auth_windows

dependencies:
  flutter:
    sdk: flutter
  local_auth_android:
    path: ../local_auth_android
  local_auth_darwin: ^1.4.0
  local_auth_platform_interface: ^1.0.1
  local_auth_windows: ^1.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  mockito: ^5.4.4
  plugin_platform_interface: ^2.1.7

topics:
  - authentication
  - biometrics
  - local-auth
