<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="io.flutter.plugins.localauthexample">

    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.USE_BIOMETRIC"/>

    <application android:label="local_auth_example" android:icon="@mipmap/ic_launcher">
        <activity android:name="io.flutter.embedding.android.FlutterFragmentActivity"
                  android:launchMode="singleTop"
                  android:theme="@style/Theme.AppCompat.Light"
                  android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|layoutDirection"
                  android:hardwareAccelerated="true"
                  android:exported="true"
                  android:windowSoftInputMode="adjustResize">
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>
        <meta-data android:name="flutterEmbedding" android:value="2"/>
    </application>
</manifest>
