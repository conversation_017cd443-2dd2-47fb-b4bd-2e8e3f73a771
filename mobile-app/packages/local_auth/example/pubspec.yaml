name: local_auth_example
description: Demonstrates how to use the local_auth plugin.
publish_to: none

environment:
  sdk: ^3.1.5

dependencies:
  flutter:
    sdk: flutter
  local_auth:
    # When depending on this package from a real application you should use:
    #   local_auth: ^x.y.z
    # See https://dart.dev/tools/pub/dependencies#version-constraints
    # The example app is bundled with the plugin so we use a path dependency on
    # the parent directory to use the current plugin's version.
    path: ../
  local_auth_android: ^1.0.0
  local_auth_darwin: ^1.2.1

dependency_overrides:
  local_auth:
    path: ../
  local_auth_android:
    path: ../../local_auth_android

dev_dependencies:
  build_runner: ^2.1.10
  flutter_test:
    sdk: flutter
  integration_test:
    sdk: flutter

flutter:
  uses-material-design: true
