name: local_auth_android_example
description: Demonstrates how to use the local_auth_android plugin.
publish_to: none

environment:
  sdk: ^3.5.0
  flutter: ">=3.24.0"

dependencies:
  flutter:
    sdk: flutter
  local_auth_android:
    # When depending on this package from a real application you should use:
    #   local_auth_android: ^x.y.z
    # See https://dart.dev/tools/pub/dependencies#version-constraints
    # The example app is bundled with the plugin so we use a path dependency on
    # the parent directory to use the current plugin's version.
    path: ../
  local_auth_platform_interface: ^1.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  integration_test:
    sdk: flutter

flutter:
  uses-material-design: true
