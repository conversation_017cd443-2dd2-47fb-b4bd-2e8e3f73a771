import 'package:app_mobile/features/auth/domain/exceptions/auth_exception.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('AuthException', () {
    test('should store the message passed to the constructor', () {
      const message = 'An authentication error occurred';
      const exception = AuthException(message);

      expect(exception.message, equals(message));
    });

    test('toString should return the correct message', () {
      const message = 'An authentication error occurred';
      const exception = AuthException(message);

      expect(exception.toString(), equals(message));
    });
  });
}
