import 'package:app_mobile/dependency_injection/repositories.dart';
import 'package:app_mobile/features/auth/data/auth_repository_webview.dart';
import 'package:app_mobile/features/central_search/data/repositories/search_repository_impl.dart';
import 'package:app_mobile/features/settings/data/shared_preferences_settings_repository.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:http/http.dart' as http;
import 'package:mockito/mockito.dart';

class MockHttpClient extends Mock implements http.Client {}

class MockSettingsRepository extends Mock
    implements SharedPreferencesSettingsRepository {}

class MockAuthRepository extends Mock implements AuthRepositoryWebview {}

class MockSearchRepository extends Mock implements SearchRepositoryImpl {}

void main() {
  late ProviderContainer container;

  setUp(() {
    container = ProviderContainer();
  });

  tearDown(() {
    container.dispose();
  });

  group('HttpClient Provider', () {
    test('returns an instance of http.Client', () {
      final client = container.read(httpClientProvider);
      expect(client, isA<http.Client>());
    });
  });

  group('SettingsRepository Provider', () {
    test('returns an instance of SharedPreferencesSettingsRepository', () {
      final settingsRepo = container.read(settingsRepositoryProvider);
      expect(settingsRepo, isA<SharedPreferencesSettingsRepository>());
    });
  });

  group('AuthRepository Provider', () {
    test('returns an instance of AuthRepositoryWebview', () {
      final authRepo = container.read(authRepositoryProvider);
      expect(authRepo, isA<AuthRepositoryWebview>());
    });
  });

  group('SearchRepository Provider', () {
    test('returns an instance of SearchRepositoryImpl', () {
      final searchRepo = container.read(searchRepositoryProvider);
      expect(searchRepo, isA<SearchRepositoryImpl>());
    });
  });
}
