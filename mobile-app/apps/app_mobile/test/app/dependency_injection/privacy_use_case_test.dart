import 'package:app_mobile/dependency_injection/use_cases/privacy_use_case.dart';
import 'package:app_mobile/features/privacy/domain/privacy_use_case.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  late ProviderContainer container;

  setUp(() {
    container = ProviderContainer();
  });

  tearDown(() {
    container.dispose();
  });

  test('privacyUseCase should return an instance of PrivacyUseCase', () {
    final privacyUseCaseInstance = container.read(privacyUseCaseProvider);

    expect(privacyUseCaseInstance, isA<PrivacyUseCase>());
  });
}
