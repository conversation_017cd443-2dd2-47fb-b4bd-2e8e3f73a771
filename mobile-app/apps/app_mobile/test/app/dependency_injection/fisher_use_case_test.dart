import 'package:app_mobile/dependency_injection/repositories.dart';
import 'package:app_mobile/dependency_injection/use_cases/nfc_fisher_use_case.dart';
import 'package:app_mobile/features/auth/domain/auth_repository.dart';
import 'package:app_mobile/features/central_search/domain/search_repository.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';

class MockAuthRepository extends Mock implements AuthRepository {}

class MockSearchRepository extends Mock implements SearchRepository {}

void main() {
  late MockAuthRepository mockAuthRepository;
  late MockSearchRepository mockSearchRepository;

  setUp(() {
    mockAuthRepository = MockAuthRepository();
    mockSearchRepository = MockSearchRepository();
  });

  test('fisherUseCase returns correct FisherUseCase instance', () {
    final container = ProviderContainer(
      overrides: [
        authRepositoryProvider.overrideWithValue(mockAuthRepository),
        searchRepositoryProvider.overrideWithValue(mockSearchRepository),
      ],
    );

    final fisherUseCaseInstance = container.read(fisherUseCaseProvider);

    expect(fisherUseCaseInstance.logout, equals(mockAuthRepository.logout));
  });
}
