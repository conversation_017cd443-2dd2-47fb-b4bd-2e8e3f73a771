import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:meta/meta.dart';

import '../mock/mock_router.mocks.dart';
import 'test_utils.dart';

class TestSizeUtils {
  static setTestSize({required WidgetTester tester, Size? size}) {
    tester.view.physicalSize = size ?? kSizePhonePortrait;
    tester.view.devicePixelRatio = 1.0;

    addTearDown(tester.view.resetPhysicalSize);
    addTearDown(tester.view.resetDevicePixelRatio);
  }

  static const Size kSizeTestStandard = Size(800, 600);
  static const Size kSizePhonePortrait = Size(375, 812);
  static const Size kSizePhoneLandscape = Size(812, 375);
  static const Size kSizePhonePlusPortrait = Size(412, 869);
  static const Size kSizePhonePlusLandscape = Size(869, 412);
  static const Size kSizeTabletPortrait = Size(786, 1024);
  static const Size kSizeTabletLandscape = Size(1024, 768);
  static const Size kSizeBigTabletPortrait = Size(1024, 1366);
  static const Size kSizeBigTabletLandscape = Size(1366, 1024);
  static const Size kSize4kPortrait = Size(2160, 3840);
  static const Size kSize4kLandscape = Size(3840, 2160);
}

@isTest
void widgetTestGuidelines(
  String description, {
  required Widget widget,
  List<Override> overrides = const [],
  String? testOn,
  Timeout? timeout,
  dynamic skip,
  dynamic tags,
  Map<String, dynamic>? onPlatform,
  int? retry,
  ThemeData? theme,
  Size? size,
  Locale locale = const Locale('de', 'DE'),
}) {
  testWidgets(
    description,
    (WidgetTester tester) async {
      final SemanticsHandle handle = tester.ensureSemantics();
      await TestSizeUtils.setTestSize(
        tester: tester,
        size: size ?? TestSizeUtils.kSizePhonePortrait,
      );

      final router = MockGoRouter();

      await tester.pumpWidget(
        buildTestWidget(
          locale: locale,
          child: widget,
          overrides: overrides,
          router: router,
          theme: theme,
        ),
      );

      await tester.pumpAndSettle();

      // debugDumpSemanticsTree();
      await expectLater(tester, meetsGuideline(androidTapTargetGuideline));
      await expectLater(tester, meetsGuideline(iOSTapTargetGuideline));
      await expectLater(tester, meetsGuideline(labeledTapTargetGuideline));
      await expectLater(tester, meetsGuideline(textContrastGuideline));
      handle.dispose();
    },
    timeout: timeout,
    skip: skip,
    tags: tags,
    retry: retry,
  );
}

@isTest
void testProviderGuidelines(
  String description,
  Widget widget, {
  List<Override> overrides = const [],
  String? testOn,
  Timeout? timeout,
  dynamic skip,
  dynamic tags,
  Map<String, dynamic>? onPlatform,
  int? retry,
  ThemeData? theme,
  Size? size,
  Locale locale = const Locale('de', 'DE'),
}) {
  testWidgets(
    description,
    (WidgetTester tester) async {
      final SemanticsHandle handle = tester.ensureSemantics();
      await TestSizeUtils.setTestSize(
        tester: tester,
        size: size ?? TestSizeUtils.kSizePhonePortrait,
      );

      await tester.pumpWidget(
        buildTestWidget(
          locale: locale,
          child: widget,
          overrides: overrides,
          theme: theme,
        ),
      );

      await tester.pumpAndSettle();
      await expectLater(tester, meetsGuideline(androidTapTargetGuideline));
      await expectLater(tester, meetsGuideline(iOSTapTargetGuideline));
      await expectLater(tester, meetsGuideline(labeledTapTargetGuideline));
      await expectLater(tester, meetsGuideline(textContrastGuideline));
      handle.dispose();
    },
    timeout: timeout,
    skip: skip,
    tags: tags,
    retry: retry,
  );
}
