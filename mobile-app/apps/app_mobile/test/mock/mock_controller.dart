import 'package:app_mobile/features/agb/state/agb_controller_state.dart';
import 'package:app_mobile/features/auth/state/auth_state_controller.dart';
import 'package:app_mobile/features/barcode_scanner/state/scanner_control_state_controller.dart';
import 'package:app_mobile/features/imprint/state/imprint_controller_state.dart';
import 'package:app_mobile/features/privacy/state/privacy_control_state.dart';
import 'package:mockito/annotations.dart';

@GenerateNiceMocks([
  MockSpec<AuthController>(),
  MockSpec<ScannerControlStateController>(),
  MockSpec<AgbControllerState>(),
  MockSpec<ImprintControllerState>(),
  MockSpec<PrivacyConntrollerState>(),
])
export 'mock_controller.mocks.dart';
