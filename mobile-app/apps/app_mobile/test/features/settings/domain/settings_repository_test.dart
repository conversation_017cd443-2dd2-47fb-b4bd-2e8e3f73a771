import 'package:flutter_test/flutter_test.dart';

abstract interface class SettingsRepository {
  Future<bool> setUseBiometrics(bool useBiometrics);
  Future<bool?> getUseBiometrics();
  void resetUseBiometrics();
}

class InMemorySettingsRepository implements SettingsRepository {
  bool _useBiometrics = false;

  @override
  Future<bool> setUseBiometrics(bool useBiometrics) async {
    _useBiometrics = useBiometrics;
    return true;
  }

  @override
  Future<bool?> getUseBiometrics() async {
    return _useBiometrics;
  }

  @override
  void resetUseBiometrics() {
    _useBiometrics = false;
  }
}

void main() {
  group('InMemorySettingsRepository', () {
    late InMemorySettingsRepository settingsRepository;

    setUp(() {
      settingsRepository = InMemorySettingsRepository();
    });

    test('setUseBiometrics sets the value correctly', () async {
      await settingsRepository.setUseBiometrics(true);
      expect(await settingsRepository.getUseBiometrics(), true);

      await settingsRepository.setUseBiometrics(false);
      expect(await settingsRepository.getUseBiometrics(), false);
    });

    test('getUseBiometrics returns false before setting', () async {
      expect(await settingsRepository.getUseBiometrics(), false);
    });

    test('resetUseBiometrics resets the value', () async {
      await settingsRepository.setUseBiometrics(true);
      expect(await settingsRepository.getUseBiometrics(), true);

      settingsRepository.resetUseBiometrics();

      expect(await settingsRepository.getUseBiometrics(), false);
    });

    test('resetUseBiometrics sets the value to false', () async {
      await settingsRepository.setUseBiometrics(true);
      expect(await settingsRepository.getUseBiometrics(), true);

      settingsRepository.resetUseBiometrics();

      expect(await settingsRepository.getUseBiometrics(), false);
    });

    test('resetUseBiometrics multiple times has no side effects', () async {
      await settingsRepository.setUseBiometrics(true);
      expect(await settingsRepository.getUseBiometrics(), true);

      settingsRepository.resetUseBiometrics();
      expect(await settingsRepository.getUseBiometrics(), false);

      settingsRepository.resetUseBiometrics();
      expect(await settingsRepository.getUseBiometrics(), false);
    });
  });
}
