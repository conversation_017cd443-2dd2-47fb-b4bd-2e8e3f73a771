import 'package:app_mobile/features/settings/data/shared_preferences_settings_repository.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';

void main() {
  group('SharedPreferencesSettingsRepository', () {
    late SharedPreferencesSettingsRepository settingsRepository;

    setUp(() {
      settingsRepository = SharedPreferencesSettingsRepository();
      SharedPreferences.setMockInitialValues({});
    });

    test('should reset and retrieve use biometrics', () async {
      await settingsRepository.setUseBiometrics(true);
      var useBiometrics = await settingsRepository.getUseBiometrics();
      expect(useBiometrics, true);

      await settingsRepository.resetUseBiometrics();
      useBiometrics = await settingsRepository.getUseBiometrics();
      expect(useBiometrics, isNull);
    });

    test('should save and retrieve use biometrics', () async {
      await settingsRepository.setUseBiometrics(true);
      final useBiometrics = await settingsRepository.getUseBiometrics();
      expect(useBiometrics, true);
    });
  });
}
