import 'dart:io';

import 'package:app_mobile/features/auth/data/auth_repository_webview.dart';
import 'package:app_mobile/features/auth/domain/errors/token_extension_error.dart';
import 'package:app_mobile/features/secure_storage/constants.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:http/http.dart' as http;
import 'package:mockito/mockito.dart';

import '../../mock/mock_client.dart';
import '../../mock/mock_flutter_app_auth.dart';

void main() {
  group('AuthRepositoryWebview', () {
    late AuthRepositoryWebview authRepository;
    late MockFlutterAppAuth mockFlutterAppAuth;
    late MockClient mockHttpClient;

    setUp(() {
      FlutterSecureStorage.setMockInitialValues({
        storageKeyAccessToken: 'mockAccessToken',
      });
      mockFlutterAppAuth = MockFlutterAppAuth();
      mockHttpClient = MockClient();
      authRepository = AuthRepositoryWebview.test(
        mockHttpClient,
        mockFlutterAppAuth,
      );
    });

    test('getAccessToken returns access token from storage', () async {
      final token = await authRepository.getAccessToken();

      expect(token, 'mockAccessToken');
    });

    test('getWithInterceptor returns response when status 200', () async {
      when(mockHttpClient.get(any, headers: anyNamed('headers')))
          .thenAnswer((_) async => http.Response('Success', 200));

      final response = await authRepository.getWithInterceptor(
        'https://example.com',
        '/path',
        {'param': 'value'},
        0,
      );

      expect(response.statusCode, 200);
    });

    test('getWithInterceptor retries on 401 and refreshes token', () async {
      when(mockHttpClient.get(any, headers: anyNamed('headers')))
          // .thenAnswer((_) async => http.Response('Unauthorized', 401))
          .thenAnswer((_) async => http.Response('Success', 200));

      final response = await authRepository.getWithInterceptor(
        'https://example.com',
        '/path',
        {'param': 'value'},
        1,
      );

      expect(response.statusCode, 200);
    });

    test('getWithInterceptor throws HttpException on 404 response', () async {
      when(mockHttpClient.get(any, headers: anyNamed('headers')))
          .thenAnswer((_) async => http.Response('Not Found', 404));

      expect(
        () async => await authRepository.getWithInterceptor(
          'https://example.com',
          '/path',
          {'param': 'value'},
          0,
        ),
        throwsA(
          isA<HttpException>()
              .having((e) => 'Unauthorized', 'message', 'Unauthorized'),
        ),
      );
    });

    test('getWithInterceptor throws HttpException on 400 response', () async {
      when(mockHttpClient.get(any, headers: anyNamed('headers')))
          .thenAnswer((_) async => http.Response('Not Found', 404));

      expect(
        () async => await authRepository.getWithInterceptor(
          'https://example.com',
          '/path',
          {'param': 'value'},
          0,
        ),
        throwsA(
          isA<HttpException>()
              .having((e) => 'invalid syntax', 'message', 'invalid syntax'),
        ),
      );
    });

    test('getWithInterceptor throws TokenExtensionError on 403 response',
        () async {
      when(mockHttpClient.get(any, headers: anyNamed('headers')))
          .thenAnswer((_) async => http.Response('Forbidden', 403));

      expect(
        () async => await authRepository.getWithInterceptor(
          'https://example.com',
          '/path',
          {'param': 'value'},
          0,
        ),
        throwsA(
          isA<TokenExtensionError>().having(
            (e) => 'authenticate failed, forbidden',
            'message',
            'authenticate failed, forbidden',
          ),
        ),
      );
    });

    test('getWithInterceptor throws TokenExtensionError on 500 response',
        () async {
      when(mockHttpClient.get(any, headers: anyNamed('headers')))
          .thenAnswer((_) async => http.Response('Internal Server Error', 500));

      expect(
        () async => await authRepository.getWithInterceptor(
          'https://example.com',
          '/path',
          {'param': 'value'},
          0,
        ),
        throwsA(
          isA<TokenExtensionError>().having(
            (e) => 'Internal Server Error',
            'message',
            'Internal Server Error',
          ),
        ),
      );
    });
  });
}
