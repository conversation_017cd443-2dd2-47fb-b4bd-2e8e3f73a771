import 'package:app_mobile/features/auth/state/auth_state.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('AuthState', () {
    test('should have default values', () {
      final state = AuthState();

      expect(state.isAuthenticated, isFalse);
    });

    test('should be able to create an instance with given values', () {
      final state = AuthState(isAuthenticated: true);

      expect(state.isAuthenticated, isTrue);
    });

    test('should be able to serialize to JSON', () {
      final state = AuthState(isAuthenticated: true);

      final json = state.toJson();

      expect(json['isAuthenticated'], true);
    });

    test('should be able to deserialize from JSON', () {
      final json = {
        'isAuthenticated': true,
      };

      final state = AuthState.fromJson(json);

      expect(state.isAuthenticated, true);
    });

    test('should support copyWith method', () {
      final state = AuthState(isAuthenticated: false);
      final newState = state.copyWith(isAuthenticated: true);

      expect(newState.isAuthenticated, isTrue);
      expect(
        state.isAuthenticated,
        isFalse,
      ); // Ensure the original state hasn't changed
    });
  });
}
