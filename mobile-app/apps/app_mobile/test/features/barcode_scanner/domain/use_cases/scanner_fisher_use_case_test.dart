import 'package:app_mobile/features/central_search/domain/entities/license_status_enum.dart';
import 'package:app_mobile/features/central_search/domain/entities/search_parameters.dart';
import 'package:app_mobile/features/central_search/domain/entities/search_result.dart';
import 'package:app_mobile/features/central_search/domain/search_repository.dart';
import 'package:app_mobile/features/nfc_scanner/domain/use_cases/nfc_fisher_use_case.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

import 'scanner_fisher_use_case_test.mocks.dart';

@GenerateMocks([SearchRepository])
void main() {
  group('FisherUseCase', () {
    late FisherUseCase useCase;
    late MockSearchRepository mockRepository;

    setUp(() {
      mockRepository = MockSearchRepository();
      useCase = FisherUseCase(searchRepository: mockRepository, logout: () {});
    });

    test('should return SearchResult when the repository call is successful',
        () async {
      final searchParameters =
          SearchParameters(identificationDocumentId: '1', hash: '1');
      final searchResult = SearchResult(
        licenseNumber: 'SH12151515151515',
        licenseType: '1',
        licenseStatus: LicenseStatus.valid,
        taxValid: true,
        licenseNote: '',
        taxNote: '',
        givenNames: '',
        surname: '',
        birthdate: '',
      );
      when(mockRepository.searchRequest(any))
          .thenAnswer((_) async => searchResult);

      final result = await useCase.call(searchParameters);

      expect(result, searchResult);
      verify(mockRepository.searchRequest(searchParameters)).called(1);
    });

    test('should return null when the repository call returns null', () async {
      final searchParameters =
          SearchParameters(identificationDocumentId: '', hash: '');
      when(mockRepository.searchRequest(any)).thenAnswer((_) async => null);

      final result = await useCase.call(searchParameters);

      expect(result, null);
      verify(mockRepository.searchRequest(searchParameters)).called(1);
    });

    test(
        'should throw an exception when the repository call throws an exception',
        () async {
      final searchParameters =
          SearchParameters(identificationDocumentId: '', hash: '');
      when(mockRepository.searchRequest(any)).thenThrow(Exception('Error'));

      expect(() => useCase.call(searchParameters), throwsException);
      verify(mockRepository.searchRequest(searchParameters)).called(1);
    });
  });
}
