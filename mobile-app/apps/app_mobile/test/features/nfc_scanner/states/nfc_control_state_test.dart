import 'package:app_mobile/features/central_search/domain/entities/license_status_enum.dart';
import 'package:app_mobile/features/central_search/domain/entities/search_parameters.dart';
import 'package:app_mobile/features/central_search/domain/entities/search_result.dart';
import 'package:app_mobile/features/nfc_scanner/state/nfc_control_state.dart';
import 'package:app_mobile/features/nfc_scanner/state/nfc_state_enum.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('NfcControllerState', () {
    test('should have null default values', () {
      const state = NfcControlState();

      expect(state.nfcSearchParameters, isNull);
      expect(state.nfcResults, isNull);
      expect(state.nfcState, isNull);
      expect(state.error, isNull);
    });

    test('should create a state with provided values', () {
      final searchParams = SearchParameters(
        identificationDocumentId: '1',
        hash: 'abc',
      );
      final searchResults = SearchResult(
        licenseNumber: 'SH12151515151515',
        licenseType: 'regular',
        licenseStatus: LicenseStatus.valid,
        licenseNote: '',
        taxValid: true,
        taxNote: '',
        givenNames: 'Test givenName',
        surname: 'Test Surname',
        birthdate: '1.1.2001',
      );
      const scanState = NfcStateEnum.idle;
      final error = Exception('An error occurred');

      final state = NfcControlState(
        nfcSearchParameters: searchParams,
        nfcResults: searchResults,
        nfcState: scanState,
        error: error,
      );

      expect(state.nfcSearchParameters, equals(searchParams));
      expect(state.nfcResults, equals(searchResults));
      expect(state.nfcState, equals(scanState));
      expect(state.error, equals(error));
    });

    test('should support copyWith method', () {
      final initialState = NfcControlState(
        nfcSearchParameters: SearchParameters(
          identificationDocumentId: '1',
          hash: 'abc',
        ),
        nfcResults: SearchResult(
          licenseNumber: 'SH12151515151515',
          licenseType: 'regular',
          licenseStatus: LicenseStatus.valid,
          licenseNote: '',
          taxValid: true,
          taxNote: '',
          givenNames: 'Test givenName',
          surname: 'Test Surname',
          birthdate: '1.1.2001',
        ),
        nfcState: NfcStateEnum.idle,
        error: Exception('An error occurred'),
      );

      final newSearchParams = SearchParameters(
        identificationDocumentId: '1',
        hash: 'abc',
      );
      final newState = initialState.copyWith(
        nfcSearchParameters: newSearchParams,
        nfcState: NfcStateEnum.success,
        error: null,
      );

      expect(newState.nfcSearchParameters, equals(newSearchParams));
      expect(newState.nfcResults, equals(initialState.nfcResults));
      expect(newState.nfcState, equals(NfcStateEnum.success));
      expect(newState.error, isNull);
    });
  });
}
