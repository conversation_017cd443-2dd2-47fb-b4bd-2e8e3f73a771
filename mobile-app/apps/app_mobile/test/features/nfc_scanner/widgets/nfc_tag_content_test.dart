import 'package:app_mobile/features/barcode_scanner/widgets/scanner_result_content.dart';
import 'package:app_mobile/features/central_search/domain/entities/license_status_enum.dart';
import 'package:app_mobile/features/central_search/domain/entities/search_parameters.dart';
import 'package:app_mobile/features/central_search/domain/entities/search_result.dart';
import 'package:app_mobile/features/central_search/widgets/scan_not_connected.dart';
import 'package:app_mobile/features/central_search/widgets/scan_not_found.dart';
import 'package:app_mobile/features/nfc_scanner/state/nfc_control_state.dart';
import 'package:app_mobile/features/nfc_scanner/state/nfc_state_enum.dart';
import 'package:app_mobile/features/nfc_scanner/widgets/nfc_fail.dart';
import 'package:app_mobile/features/nfc_scanner/widgets/nfc_tag_content.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../utils/test_utils.dart';

void main() {
  group('NfcTag WidgetTest', () {
    testWidgets('NfcTagContent shows ScannerResultContent on success',
        (WidgetTester tester) async {
      final nfcControlState = NfcControlState(
        nfcSearchParameters:
            SearchParameters(identificationDocumentId: '1', hash: '45'),
        nfcResults: SearchResult(
          licenseNumber: 'licenseNumber',
          licenseType: 'licenseType',
          licenseStatus: LicenseStatus.valid,
          licenseNote: 'licenseNote',
          taxValid: true,
          taxNote: 'taxNote',
          givenNames: 'givenNames',
          surname: 'surname',
          birthdate: '1.1.2001',
        ),
        nfcState: NfcStateEnum.success,
      );

      final asyncValue = AsyncData<NfcControlState>(nfcControlState);

      await tester.pumpWidget(
        buildTestWidget(
          child: NfcTagContent(asyncValue),
          overrides: [],
        ),
      );

      expect(find.byType(ScannerResultContent), findsOneWidget);
    });

    testWidgets('NfcTagContent shows ScanNotFound on notFound state',
        (WidgetTester tester) async {
      final nfcControlState = NfcControlState(
        nfcSearchParameters:
            SearchParameters(identificationDocumentId: '1', hash: '45'),
        nfcResults: null,
        nfcState: NfcStateEnum.notFound,
      );

      final asyncValue = AsyncData<NfcControlState>(nfcControlState);

      await tester.pumpWidget(
        buildTestWidget(
          child: NfcTagContent(asyncValue),
          overrides: [],
        ),
      );

      expect(find.byType(ScanNotFound), findsOneWidget);
    });

    testWidgets('NfcTagContent shows ScanFail on fail state',
        (WidgetTester tester) async {
      final nfcControlState = NfcControlState(
        nfcSearchParameters:
            SearchParameters(identificationDocumentId: '1', hash: '45'),
        nfcResults: null,
        nfcState: NfcStateEnum.fail,
      );

      final asyncValue = AsyncData<NfcControlState>(nfcControlState);

      await tester.pumpWidget(
        buildTestWidget(
          child: NfcTagContent(
            asyncValue,
            width: 0,
          ),
          overrides: [],
        ),
      );

      expect(find.byType(NfcFail), findsOneWidget);
    });

    testWidgets(
        'NfcTagContent shows ScanNotConnected on scanNotConnected state',
        (WidgetTester tester) async {
      final nfcControlState = NfcControlState(
        nfcSearchParameters:
            SearchParameters(identificationDocumentId: '1', hash: '45'),
        nfcResults: null,
        nfcState: NfcStateEnum.scanNotConnected,
      );

      final asyncValue = AsyncData<NfcControlState>(nfcControlState);

      await tester.pumpWidget(
        buildTestWidget(
          child: NfcTagContent(
            asyncValue,
            width: 0,
          ),
          overrides: [],
        ),
      );

      expect(find.byType(ScanNotConnected), findsOneWidget);
    });
  });
}
