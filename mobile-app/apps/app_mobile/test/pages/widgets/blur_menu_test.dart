import 'package:app_mobile/app/pages/widgets/menu_icon/blur_menu.dart';
import 'package:app_mobile/app/pages/widgets/menu_icon/blur_menu_item.dart';
import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../utils/test_utils.dart';

void main() {
  testWidgets('BlurMenu displays the correct number of items',
      (WidgetTester tester) async {
    final autoSizeGroup = AutoSizeGroup();

    final items = List.generate(
      5,
      (index) => BlurMenuItem(
        'Item $index',
        semanticLabel: 'Semantic Label $index',
        asset: 'assets/images/icons/result-type-negative.png',
        autoSizeGroup: autoSizeGroup,
        itemPressed: () => {},
      ),
    );

    await tester.pumpWidget(
      buildTestWidget(
        child: BlurMenu(
          items: items,
          width: 200,
          itemHeight: 50,
          isLoggedIn: false,
        ),
        overrides: [],
      ),
    );

    expect(find.byType(ListTile), findsNWidgets(items.length));
  });
}
