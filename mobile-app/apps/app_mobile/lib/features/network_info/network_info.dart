import 'dart:ui';

import 'package:design/design.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../app/app_index.dart';
import 'state/network_info_state.dart';
import 'state/network_info_state_controller.dart';

class NetworkInfo extends ConsumerWidget {
  const NetworkInfo({super.key, required this.app});

  final Widget app;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final NetworkInfoState networkInfoState =
        ref.watch(networkInfoStateControllerProvider);

    return Stack(
      children: [
        IgnorePointer(
          ignoring: networkInfoState.displayNetworkInfo,
          child: app,
        ),
        if (networkInfoState.displayNetworkInfo)
          BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 2, sigmaY: 2),
            child: Dialog(
              shadowColor: Colors.black,
              elevation: 10,
              child: Stack(
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 20,
                      vertical: 40,
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          context.strings.networkInfoTitle,
                          style: context.theme.networkInfoheader,
                        ),
                        spacingVertical16,
                        ErrorTag(
                          errorTagTitle: context.strings.networkInfoErrorTag,
                        ),
                        spacingVertical16,
                        Text(
                          context.strings.networkInfoHint,
                          style: StyleGuideTextStyles.body,
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                  Align(
                    heightFactor: 1,
                    alignment: Alignment.topRight,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                        vertical: 8,
                        horizontal: 12,
                      ),
                      child: IconButton(
                        iconSize: 28,
                        onPressed: () {
                          ref
                              .read(networkInfoStateControllerProvider.notifier)
                              .dismiss();
                        },
                        icon: const Icon(Icons.close),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }
}
