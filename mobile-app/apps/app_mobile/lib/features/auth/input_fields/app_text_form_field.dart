import 'package:design/design.dart';
import 'package:flutter/material.dart';

import '../../../app/app_index.dart';

class AppTextFormField extends StatefulWidget {
  const AppTextFormField({
    super.key,
    this.controller,
    this.focusNode,
    this.onSaved,
    this.onFieldSubmitted,
    this.onEditingComplete,
    this.textInputAction,
    this.hintText,
    this.suffixIcon,
    this.keyboardType,
    this.autofillHints,
    this.autocorrect = true,
    this.validator,
    this.initialValue,
    required this.semanticLabel,
    this.obscureText = false,
  });

  final TextEditingController? controller;
  final FocusNode? focusNode;
  final Function(String?)? onSaved;
  final Function(String)? onFieldSubmitted;
  final Function()? onEditingComplete;
  final TextInputAction? textInputAction;
  final String? hintText;
  final Widget? suffixIcon;
  final TextInputType? keyboardType;
  final Iterable<String>? autofillHints;
  final bool autocorrect;
  final String? Function(String?)? validator;
  final bool obscureText;
  final String semanticLabel;
  final String? initialValue;

  @override
  State<AppTextFormField> createState() => _AppTextFormFieldState();
}

class _AppTextFormFieldState extends State<AppTextFormField> {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  bool isButtonEnabled = false;

  bool isEmpty() {
    setState(() {
      isButtonEnabled = _controller.text.isNotEmpty;
    });
    return isButtonEnabled;
  }

  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? TextEditingController();
    _controller.text = widget.initialValue ?? '';
    _focusNode = widget.focusNode ?? FocusNode();
  }

  @override
  void dispose() {
    // Dispose the controller/focusNode if they were created in this widget. Otherwise, it is the responsibility of the parent widget to dispose them.
    if (widget.controller == null) {
      _controller.dispose();
    }
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Expanded(
          child: TextFormField(
            onChanged: (val) {
              isEmpty();
            },
            controller: _controller,
            focusNode: _focusNode,
            decoration: InputDecoration(
              alignLabelWithHint: false,
              hintText: widget.hintText,
              label: widget.hintText != null ? Text(widget.hintText!) : null,
            ),
            keyboardType: widget.keyboardType,
            autofillHints: widget.autofillHints,
            autocorrect: widget.autocorrect,
            validator: widget.validator,
            onSaved: widget.onSaved,
            textInputAction: widget.textInputAction,
            onFieldSubmitted: widget.onFieldSubmitted,
            obscureText: widget.obscureText,
            onEditingComplete: widget.onEditingComplete,
          ),
        ),
        widget.suffixIcon ??
            ClearButton(
              key: ValueKey(_controller.text.isNotEmpty),
              isButtonEnabled: isButtonEnabled,
              onPressed: (isButtonEnabled)
                  ? () {
                      _controller.clear();
                      setState(() {
                        isButtonEnabled = false;
                      });
                    }
                  : null,
            ),
      ],
    );
  }
}

class ClearButton extends StatefulWidget {
  const ClearButton({
    super.key,
    required this.onPressed,
    required this.isButtonEnabled,
  });
  final Function()? onPressed;
  final bool isButtonEnabled;

  @override
  State<ClearButton> createState() => _ClearButtonState();
}

class _ClearButtonState extends State<ClearButton> {
  @override
  Widget build(BuildContext context) {
    return AppIconButton(
      key: ValueKey(widget.isButtonEnabled),
      icon: Icon(AdaptiveIcons.clear, size: 20.0),
      tooltip: (widget.isButtonEnabled)
          ? context.strings.formFieldClearButtonTooltip
          : '',
      onPressed: widget.onPressed,
    );
  }
}
