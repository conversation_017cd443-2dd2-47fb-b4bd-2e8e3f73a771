// ignore_for_file: use_rethrow_when_possible

import 'dart:convert';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter_appauth/flutter_appauth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:http/http.dart' as http;
import 'package:http/http.dart';

import '../../../app/config/config.dart';
import '../../secure_storage/constants.dart';
import '../../secure_storage/secure_storage_service.dart';
import '../domain/auth_repository.dart';
import '../domain/errors/token_extension_error.dart';
import '../domain/exceptions/auth_exception.dart';

typedef HttpClientFactory = http.Client Function();

class AuthRepositoryKeycloak implements AuthRepository {
  AuthRepositoryKeycloak()
      : _client = http.Client(),
        _appAuth = const FlutterAppAuth();

  @visibleForTesting
  AuthRepositoryKeycloak.test(
    final http.Client client,
    FlutterAppAuth? appAuth,
  )   : _client = client,
        _appAuth = appAuth ?? const FlutterAppAuth();
  final FlutterAppAuth _appAuth;
  final http.Client _client;

  Future<String?> getAccessToken() {
    return _getStoredAccessToken();
  }

  // handle retry if access token is not valid anymore and need to refresh
  Future<http.Response> getWithInterceptor(
    String baseUrl,
    String path,
    Map<String, dynamic> queryParameters,
    int retryCount,
  ) async {
    final String? accessToken = await getAccessToken();
    final String queryString = Uri(queryParameters: queryParameters).query;
    final String requestUrl = '$baseUrl$path?$queryString';

    final response = await _client.get(
      Uri.parse(requestUrl),
      headers: {
        'Authorization': 'Bearer $accessToken',
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    );

    if (response.statusCode == 200) {
      return response;
    } else if (response.statusCode == 401) {
      if (retryCount > 0) {
        throw TokenExtensionError(
          'Unable to refresh token, retry count exceeded',
        );
      }
      final String? newAccessToken = await _refreshToken();
      if (newAccessToken != null) {
        return getWithInterceptor(baseUrl, path, queryParameters, 1);
      } else {
        throw TokenExtensionError('no access token');
      }
    } else if (response.statusCode == 403) {
      throw TokenExtensionError('authenticate failed, forbidden');
    } else {
      throw TokenExtensionError(response.reasonPhrase);
    }
  }

  @override
  Future<void> login([String? username, String? password]) async {
    try {
      const String authUrl = '${Config.baseUrl}/${Config.tokenUrl}';

      final Response response = await _client.post(
        Uri.parse(authUrl),
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': '*/*',
        },
        body: {
          'client_id': Config.clientId,
          'username': username,
          'password': password,
          'redirect_uri': Config.redirectUrl,
          'client_secret': Config.clientSecret,
          'scope': 'openid',
          'response_type': 'code',
          'grant_type': 'password',
        },
      );

      Map<String, dynamic> responseJson = jsonDecode(response.body);
      DateTime accessTokenExpirationDateTime =
          DateTime.now().add(const Duration(seconds: 300));
      if (responseJson['expires_in'] != null) {
        accessTokenExpirationDateTime =
            DateTime.now().add(Duration(seconds: responseJson['expires_in']));
      }
      if (responseJson['error'] != null) {
        throw AuthException(responseJson['error']);
      }

      await _saveTokenRequestResponse(
        AuthorizationTokenResponse(
          responseJson['access_token'],
          responseJson['refresh_token'],
          accessTokenExpirationDateTime,
          responseJson['id_token'],
          responseJson['token_type'],
          responseJson['scope'].split(' '),
          null,
          null,
        ),
      );
    } catch (e) {
      if (e is AuthException) {
        throw AuthException(e.message);
      } else {
        throw const AuthException('AuthException');
      }
    }
  }

  @override
  Future<void> logout(Ref ref) async {
    const String authUrl = '${Config.baseUrl}/${Config.revokeURL}';

    await for (final String? token in Stream.fromFutures(
      [_getStoredAccessToken(), _getStoredRefreshToken()],
    )) {
      if (token == null) {
        continue;
      }

      _client.post(
        Uri.parse(authUrl),
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': '*/*',
        },
        body: {
          'client_id': Config.clientId,
          'redirect_uri': Config.redirectUrl,
          'token': token,
        },
      ).ignore();
    }

    await Future.wait([
      SecureStorageService.removeData(storageKeyRefreshToken),
      SecureStorageService.removeData(storageKeyAccessToken),
      SecureStorageService.removeData(storageKeyAccessTokenExpirationDateTime),
    ]);
  }

  // refresh Login
  // if token still valid, refresh
  // if not, try to get a new auth token With refresh token
  @override
  Future<void> refreshLogin() async {
    try {
      final DateTime? accessTokenExpiration =
          await _getAccessTokenExpirationDateTime();

      if (accessTokenExpiration == null) {
        throw Exception('Kein Ablaufdatum für Accesstoken');
      }

      if (DateTime.now().compareTo(accessTokenExpiration) != -1) {
        await _refreshToken();
      }
    } catch (e) {
      throw Exception('Anmeldung fehlgeschlagen (Refresh)');
    }
  }

  // if auth token invalidated, get a new one with the refresh token
  Future<String?> _refreshToken() async {
    final TokenResponse? tokenRequestResponse;

    String? storedRefreshToken = await _getStoredRefreshToken();

    if (storedRefreshToken != null) {
      try {
        tokenRequestResponse = await _appAuth.token(
          TokenRequest(
            clientSecret: Config.clientSecret,
            Config.clientId,
            Config.redirectUrl,
            discoveryUrl: '${Config.baseUrl}/${Config.discoveryUrl}',
            refreshToken: storedRefreshToken,
            scopes: ['openid'],
          ),
        );

        if (tokenRequestResponse != null) {
          await _saveTokenRequestResponse(tokenRequestResponse);
          return tokenRequestResponse.accessToken;
        }
      } on SocketException {
        throw const SocketException('No Internet connection (SocketException)');
      }
    }
    throw const HttpException('Unauthorized');
  }

  Future<void> _saveTokenRequestResponse(
    TokenResponse tokenRequestResponse,
  ) async {
    final String? refreshToken = tokenRequestResponse.refreshToken;
    final String? accessToken = tokenRequestResponse.accessToken;
    final DateTime? accessTokenExpirationDateTime =
        tokenRequestResponse.accessTokenExpirationDateTime;

    await Future.wait([
      _saveRefreshToken(refreshToken),
      _saveAccessToken(accessToken),
      _saveAccessTokenExpirationDateTime(accessTokenExpirationDateTime),
    ]);
  }

  Future<String?> _getStoredRefreshToken() {
    return SecureStorageService.loadData(storageKeyRefreshToken);
  }

  Future<String?> _getStoredAccessToken() {
    return SecureStorageService.loadData(storageKeyAccessToken);
  }

  Future<void> _saveAccessToken(String? accessToken) async {
    if (accessToken != null) {
      await SecureStorageService.saveData(storageKeyAccessToken, accessToken);
    }
  }

  Future<DateTime?> _getAccessTokenExpirationDateTime() async {
    final String? storedValue = await SecureStorageService.loadData(
      storageKeyAccessTokenExpirationDateTime,
    );

    if (storedValue != null) {
      return DateTime.parse(storedValue);
    }

    return null;
  }

  Future<void> _saveAccessTokenExpirationDateTime(
    DateTime? accessTokenExpirationDateTime,
  ) async {
    if (accessTokenExpirationDateTime != null) {
      await SecureStorageService.saveData(
        storageKeyAccessTokenExpirationDateTime,
        accessTokenExpirationDateTime.toString(),
      );
    }
  }

  Future<void> _saveRefreshToken(String? refreshToken) async {
    if (refreshToken != null) {
      await SecureStorageService.saveData(storageKeyRefreshToken, refreshToken);
    }
  }
}
