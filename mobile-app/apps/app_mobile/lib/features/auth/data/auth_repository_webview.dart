// ignore_for_file: use_rethrow_when_possible

import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter_appauth/flutter_appauth.dart';
import 'package:http/http.dart' as http;

import '../../../app/config/config.dart';
import '../../../dependency_injection/repositories.dart';
import '../../automatic_logout/providers/timer_repository_provider.dart';
import '../../secure_storage/constants.dart';
import '../../secure_storage/secure_storage_service.dart';
import '../domain/auth_repository.dart';
import '../domain/errors/token_extension_error.dart';
import '../domain/exceptions/auth_exception.dart';

typedef HttpClientFactory = http.Client Function();

class AuthRepositoryWebview implements AuthRepository {
  AuthRepositoryWebview()
      : _client = http.Client(),
        _appAuth = const FlutterAppAuth();

  @visibleForTesting
  AuthRepositoryWebview.test(
    final http.Client client,
    FlutterAppAuth? appAuth,
  )   : _client = client,
        _appAuth = appAuth ?? const FlutterAppAuth();

  final FlutterAppAuth _appAuth;
  final http.Client _client;

  Future<String?> getAccessToken() async {
    return await _getStoredAccessToken();
  }

  // handle retry if access token is not valid anymore and need to refresh
  Future<http.Response> getWithInterceptor(
    String baseUrl,
    String path,
    Map<String, dynamic> queryParameters,
    int retryCount,
  ) async {
    final String? accessToken = await getAccessToken();
    final String queryString = Uri(queryParameters: queryParameters).query;
    final String requestUrl = '$baseUrl$path?$queryString';
    final response = await _client.get(
      Uri.parse(requestUrl),
      headers: {
        'Authorization': 'Bearer $accessToken',
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    );

    if (response.statusCode == 200) {
      return response;
    } else if (response.statusCode == 401) {
      if (retryCount > 0) {
        await Future.wait([
          SecureStorageService.removeData(storageKeyRefreshToken),
          SecureStorageService.removeData(storageKeyAccessToken),
          SecureStorageService.removeData(
            storageKeyAccessTokenExpirationDateTime,
          ),
        ]);
        throw TokenExtensionError(
          'Unable to refresh token, retry count exceeded',
        );
      }
      final String? newAccessToken = await _refreshToken();
      if (newAccessToken != null) {
        return getWithInterceptor(baseUrl, path, queryParameters, 1);
      } else {
        await Future.wait([
          SecureStorageService.removeData(storageKeyRefreshToken),
          SecureStorageService.removeData(storageKeyAccessToken),
          SecureStorageService.removeData(
            storageKeyAccessTokenExpirationDateTime,
          ),
        ]);

        throw TokenExtensionError('no access token');
      }
    } else if (response.statusCode == 403) {
      throw TokenExtensionError('authenticate failed, forbidden');
    } else if (response.statusCode == 404) {
      throw HttpException('not found');
    } else if (response.statusCode == 400) {
      throw HttpException('invalid syntax');
    } else {
      throw TokenExtensionError(response.reasonPhrase);
    }
  }

  @override
  Future<void> login([String? username, String? password]) async {
    try {
      AuthorizationTokenResponse? authenticateResult =
          await _appAuth.authorizeAndExchangeCode(
        AuthorizationTokenRequest(
          clientSecret: Config.clientSecret,
          Config.clientId,
          Config.redirectUrl,
          discoveryUrl: '${Config.baseUrl}/${Config.discoveryUrl}',
          preferEphemeralSession: true,
          scopes: ['openid'],
          promptValues: ['login'],
        ),
      );
      if (authenticateResult != null) {
        _saveTokenRequestResponse(authenticateResult);
      }
    } catch (e) {
      if (e is AuthException) {
        throw AuthException(e.message);
      } else if (e is PlatformException) {
        throw const AuthException('AuthException');
      }
    }
  }

  @override
  Future<void> logout(ref) async {
    ref.read(timerNotifierProvider.notifier).stopTimer();
    final settingsRepository = ref.read(settingsRepositoryProvider);
    bool? getUseBiometrics = await settingsRepository.getUseBiometrics();

    const String authUrl = '${Config.baseUrl}/${Config.revokeURL}';

    List<Future<String?>> tokensToRevoke = [_getStoredAccessToken()];
    bool notUsingBiometrics =
        ((getUseBiometrics != null && getUseBiometrics == false) ||
            getUseBiometrics == null);
    if (notUsingBiometrics) {
      // if we do not use biometrics, we can revoke the refresh token
      tokensToRevoke.add(_getStoredRefreshToken());
    }

    await for (final String? token in Stream.fromFutures(tokensToRevoke)) {
      if (token == null) {
        continue;
      }

      _client.post(
        Uri.parse(authUrl),
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': '*/*',
        },
        body: {
          'client_id': Config.clientId,
          'redirect_uri': Config.redirectUrl,
          'token': token,
        },
      ).ignore();
    }

    await Future.wait([
      // if we do not use biometrics, we can remove the refresh token
      if (notUsingBiometrics)
        SecureStorageService.removeData(storageKeyRefreshToken),
      SecureStorageService.removeData(storageKeyAccessToken),
      SecureStorageService.removeData(storageKeyAccessTokenExpirationDateTime),
    ]);
  }

  // refresh Login
  // check if token still valid
  // if not, try to get a new auth token With refresh token
  @override
  Future<void> refreshLogin() async {
    try {
      await _refreshToken();
    } catch (e) {
      throw Exception('Refresh login fehlgeschlagen');
    }
  }

  // if auth token invalidated, get a new one with the refresh token
  Future<String?> _refreshToken() async {
    TokenResponse? tokenRequestResponse;

    String? storedRefreshToken = await _getStoredRefreshToken();
    if (storedRefreshToken is String) {
      try {
        tokenRequestResponse = await _appAuth.token(
          TokenRequest(
            clientSecret: Config.clientSecret,
            Config.clientId,
            Config.redirectUrl,
            discoveryUrl: '${Config.baseUrl}/${Config.discoveryUrl}',
            refreshToken: storedRefreshToken,
            scopes: ['openid'],
          ),
        );

        if (tokenRequestResponse != null) {
          _saveTokenRequestResponse(tokenRequestResponse);
        } else {
          throw TokenExtensionError('Unauthorized');
        }

        return tokenRequestResponse.accessToken;
      } on SocketException {
        throw const SocketException('No Internet connection (SocketException)');
      } catch (e) {
        throw TokenExtensionError(e.toString());
      }
    }
    return null;
  }

  Future<void> _saveTokenRequestResponse(
    TokenResponse tokenRequestResponse,
  ) async {
    String? refreshToken = tokenRequestResponse.refreshToken;
    String? accessToken = tokenRequestResponse.accessToken;
    String? idToken = tokenRequestResponse.idToken;
    DateTime? accessTokenExpirationDateTime =
        tokenRequestResponse.accessTokenExpirationDateTime;

    await Future.wait([
      _saveRefreshToken(refreshToken),
      _saveAccessToken(accessToken),
      _saveAccessTokenExpirationDateTime(accessTokenExpirationDateTime),
      _saveIdToken(idToken),
    ]);
  }

  Future<String?> _getStoredRefreshToken() async {
    String? storedToken =
        await SecureStorageService.loadData(storageKeyRefreshToken);
    return storedToken;
  }

  Future<String?> _getStoredAccessToken() async {
    String? storedToken =
        await SecureStorageService.loadData(storageKeyAccessToken);
    return storedToken;
  }

  Future<void> _saveIdToken(String? idToken) async {
    if (idToken != null) {
      SecureStorageService.saveData(storageKeyIdToken, idToken);
    }
  }

  Future<void> _saveAccessToken(String? accessToken) async {
    if (accessToken != null) {
      SecureStorageService.saveData(storageKeyAccessToken, accessToken);
    }
  }

  Future<void> _saveAccessTokenExpirationDateTime(
    DateTime? accessTokenExpirationDateTime,
  ) async {
    if (accessTokenExpirationDateTime != null) {
      SecureStorageService.saveData(
        storageKeyAccessTokenExpirationDateTime,
        accessTokenExpirationDateTime.toString(),
      );
    }
  }

  Future<void> _saveRefreshToken(String? refreshToken) async {
    if (refreshToken != null) {
      SecureStorageService.saveData(storageKeyRefreshToken, refreshToken);
    }
  }
}
