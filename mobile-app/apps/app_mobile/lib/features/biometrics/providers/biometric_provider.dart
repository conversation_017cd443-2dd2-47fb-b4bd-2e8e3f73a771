import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:local_auth/local_auth.dart';

import '../data/biometric_repository_impl.dart';
import '../domain/biometric_repository.dart';

final localAuthProvider = Provider<LocalAuthentication>((ref) {
  return LocalAuthentication();
});

final biometricRepositoryProvider = Provider<BiometricRepository>((ref) {
  final localAuth = ref.watch(localAuthProvider);
  return BiometricRepositoryImpl(localAuth);
});
