import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../dependency_injection/use_cases/privacy_use_case.dart';
import 'privacy_state.dart';

part 'privacy_control_state.g.dart';

@Riverpod(keepAlive: true)
class PrivacyConntrollerState extends _$PrivacyConntrollerState {
  @override
  FutureOr<PrivacyState> build() async {
    final useCase = ref.read(privacyUseCaseProvider);
    final value = await useCase.call();

    return PrivacyState(content: value);
  }
}
