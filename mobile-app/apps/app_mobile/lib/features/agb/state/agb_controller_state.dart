import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../dependency_injection/use_cases/agb_use_case.dart';
import 'agb_state.dart';

part 'agb_controller_state.g.dart';

@Riverpod(keepAlive: true)
class AgbControllerState extends _$AgbControllerState {
  @override
  FutureOr<AgbState> build() async {
    final useCase = ref.read(agbUseCaseProvider);
    final value = await useCase.call();

    return AgbState(content: value);
  }
}
