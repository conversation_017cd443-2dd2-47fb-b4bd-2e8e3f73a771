import 'dart:async';

import 'package:core/core.dart';

import '../../../auth/domain/errors/token_extension_error.dart';
import '../../../central_search/domain/entities/search_parameters.dart';
import '../../../central_search/domain/entities/search_result.dart';
import '../../../central_search/domain/search_repository.dart';

class FisherUseCase implements UseCase<SearchResult?, SearchParameters> {
  /// UseCase to read fisher data from repository.
  FisherUseCase({
    required this.logout,
    required SearchRepository searchRepository,
  }) : repository = searchRepository;

  final Function logout;

  final SearchRepository repository;

  @override
  Future<SearchResult?> call(SearchParameters parameters) async {
    try {
      return repository.searchRequest(
        parameters,
      );
    } on TokenExtensionError {
      await logout();

      rethrow;
    }
  }
}
