import 'package:freezed_annotation/freezed_annotation.dart';

import '../../central_search/domain/entities/search_parameters.dart';
import '../../central_search/domain/entities/search_result.dart';
import 'nfc_state_enum.dart';

part 'nfc_control_state.freezed.dart';

@freezed
class NfcControlState with _$NfcControlState {
  const factory NfcControlState({
    SearchParameters? nfcSearchParameters,
    SearchResult? nfcResults,
    NfcStateEnum? nfcState,
    Object? error,
  }) = _NfcControlState;
}
