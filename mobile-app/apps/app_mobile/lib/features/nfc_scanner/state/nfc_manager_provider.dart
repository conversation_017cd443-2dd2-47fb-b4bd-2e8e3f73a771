import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:nfc_manager/nfc_manager.dart';

import '../../../app/app.dart';
import '../../auth/state/auth_state.dart';
import '../../auth/state/auth_state_controller.dart';

typedef NfcDiscoveredCallback = void Function(
  NfcTag tag,
  UniqueKey id,
  ProviderContainer ref,
  Locale? defaultLocale,
);

final nfcManagerProvider = Provider<NfcManagerInstance?>((ref) {
  final locale = AppLocalizations.supportedLocales[0];

  return NfcManagerInstance(locale);
});

// default callback which display toast message on Android
void androidDiscoveredNfcTag(
  NfcTag tag,
  UniqueKey id,
  ProviderContainer ref,
  Locale? defaultLocale,
) async {
  AppLocalizations? t;
  String snackMessage = '';
  final AuthState authState = await ref.read(authControllerProvider.future);

  if (defaultLocale != null) {
    t = await AppLocalizations.delegate.load(defaultLocale);
    snackMessage = (authState.isAuthenticated)
        ? t.nfcSnackBarLoggedIn
        : t.nfcSnackBarNotLoggedIn;
  }
  scaffoldMessengerKey.currentState?.hideCurrentSnackBar();
  scaffoldMessengerKey.currentState?.showSnackBar(
    SnackBar(
      content: Text(snackMessage),
    ),
  );
}

class NfcManagerInstance {
  NfcManagerInstance(this.defaultLocale);

  final Locale defaultLocale;
  Map<UniqueKey, NfcDiscoveredCallback?> callbacks = {};
  bool isStarted = false;
  UniqueKey id = UniqueKey();

  void stop(UniqueKey id) {
    callbacks.remove(id);
    if (!Platform.isAndroid) {
      NfcManagerInstance._stopSession();
    }
  }

  UniqueKey start({
    NfcDiscoveredCallback? discoveredCallback = androidDiscoveredNfcTag,
    required Function(NfcError error) onError,
    required ProviderContainer container,
  }) {
    id = UniqueKey();
    callbacks[id] = discoveredCallback;

    if (!isStarted) {
      _startSession(id, onError, container);
    }
    return id;
  }

  void _startSession(
    UniqueKey id,
    Function? onError,
    ProviderContainer container,
  ) async {
    bool available = await NfcManager.instance.isAvailable();
    if (!available) {
      throw Exception('no nfc available');
    }

    NfcManager.instance.startSession(
      invalidateAfterFirstRead: true,
      onError: (onError != null)
          ? (error) {
              return onError(error);
            }
          : (error) async {},
      onDiscovered: (final NfcTag tag) async {
        assert(callbacks.isNotEmpty);
        callbacks.entries.last.value!(
          tag,
          id,
          container,
          defaultLocale,
        );
      },
    );
  }

  static void _stopSession() {
    NfcManager.instance.stopSession();
  }
}
