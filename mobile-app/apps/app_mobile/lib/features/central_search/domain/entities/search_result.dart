import 'license_status_enum.dart';

class SearchResult {
  SearchResult({
    this.licenseNumber,
    this.title,
    required this.licenseType,
    required this.licenseStatus,
    required this.licenseNote,
    required this.taxValid,
    required this.taxNote,
    required this.givenNames,
    required this.surname,
    required this.birthdate,
  });

  final String? licenseNumber;
  final String? title;
  final String birthdate;
  final String givenNames;
  final String surname;
  final String licenseNote; // lesbar<PERSON>, übersetzter Zusatzhinweis
  final String licenseType; // 'regular'
  final LicenseStatus
      licenseStatus; // VALID | INVALID | BANNED // gültig | ungültig | gesperrt Fischereischein
  final String taxNote; // lesbarer, übersetzter Zusatzhinweis
  final bool
      taxValid; // true | false ist eine Fischereiabgabe für das aktuelle Jahr getätigt worden
}
