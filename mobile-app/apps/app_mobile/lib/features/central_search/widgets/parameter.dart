import 'package:auto_size_text/auto_size_text.dart';
import 'package:design/design.dart';
import 'package:flutter/material.dart';

import '../../../app/app_index.dart';
import '../domain/entities/image_type_enum.dart';
import '../domain/entities/parameter_type.dart';
import '../domain/entities/result_type_enum.dart';
import 'parameter_widget.dart';

class Parameter extends StatelessWidget {
  const Parameter({
    super.key,
    required this.imageType,
    required this.resultType,
    required this.note,
    required this.context,
    required this.parameterType,
  });
  final ImageType imageType;
  final LicenseValidityStatus resultType;
  final String note;
  final BuildContext context;
  final ParameterType parameterType;

  List<String> cutTextForDisplay(String text) {
    return [
      text.substring(0, text.length - 6),
      text.substring(text.length - 6, text.length),
    ];
  }

  Widget overflowReplacement(
    String text,
    Color textColor1,
    Color textColor2,
    TextStyle textStyle,
  ) {
    return Padding(
      padding: paddingTop4,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '${cutTextForDisplay(text)[0]}-',
            style: textStyle.copyWith(
              fontWeight: FontWeight.w400,
              color: textColor1,
              height: 1.2,
            ),
          ),
          Text(
            cutTextForDisplay(text)[1],
            style: textStyle.copyWith(
              color: textColor2,
              height: 1.2,
            ),
          ),
        ],
      ),
    );
  }

  Widget autoSizedText(
    String text,
    Color textColor1,
    Color textColor2,
    TextStyle textStyle,
    AutoSizeGroup autoSizeGroup,
  ) {
    return FittedBox(
      fit: BoxFit.scaleDown,
      child: AutoSizeText.rich(
        group: autoSizeGroup,
        TextSpan(
          text: cutTextForDisplay(text)[0],
          style: textStyle.copyWith(
            fontWeight: FontWeight.w300,
            fontSize: 24,
            color: textColor1,
          ),
          children: <TextSpan>[
            TextSpan(
              text: cutTextForDisplay(text)[1],
              style: textStyle.copyWith(
                fontSize: 24,
                color: textColor2,
              ),
            ),
          ],
        ),
        maxLines: 1,
        overflowReplacement:
            overflowReplacement(text, textColor1, textColor2, textStyle),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    String typeImageAsset = '';
    String resultTypeImageAsset = '';
    String? noticeText = note;
    String resultTypeImageAltText = '';
    Widget typeWidget = const Text('');
    bool needBorderRadius = false;
    Color? parameterBackgroundColor;
    Color? textColorPrimary = context.theme.textPrimary;
    Color? textColorSecondary = context.theme.textSecondary;
    final AutoSizeGroup autoSizeGroup = AutoSizeGroup();

    switch (resultType) {
      case LicenseValidityStatus.missing:
        resultTypeImageAsset = LicenseValidityStatus.missing.imageAsset;
        resultTypeImageAltText = context.strings.negative;

        break;
      case LicenseValidityStatus.invalid:
        resultTypeImageAsset =
            LicenseValidityStatus.invalidOnRedBackground.imageAsset;
        break;
      case LicenseValidityStatus.invalidOnRedBackground:
        resultTypeImageAsset = LicenseValidityStatus.invalid.imageAsset;
        parameterBackgroundColor = context.theme.invalid;
        textColorPrimary = context.theme.colorScheme.onInverseSurface;
        textColorSecondary = context.theme.colorScheme.onInverseSurface;
        resultTypeImageAltText = context.strings.negative;
        break;
      case LicenseValidityStatus.valid:
        resultTypeImageAsset = LicenseValidityStatus.valid.imageAsset;
        resultTypeImageAltText = context.strings.positive;
    }

    if (parameterType == ParameterType.license &&
        resultType == LicenseValidityStatus.invalid) {
      if (noticeText != '') {
        // with text
        parameterBackgroundColor = Colors.white;
        textColorPrimary = context.theme.textPrimary;
        textColorSecondary = context.theme.textSecondary;
      } else {
        // without text
        parameterBackgroundColor = context.theme.invalid;
        textColorPrimary = context.theme.colorScheme.onInverseSurface;
        textColorSecondary = context.theme.colorScheme.onInverseSurface;
        resultTypeImageAltText = context.strings.negative;
      }
    } else if (parameterType == ParameterType.license &&
        resultType == LicenseValidityStatus.valid) {
      parameterBackgroundColor = Colors.white;
      textColorPrimary = context.theme.textPrimary;
      textColorSecondary = context.theme.textSecondary;
    } else if (parameterType == ParameterType.tax &&
        resultType == LicenseValidityStatus.invalid) {
      resultTypeImageAsset =
          LicenseValidityStatus.invalidOnRedBackground.imageAsset;

      parameterBackgroundColor = context.theme.invalid;
      textColorPrimary = context.theme.colorScheme.onInverseSurface;
      textColorSecondary = context.theme.colorScheme.onInverseSurface;
      resultTypeImageAltText = context.strings.negative;
    }

    switch (imageType) {
      case ImageType.fishingTax:
        typeImageAsset = 'assets/images/icons/icon-48-fishing-tax.png';
      case ImageType.fishingTaxMissing:
        typeImageAsset = 'assets/images/icons/icon-48-fishing-tax-missing.png';
      case ImageType.license:
        typeImageAsset = 'assets/images/icons/icon-48-license-card.png';
      case ImageType.licenseMissing:
        typeImageAsset = 'assets/images/icons/icon-48-no-licensecard.png';
      case ImageType.licenseMissingWithText:
        typeImageAsset = 'assets/images/icons/icon-48-no-licensecard-grey.png';
        break;
    }

    switch (parameterType) {
      case ParameterType.license:
        typeWidget = autoSizedText(
          context.strings.fishingLicense,
          textColorPrimary,
          textColorSecondary,
          context.theme.header,
          autoSizeGroup,
        );
        break;
      case ParameterType.tax:
        typeWidget = autoSizedText(
          context.strings.fishingTax,
          textColorPrimary,
          textColorSecondary,
          context.theme.header,
          autoSizeGroup,
        );
        needBorderRadius = true;
        break;
    } //

    EdgeInsets paddingRight = EdgeInsets.only(right: 3);

    if (typeImageAsset == 'assets/images/icons/icon-48-license-card.png' ||
        typeImageAsset == 'assets/images/icons/icon-48-no-licensecard.png' ||
        typeImageAsset ==
            'assets/images/icons/icon-48-no-licensecard-grey.png') {
      paddingRight = EdgeInsets.only(right: 0);
    }

    return ParameterWidget(
      parameterBackgroundColor: parameterBackgroundColor,
      noticeTag: noticeText,
      paddingRight: paddingRight,
      resultTypeImageAsset: resultTypeImageAsset,
      typeImageAsset: typeImageAsset,
      resultTypeImageAltText: resultTypeImageAltText,
      typeWidget: typeWidget,
      needBorderRadius: needBorderRadius,
    );
  }
}
