import 'package:flutter/foundation.dart';
import 'package:go_router/go_router.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../features/auth/state/auth_state_controller.dart';
import '../../features/network_info/state/network_info_state_controller.dart';
import '../app_index.dart';

part 'app_router.g.dart';

@Riverpod(keepAlive: true)
GoRouter appRouter(ref) {
  final router = GoRouter(
    navigatorKey: rootNavigatorKey,
    debugLogDiagnostics: kDebugMode,
    initialLocation: AppRoutes.welcomePage,
    routes: $appRoutes,
    redirect: (context, state) {
      final authState = ref.read(authControllerProvider);

      final location = state.matchedLocation;

      final isLoggedIn =
          (authState.value != null && (authState.value!.isAuthenticated));
      final loggingIn =
          (location == AppRoutes.signIn || location == AppRoutes.welcomePage);

      if (!isLoggedIn &&
          location != AppRoutes.signIn &&
          location != AppRoutes.welcomePage &&
          location != AppRoutes.agb &&
          location != AppRoutes.privacy &&
          location != AppRoutes.imprint) {
        // If the user is not logged in redirect to welcome
        return AppRoutes.welcomePage;
      } else if (isLoggedIn && loggingIn) {
        // If the user is logged in and is on the login page, redirect to the home page

        return AppRoutes.homePage;
      }
      // No redirection is needed

      return null;
    },
  );

  ref.listen(authControllerProvider, (final prev, final current) {
    if (!current.isLoading &&
        prev?.value?.isAuthenticated != current.value?.isAuthenticated) {
      router.refresh();
    }
  });

  ref.listen(networkInfoStateControllerProvider,
      (final prev, final current) async {
    if (current.displayNetworkInfo) {
      await ref.read(authControllerProvider.notifier).signout();
    }
  });

  return router;
}
