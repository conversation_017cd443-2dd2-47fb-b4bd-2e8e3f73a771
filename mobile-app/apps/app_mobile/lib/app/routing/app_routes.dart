import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../app_index.dart';

import '../pages/agb_page.dart';
import '../pages/imprint_page.dart';
import '../pages/privacy_page.dart';
import '../pages/start_screen.dart';

part 'app_routes.g.dart';

final rootNavigatorKey = GlobalKey<NavigatorState>(debugLabel: 'rootNavigator');

@TypedGoRoute<NFCRegisterPageRoute>(path: AppRoutes.nfcRegister)
class NFCRegisterPageRoute extends GoRouteData {
  const NFCRegisterPageRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const NfcRegisterPage();
  }
}

@TypedGoRoute<BarcodeRegisterPageRoute>(path: AppRoutes.barCodeScannerRegister)
class BarcodeRegisterPageRoute extends GoRouteData {
  const BarcodeRegisterPageRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const BarCodeRegisterPage();
  }
}

@TypedGoRoute<BarcodeResultPageRoute>(path: AppRoutes.homePage)
class BarcodeResultPageRoute extends GoRouteData {
  const BarcodeResultPageRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const StartScreen();
  }
}

@TypedGoRoute<WelcomePageRoute>(path: AppRoutes.welcomePage)
class WelcomePageRoute extends GoRouteData {
  const WelcomePageRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const WelcomePage();
  }
}

@TypedGoRoute<PrivacyPageRoute>(path: AppRoutes.privacy)
class PrivacyPageRoute extends GoRouteData {
  const PrivacyPageRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const PrivacyPage();
  }
}

@TypedGoRoute<AgbPageRoute>(path: AppRoutes.agb)
class AgbPageRoute extends GoRouteData {
  const AgbPageRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const AgbPage();
  }
}

@TypedGoRoute<ImprintPageRoute>(path: AppRoutes.imprint)
class ImprintPageRoute extends GoRouteData {
  const ImprintPageRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const ImprintPage();
  }
}
