import 'dart:io';
import 'dart:math';

import 'package:auto_size_text/auto_size_text.dart';
import 'package:design/design.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';

import '../../../../features/auth/state/auth_state_controller.dart';
import '../../../../features/automatic_logout/providers/timer_repository_provider.dart';
import '../../../app_index.dart';
import 'blur_menu.dart';
import 'blur_menu_item.dart';

class _OverlayableContainerLayout extends SingleChildLayoutDelegate {
  _OverlayableContainerLayout(
    this.position,
    this.itemsCount,
    this.itemHeight,
    this.textScale,
    this.menuMaxWidth,
  );

  final Rect position;
  final int itemsCount;
  final double itemHeight;
  final double? menuMaxWidth;
  final double textScale;
  final double leftMargin = 230;
  final double itemsMargin = 30;

  @override
  BoxConstraints getConstraintsForChild(BoxConstraints constraints) {
    return BoxConstraints.tight(
      Size(
        position.width + (leftMargin * textScale),
        position.height + (itemHeight) * itemsCount + itemsMargin,
      ),
    );
  }

  @override
  Offset getPositionForChild(Size size, Size childSize) {
    double positionLeft = position.left - leftMargin * textScale;
    if (childSize.width > size.width) {
      positionLeft = -20;
    }

    return Offset(
      positionLeft,
      position.top,
    );
  }

  @override
  bool shouldRelayout(covariant _OverlayableContainerLayout oldDelegate) {
    return (position != oldDelegate.position);
  }
}

class MenuIconButton extends ConsumerStatefulWidget {
  const MenuIconButton({
    super.key,
    required this.onMenuToggle,
    required this.menuMaxWidth,
  });
  final VoidCallback onMenuToggle;

  final double menuMaxWidth;

  @override
  ConsumerState<MenuIconButton> createState() => _MenuIconButtonState();
}

class _MenuIconButtonState extends ConsumerState<MenuIconButton> {
  OverlayEntry? overlayEntry;

  void toggleBlurMenu(double textScale) {
    final OverlayState overlayState = Overlay.of(context);
    const double itemHeight = 65;
    const double blurMenuWidthInit = 280;
    const double buttonPaddingRight = 10;

    if (overlayEntry != null) {
      overlayEntry?.remove();
      overlayEntry = null;
      widget.onMenuToggle();
      return;
    }

    widget.onMenuToggle();
    final authState = ref.watch(authControllerProvider);
    final bool isLoggedIn =
        (authState.value != null && (authState.value!.isAuthenticated));
    final AutoSizeGroup autoSizeGroup = AutoSizeGroup();

    Rect getPosition(BuildContext context) {
      final RenderBox box = context.findRenderObject() as RenderBox;
      final Offset topLeft = box.size.topLeft(box.localToGlobal(Offset.zero));
      final Offset bottomRight =
          box.size.bottomRight(box.localToGlobal(Offset.zero));
      return Rect.fromLTRB(
        topLeft.dx,
        topLeft.dy,
        bottomRight.dx,
        bottomRight.dy,
      );
    }

    final Rect overlayPosition = getPosition(overlayState.context);

    final Rect widgetPosition = getPosition(context).translate(
      -overlayPosition.left,
      -overlayPosition.top,
    );

    List<BlurMenuItem> blurMenuItems = [
      BlurMenuItem(
        semanticLabel: context.strings.menuPrivacySematic,
        asset: 'assets/images/icons/icon-48-privacy.png',
        autoSizeGroup: autoSizeGroup,
        context.strings.menuPrivacy,
        itemPressed: () {
          _removeOverlay();
          context.go(
            AppRoutes.privacy,
          );
        },
      ),
      BlurMenuItem(
        asset: 'assets/images/icons/icon-48-agb.png',
        autoSizeGroup: autoSizeGroup,
        context.strings.menuAGB,
        itemPressed: () {
          _removeOverlay();
          context.go(
            AppRoutes.agb,
          );
        },
        semanticLabel: context.strings.menuAGBSemantic,
      ),
      BlurMenuItem(
        asset: 'assets/images/icons/icon-48-imprint.png',
        autoSizeGroup: autoSizeGroup,
        context.strings.menuImprint,
        semanticLabel: context.strings.imprintSemantic,
        itemPressed: () {
          _removeOverlay();
          context.go(
            AppRoutes.imprint,
          );
        },
      ),
      BlurMenuItem(
        asset: 'assets/images/icons/icon-48-support.png',
        autoSizeGroup: autoSizeGroup,
        context.strings.menuSupport,
        semanticLabel: context.strings.menuSupportSemantic,
        itemPressed: () {
          _removeOverlay();

          _launchDeviceInfoUrl(
            context,
          );
        },
      ),
    ];

    if (isLoggedIn) {
      blurMenuItems.add(
        BlurMenuItem(
          semanticLabel: context.strings.settingsPageSignOutButtonTitle,
          asset: 'assets/images/icons/icon-48-signout.png',
          autoSizeGroup: autoSizeGroup,
          context.strings.menuSignout,
          itemPressed: () {
            _removeOverlay();
            ref.read(authControllerProvider.notifier).signout();
            ref.read(timerNotifierProvider.notifier).stopTimer();
          },
        ),
      );
    }

    double menuWidth = min(
      blurMenuWidthInit * textScale,
      widget.menuMaxWidth - buttonPaddingRight,
    );

    overlayEntry = OverlayEntry(
      builder: (context) {
        return GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: () {
            _removeOverlay();
          },
          child: Semantics(
            enabled: true,
            child: CustomSingleChildLayout(
              delegate: _OverlayableContainerLayout(
                widgetPosition,
                blurMenuItems.length,
                itemHeight,
                textScale,
                widget.menuMaxWidth,
              ),
              child: GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () {
                  _removeOverlay();
                },
                child: BlurMenu(
                  width: menuWidth,
                  itemHeight: itemHeight * textScale,
                  isLoggedIn: isLoggedIn,
                  items: blurMenuItems,
                ),
              ),
            ),
          ),
        );
      },
    );

    overlayState.insert(
      overlayEntry!,
    );
  }

  void _removeOverlay() {
    widget.onMenuToggle();
    overlayEntry?.remove();
    overlayEntry = null;
  }

  @override
  Widget build(BuildContext context) {
    double textScale = max(MediaQuery.textScalerOf(context).scale(1), 1.0);

    return Container(
      decoration: const BoxDecoration(
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: Color.fromRGBO(205, 210, 218, 0.16),
            offset: Offset(-3, 2),
            blurRadius: 5,
            spreadRadius: 1,
          ),
          BoxShadow(
            color: Color.fromRGBO(205, 210, 218, 0.08),
            offset: Offset(-2, 3),
            blurRadius: 10,
            spreadRadius: 0,
          ),
          BoxShadow(
            color: Color.fromRGBO(205, 210, 218, 0.08),
            offset: Offset(2, 4),
            blurRadius: 6,
            spreadRadius: 0,
          ),
          BoxShadow(
            color: Color.fromRGBO(205, 210, 218, 0.02),
            offset: Offset(0, 6),
            blurRadius: 8,
            spreadRadius: 0,
          ),
        ],
      ),
      child: PopScope(
        canPop: true,
        onPopInvokedWithResult: (didPop, result) {
          _removeOverlay();
        },
        child: Semantics(
          button: true,
          label: (overlayEntry != null)
              ? context.strings.menuIconOpened
              : context.strings.menuIcon,
          child: GestureDetector(
            onTap: () => toggleBlurMenu(textScale),
            child: Padding(
              padding: paddingTop12 + paddingBottom8,
              child: SvgPicture.asset(
                'assets/images/icons/menu_icon.svg',
                width: 60,
                height: 30,
                excludeFromSemantics: true,
              ),
            ),
          ),
        ),
      ),
    );
  }

  _launchDeviceInfoUrl(BuildContext context) async {
    final DeviceInfoProvider deviceInfoProvider = DeviceInfoProvider();
    Map<String, String> deviceInfoAndroid = {};
    Map<String, String> deviceInfoIOS = {};

    deviceInfoIOS = await deviceInfoProvider.fetchIosDeviceInfo();
    deviceInfoAndroid = await deviceInfoProvider.fetchAndroidDeviceInfo();

    if (context.mounted) {
      final encoded = Platform.isAndroid
          ? encodedAndroid(context, deviceInfoAndroid)
          : encodedIos(context, deviceInfoIOS);

      urlLauncher(encoded);
    }
  }

  encodedAndroid(
    BuildContext context,
    Map<String, String> deviceInfoAndroid,
  ) {
    return Uri.encodeFull(
      '${context.strings.reportContactDataUrl}'
      '${context.strings.supportGreetingText}'
      '${context.strings.deviceInfoText} ${deviceInfoAndroid['model']}'
      ' ${deviceInfoAndroid['device']}'
      '${context.strings.infoAndroidSDKText}${deviceInfoAndroid['SDK']}'
      ', Android Version ${deviceInfoAndroid['androidVersion']}'
      '${context.strings.versionText}${DeviceInfoProvider.appVersion}\n'
      '${context.strings.supportDescriptionText}'
      '${context.strings.supportFormalClosing}',
    );
  }

  encodedIos(BuildContext context, Map<String, String> deviceInfoIOS) {
    return Uri.encodeFull(
      '${context.strings.reportContactDataUrl}'
      '${context.strings.supportGreetingText}'
      '\n${context.strings.deviceInfoText}${deviceInfoIOS['deviceName']}'
      '\n${deviceInfoIOS['systemName']}'
      ' ${deviceInfoIOS['systemVersion']}'
      '${context.strings.versionText}${DeviceInfoProvider.appVersion}\n'
      '${context.strings.supportDescriptionText}'
      '${context.strings.supportFormalClosing}',
    );
  }
}
