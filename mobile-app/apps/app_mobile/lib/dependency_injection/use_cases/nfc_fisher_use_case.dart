import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../features/nfc_scanner/domain/use_cases/nfc_fisher_use_case.dart';
import '../repositories.dart';

part 'nfc_fisher_use_case.g.dart';

@riverpod
FisherUseCase fisherUseCase(ref) {
  final searchRepository = ref.read(searchRepositoryProvider);
  return FisherUseCase(
    logout: ref.read(authRepositoryProvider).logout,
    searchRepository: searchRepository,
  );
}
