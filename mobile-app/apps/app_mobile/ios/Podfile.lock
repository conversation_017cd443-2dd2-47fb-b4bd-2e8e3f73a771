PODS:
  - AppAuth (1.7.4):
    - AppAuth/Core (= 1.7.4)
    - AppAuth/ExternalUserAgent (= 1.7.4)
  - AppAuth/Core (1.7.4)
  - AppAuth/ExternalUserAgent (1.7.4):
    - AppAuth/Core
  - audio_session (0.0.1):
    - Flutter
  - connectivity_plus (0.0.1):
    - Flutter
  - device_info_plus (0.0.1):
    - Flutter
  - Flutter (1.0.0)
  - flutter_appauth (0.0.1):
    - AppAuth (= 1.7.4)
    - Flutter
  - flutter_native_splash (2.4.3):
    - Flutter
  - flutter_secure_storage (6.0.0):
    - Flutter
  - GoogleDataTransport (9.4.1):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleMLKit/BarcodeScanning (6.0.0):
    - GoogleMLKit/MLKitCore
    - MLKitBarcodeScanning (~> 5.0.0)
  - GoogleMLKit/MLKitCore (6.0.0):
    - MLKitCommon (~> 11.0.0)
  - GoogleToolboxForMac/Defines (4.2.1)
  - GoogleToolboxForMac/Logger (4.2.1):
    - GoogleToolboxForMac/Defines (= 4.2.1)
  - "GoogleToolboxForMac/NSData+zlib (4.2.1)":
    - GoogleToolboxForMac/Defines (= 4.2.1)
  - GoogleUtilities/Environment (7.13.0):
    - GoogleUtilities/Privacy
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.13.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (7.13.0)
  - GoogleUtilities/UserDefaults (7.13.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilitiesComponents (1.1.0):
    - GoogleUtilities/Logger
  - GTMSessionFetcher/Core (3.4.1)
  - just_audio (0.0.1):
    - Flutter
    - FlutterMacOS
  - local_auth_darwin (0.0.1):
    - Flutter
    - FlutterMacOS
  - MLImage (1.0.0-beta5)
  - MLKitBarcodeScanning (5.0.0):
    - MLKitCommon (~> 11.0)
    - MLKitVision (~> 7.0)
  - MLKitCommon (11.0.0):
    - GoogleDataTransport (< 10.0, >= 9.4.1)
    - GoogleToolboxForMac/Logger (< 5.0, >= 4.2.1)
    - "GoogleToolboxForMac/NSData+zlib (< 5.0, >= 4.2.1)"
    - GoogleUtilities/UserDefaults (< 8.0, >= 7.13.0)
    - GoogleUtilitiesComponents (~> 1.0)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3.2)
  - MLKitVision (7.0.0):
    - GoogleToolboxForMac/Logger (< 5.0, >= 4.2.1)
    - "GoogleToolboxForMac/NSData+zlib (< 5.0, >= 4.2.1)"
    - GTMSessionFetcher/Core (< 4.0, >= 3.3.2)
    - MLImage (= 1.0.0-beta5)
    - MLKitCommon (~> 11.0)
  - mobile_scanner (5.2.3):
    - Flutter
    - GoogleMLKit/BarcodeScanning (~> 6.0.0)
  - nanopb (2.30910.0):
    - nanopb/decode (= 2.30910.0)
    - nanopb/encode (= 2.30910.0)
  - nanopb/decode (2.30910.0)
  - nanopb/encode (2.30910.0)
  - nfc_manager (0.0.1):
    - Flutter
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - PromisesObjC (2.4.0)
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - url_launcher_ios (0.0.1):
    - Flutter
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - wakelock_plus (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - audio_session (from `.symlinks/plugins/audio_session/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - Flutter (from `Flutter`)
  - flutter_appauth (from `.symlinks/plugins/flutter_appauth/ios`)
  - flutter_native_splash (from `.symlinks/plugins/flutter_native_splash/ios`)
  - flutter_secure_storage (from `.symlinks/plugins/flutter_secure_storage/ios`)
  - just_audio (from `.symlinks/plugins/just_audio/darwin`)
  - local_auth_darwin (from `.symlinks/plugins/local_auth_darwin/darwin`)
  - mobile_scanner (from `.symlinks/plugins/mobile_scanner/ios`)
  - nfc_manager (from `.symlinks/plugins/nfc_manager/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/darwin`)
  - wakelock_plus (from `.symlinks/plugins/wakelock_plus/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/darwin`)

SPEC REPOS:
  trunk:
    - AppAuth
    - GoogleDataTransport
    - GoogleMLKit
    - GoogleToolboxForMac
    - GoogleUtilities
    - GoogleUtilitiesComponents
    - GTMSessionFetcher
    - MLImage
    - MLKitBarcodeScanning
    - MLKitCommon
    - MLKitVision
    - nanopb
    - PromisesObjC

EXTERNAL SOURCES:
  audio_session:
    :path: ".symlinks/plugins/audio_session/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  Flutter:
    :path: Flutter
  flutter_appauth:
    :path: ".symlinks/plugins/flutter_appauth/ios"
  flutter_native_splash:
    :path: ".symlinks/plugins/flutter_native_splash/ios"
  flutter_secure_storage:
    :path: ".symlinks/plugins/flutter_secure_storage/ios"
  just_audio:
    :path: ".symlinks/plugins/just_audio/darwin"
  local_auth_darwin:
    :path: ".symlinks/plugins/local_auth_darwin/darwin"
  mobile_scanner:
    :path: ".symlinks/plugins/mobile_scanner/ios"
  nfc_manager:
    :path: ".symlinks/plugins/nfc_manager/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/darwin"
  wakelock_plus:
    :path: ".symlinks/plugins/wakelock_plus/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/darwin"

SPEC CHECKSUMS:
  AppAuth: 182c5b88630569df5acb672720534756c29b3358
  audio_session: 19e9480dbdd4e5f6c4543826b2e8b0e4ab6145fe
  connectivity_plus: 2a701ffec2c0ae28a48cf7540e279787e77c447d
  device_info_plus: 97af1d7e84681a90d0693e63169a5d50e0839a0d
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_appauth: 1ce438877bc111c5d8f42da47729909290624886
  flutter_native_splash: f71420956eb811e6d310720fee915f1d42852e7a
  flutter_secure_storage: d33dac7ae2ea08509be337e775f6b59f1ff45f12
  GoogleDataTransport: 6c09b596d841063d76d4288cc2d2f42cc36e1e2a
  GoogleMLKit: 97ac7af399057e99182ee8edfa8249e3226a4065
  GoogleToolboxForMac: d1a2cbf009c453f4d6ded37c105e2f67a32206d8
  GoogleUtilities: d053d902a8edaa9904e1bd00c37535385b8ed152
  GoogleUtilitiesComponents: 679b2c881db3b615a2777504623df6122dd20afe
  GTMSessionFetcher: 8000756fc1c19d2e5697b90311f7832d2e33f6cd
  just_audio: a42c63806f16995daf5b219ae1d679deb76e6a79
  local_auth_darwin: 66e40372f1c29f383a314c738c7446e2f7fdadc3
  MLImage: 1824212150da33ef225fbd3dc49f184cf611046c
  MLKitBarcodeScanning: 10ca0845a6d15f2f6e911f682a1998b68b973e8b
  MLKitCommon: afec63980417d29ffbb4790529a1b0a2291699e1
  MLKitVision: e858c5f125ecc288e4a31127928301eaba9ae0c1
  mobile_scanner: 96e91f2e1fb396bb7df8da40429ba8dfad664740
  nanopb: 438bc412db1928dac798aa6fd75726007be04262
  nfc_manager: d7da7cb781f7744b94df5fe9dbca904ac4a0939e
  package_info_plus: c0502532a26c7662a62a356cebe2692ec5fe4ec4
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78
  sqflite_darwin: 5a7236e3b501866c1c9befc6771dfd73ffb8702d
  url_launcher_ios: 5334b05cef931de560670eeae103fd3e431ac3fe
  video_player_avfoundation: 7c6c11d8470e1675df7397027218274b6d2360b3
  wakelock_plus: 76957ab028e12bfa4e66813c99e46637f367fc7e
  webview_flutter_wkwebview: a4af96a051138e28e29f60101d094683b9f82188

PODFILE CHECKSUM: 206197ac4b38a586f719f01a95af058568744b1e

COCOAPODS: 1.16.2
