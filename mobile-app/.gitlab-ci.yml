stages:
  - build
  - test
  - analyze
  - release
  - distribute

variables:
  API_BASE_URL: "https://digifischdok-stage-cluster.dsecurecloud.de"
  AUTH_BASE_URL: "https://digifischdok-iam.dsecurecloud.de:8443/realms/digifischdok_stage"

.default:
  image: ghcr.io/cirruslabs/flutter:3.24.0
  cache:
    - key: cache-default
      paths:
        - $CI_PROJECT_DIR/.pub-cache/
  before_script:
    - export PUB_CACHE=$CI_PROJECT_DIR/.pub-cache
    - export PATH="$PATH":"$PUB_CACHE/bin"
    - export MELOS_SDK_PATH="$FLUTTER_HOME"
    - dart pub global activate melos 6.2.0
    - melos bs

.macos:
  image: ghcr.io/cirruslabs/macos-sonoma-xcode:latest
  cache:
    - key: cache-macos
      paths:
        - $CI_PROJECT_DIR/.pub-cache/
  before_script:
    - export PUB_CACHE=$CI_PROJECT_DIR/.pub-cache
    - export PATH="$PATH":"$PUB_CACHE/bin"
    - brew tap leoafarias/fvm
    - brew install fvm
    - brew install fastlane
    - "('yes' | fvm use) || true"
    - export PATH="$PATH:$CI_PROJECT_DIR/.fvm/flutter_sdk/bin"
    - fvm dart pub global activate melos 6.2.0
    - melos bs

build-android:
  extends: .default
  stage: build
  script: 
  - cd packages/local_auth_android
  - flutter pub get
  - flutter pub run pigeon --input pigeons/messages.dart 
  - cd ../../apps/app_mobile 
  - flutter build apk --dart-define=API_BASE_URL=$API_BASE_URL --dart-define=AUTH_BASE_URL=$AUTH_BASE_URL

   

  when: manual
  artifacts:
    paths:
      - apps/app_mobile/build/app/outputs/flutter-apk/app-release.apk
build-ios:
  tags:
    - macos
  extends: .macos
  stage: build
  script:
    - curl --silent "https://gitlab.com/gitlab-org/incubation-engineering/mobile-devops/download-secure-files/-/raw/main/installer" | bash
    - fastlane run install_provisioning_profile path:"$CI_PROJECT_DIR/.secure_files/KontrollApp_Fischereiregister_App_Store.mobileprovision"
    - fastlane run unlock_keychain path:"$CI_PROJECT_DIR/.secure_files/kontrollapp.keychain-db" password:"$FL_UNLOCK_KEYCHAIN_PASSWORD"
    - cd packages/local_auth_android
    - flutter pub get
    - flutter pub run pigeon --input pigeons/messages.dart 
    - cd ../../apps/app_mobile
    - fvm flutter build ipa --export-options-plist=ios/export-options.plist --dart-define=API_BASE_URL=$API_BASE_URL --dart-define=AUTH_BASE_URL=$AUTH_BASE_URL
  when: manual
  artifacts:
    paths:
      - apps/app_mobile/build/ios/ipa/*.ipa

test:
  extends: .default
  stage: test
  script:
    - dart pub global activate combine_coverage
    - dart pub global activate junitreport
    - dart pub global activate cobertura
    - cd packages/local_auth_android
    - flutter pub get
    - flutter pub run pigeon --input pigeons/messages.dart 
    - cd ../../apps/app_mobile
    - (melos exec --dir-exists="test" -- "flutter test --dart-define=AUTH_BASE_URL=http://example.com --dart-define=TOKEN_URL=token --dart-define=REVOKE_URL=revoke --dart-define=API_BASE_URL=http://api.example.com --dart-define=VALIDATE_URL=validate --coverage --reporter json > .dart_tool/tests.output") || CODE=$?
    - cat $(find . -iname 'tests.output' 2> /dev/null) | tojunit >> junitreport.xml
    - combine_coverage -p .
    - cobertura convert -i ./coverage/lcov.info -o coverage/cobertura.xml -n kontrollapp -v 1
    - exit $CODE
  artifacts:
    paths:
      - "**/tests.output"
    reports:
      coverage_report:
        coverage_format: cobertura
        path: coverage/cobertura.xml
      junit: junitreport.xml

analyze:
  extends: .default
  stage: analyze
  script:
    - cd packages/local_auth_android
    - flutter pub get
    - flutter pub run pigeon --input pigeons/messages.dart 
    - cd ../../apps/app_mobile
    - melos analyze

sbom:
  extends: .default
  stage: analyze
  script:
    - melos exec -- "dart pub deps --style list --no-dev > sbom.txt"
    - cat $(find . -iname 'sbom.txt' 2> /dev/null) >> sboms.txt
  artifacts:
    paths:
      - sboms.txt

format:
  extends: .default
  stage: analyze
  script:
    - dart format . --set-exit-if-changed
