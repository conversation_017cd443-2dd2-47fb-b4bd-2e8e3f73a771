{
	"folders": [
		{
			"path": "web-app"
		},
		{
			"path": "mobile-app",
		},
		{
			"path": "backend/register-service"
		},
		{
			"path": ".",
			"name": "root"
		}
	],
	"settings": {
		"files.exclude": {
			"backend": true,
			"web-app": true,
			"mobile-app": true
		},
		"java.configuration.updateBuildConfiguration": "automatic",
		"sqltools.connections": [
			{
				"previewLimit": 50,
				"server": "localhost",
				"port": 5432,
				"driver": "PostgreSQL",
				"name": "DigiFischDok Local",
				"database": "digifischdok",
				"username": "digifischdok"
			}
		],
		"yaml.schemas": {
			"file:///Users/<USER>/.vscode/extensions/atlassian.atlascode-3.0.10/resources/schemas/pipelines-schema.json": "bitbucket-pipelines.yml",
			"https://gitlab.com/gitlab-org/gitlab/-/raw/master/app/assets/javascripts/editor/schema/ci.json": "file:///Users/<USER>/Git/adesso/fischerei/digifischdok/.gitlab-ci-release.yml"
		},
	}
}