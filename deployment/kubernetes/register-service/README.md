# register-service

![Version: 2.1.0](https://img.shields.io/badge/Version-2.1.0-informational?style=flat-square) ![Type: application](https://img.shields.io/badge/Type-application-informational?style=flat-square) ![AppVersion: 0.17.3](https://img.shields.io/badge/AppVersion-0.17.3-informational?style=flat-square)

Helm chart for the backend service of the digifischdok application

## Values

| Key | Type | Default | Description |
|-----|------|---------|-------------|
| affinity | object | `{}` | Affinity configuration |
| autoscaling | object | `{"enabled":false,"maxReplicas":100,"minReplicas":1,"targetCPUUtilizationPercentage":80}` | Autoscaling configuration |
| autoscaling.enabled | bool | `false` | Whether to enable autoscaling |
| autoscaling.maxReplicas | int | `100` | The maximum number of replicas |
| autoscaling.minReplicas | int | `1` | The minimum number of replicas |
| autoscaling.targetCPUUtilizationPercentage | int | `80` | The target CPU utilization percentage for autoscaling |
| externalDatabase | object | `{"databaseName":"digifischdok","host":"localhost","password":"digifischdok","port":3306,"user":"digifischdok"}` | External PostgreSQL configuration. These values are used to configure the database connection. |
| externalDatabase.databaseName | string | `"digifischdok"` | The name of the external database |
| externalDatabase.host | string | `"localhost"` | The host of the external database |
| externalDatabase.password | string | `"digifischdok"` | The password for the external database |
| externalDatabase.port | int | `3306` | The port of the external database |
| externalDatabase.user | string | `"digifischdok"` | The user for the external database |
| externalKeycloak | object | `{"clientId":"backend","clientSecret":"","issuerUri":"https://auth.dev.echolot.app/realms/digifischdok_dev"}` | External Keycloak configuration |
| externalKeycloak.clientId | string | `"backend"` | The client ID for Keycloak |
| externalKeycloak.clientSecret | string | `""` | The client secret for Keycloak |
| externalKeycloak.issuerUri | string | `"https://auth.dev.echolot.app/realms/digifischdok_dev"` | The issuer URI for Keycloak |
| externalMail | object | `{"from":"<EMAIL>","host":"localhost","password":"none","port":"1025","user":"none"}` | External Mail configuration |
| externalMail.from | string | `"<EMAIL>"` | The from address for the external mail server |
| externalMail.host | string | `"localhost"` | The host of the external mail server |
| externalMail.password | string | `"none"` | The password for the external mail server |
| externalMail.port | string | `"1025"` | The port of the external mail server |
| externalMail.user | string | `"none"` | The user for the external mail server |
| fullnameOverride | string | `""` | Overrides for the full name of the service |
| image | object | `{"pullPolicy":"Always","repository":"digifischdok-oci-dev.repo-ex.zcdi.dataport.de/artifactory/digifischdok-oci-dev/digifischdok/digifischdok-register-service","tag":"latest"}` | Image configuration |
| image.pullPolicy | string | `"Always"` | The policy to apply when pulling the image |
| image.repository | string | `"digifischdok-oci-dev.repo-ex.zcdi.dataport.de/artifactory/digifischdok-oci-dev/digifischdok/digifischdok-register-service"` | The repository where the image is stored |
| image.tag | string | `"latest"` | Overrides the image tag whose default is the chart `appVersion`. |
| imagePullSecrets | list | `[{"name":"adesso-gitlab-registry"}]` | Secrets used to pull images from a private registry |
| ingress | object | `{"annotations":{},"className":"","enabled":false,"hosts":[{"host":"chart-example.local","paths":[{"path":"/","pathType":"ImplementationSpecific"}]}],"tls":[]}` | Ingress configuration |
| ingress.annotations | object | `{}` | Annotations for the ingress |
| ingress.className | string | `""` | The class of the ingress |
| ingress.enabled | bool | `false` | Whether to enable ingress |
| ingress.hosts | list | `[{"host":"chart-example.local","paths":[{"path":"/","pathType":"ImplementationSpecific"}]}]` | The hosts and paths for the ingress |
| ingress.tls | list | `[]` | TLS configuration for the ingress |
| javaArgs | list | `[]` | Additional Java Arguments |
| livenessProbe | object | `{"httpGet":{"path":"/api/actuator/health/liveness","port":"http"},"initialDelaySeconds":10}` | Liveness probe configuration. Liveness probe is used to check if this app is responding to requests (after it is marked as "ready"). |
| livenessProbe.httpGet | object | `{"path":"/api/actuator/health/liveness","port":"http"}` | The HTTP path for the liveness probe |
| livenessProbe.initialDelaySeconds | int | `10` | The initial delay before the liveness probe is initiated |
| nameOverride | string | `""` | Overrides for the default name of the service |
| nodeSelector | object | `{}` | Node selector configuration |
| osInbox | object | `{"base-urn":"https://example.com","clientId":"exampleId","clientSecret":"12345","resource":"urn:dataport:osi:postfach:rz2:stage:go","scope":"default","tokenUri":"https://some.example.com/webidp2/connect/token"}` | Online Service inbox configuration |
| osInbox.base-urn | string | `"https://example.com"` | Base URN of the OSI inbox |
| osInbox.clientId | string | `"exampleId"` | Client ID for the inbox Keycloak |
| osInbox.clientSecret | string | `"12345"` | Client Secret for the inbox keylcoak |
| osInbox.resource | string | `"urn:dataport:osi:postfach:rz2:stage:go"` | Resource path of the inbox |
| osInbox.scope | string | `"default"` | Scope of the inbox keycloak |
| osInbox.tokenUri | string | `"https://some.example.com/webidp2/connect/token"` | The URI from where to fetch the auth token for the online service inbox |
| podAnnotations | object | `{}` | Annotations to add to the pod |
| podLabels | object | `{}` | Labels to add to the pod |
| podSecurityContext | object | `{}` | Security context for the pod |
| postgresql | object | `{"auth":{"database":"digifischdok","password":"digifischdok","rootPassword":"postgres","username":"digifischdok"},"enabled":false}` | PostgreSQL chart configuration Note: The PostgreSQL subchart feature has been removed. This section is kept for backward compatibility but has no effect. Use externalDatabase configuration instead. |
| postgresql.auth | object | `{"database":"digifischdok","password":"digifischdok","rootPassword":"postgres","username":"digifischdok"}` | Authentication details for PostgreSQL (not used as subchart feature has been removed) |
| postgresql.enabled | bool | `false` | Whether to enable the bundled PostgreSQL chart (always false as feature has been removed) |
| properties | object | `{"SPRING_PROFILES_ACTIVE":"default"}` | Spring specific configuration. These Properties are commonly set up in application.properties or applications.yaml files... |
| properties.SPRING_PROFILES_ACTIVE | string | `"default"` | The active Spring profiles. (default: application.yaml; dev: application-dev.yaml). |
| readinessProbe | object | `{"httpGet":{"path":"/api/actuator/health/readiness","port":"http"},"initialDelaySeconds":60,"periodSeconds":1}` | Readiness probe configuration. Readiness probe is used to check if this app is ready to serve traffic. |
| readinessProbe.httpGet | object | `{"path":"/api/actuator/health/readiness","port":"http"}` | The HTTP path for the readiness probe |
| readinessProbe.initialDelaySeconds | int | `60` | The initial delay before the readiness probe is initiated |
| readinessProbe.periodSeconds | int | `1` | The frequency at which the probe is performed |
| replicaCount | int | `1` | The number of replicas for the service |
| resources | object | `{}` | Resource limits and requests |
| s3 | object | `{"accessKey":"example-access-key-id","bucket":"example-bucket","endpoint":"https://example.com","region":"eu-central-1","secretKey":"example-secret-access-key"}` | S3 configuration |
| s3.accessKey | string | `"example-access-key-id"` | The access key ID for the S3 bucket |
| s3.bucket | string | `"example-bucket"` | The name of the S3 bucket |
| s3.endpoint | string | `"https://example.com"` | The endpoint of the S3 bucket |
| s3.region | string | `"eu-central-1"` | The region of the S3 bucket |
| s3.secretKey | string | `"example-secret-access-key"` | The secret access key for the S3 bucket |
| securityContext | object | `{"fsGroup":1001,"runAsGroup":1001,"runAsNonRoot":true,"runAsUser":1001}` | Security context for the container |
| service | object | `{"port":8080,"type":"ClusterIP"}` | Service configuration |
| service.port | int | `8080` | The port that the service exposes |
| service.type | string | `"ClusterIP"` | The type of service to create |
| serviceAccount | object | `{"annotations":{},"automount":true,"create":true,"name":""}` | Service account configuration |
| serviceAccount.annotations | object | `{}` | Annotations to add to the service account |
| serviceAccount.automount | bool | `true` | Whether to automatically mount the service account's API credentials |
| serviceAccount.create | bool | `true` | Specifies whether a service account should be created |
| serviceAccount.name | string | `""` | The name of the service account to use If not set and `create` is `true`, a name is generated using the fullname template |
| tolerations | list | `[]` | Tolerations configuration |
| volumeMounts | list | `[]` | Additional volumeMounts on the output Deployment definition. |
| volumes | list | `[]` | Additional volumes on the output Deployment definition. |

----------------------------------------------
Autogenerated from chart metadata using [helm-docs v1.13.1](https://github.com/norwoodj/helm-docs/releases/v1.13.1)
