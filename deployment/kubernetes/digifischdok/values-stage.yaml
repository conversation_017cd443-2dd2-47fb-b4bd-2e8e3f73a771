register-service:
  imagePullSecrets:
    - name: dfd-oci-registry

  ingress:
    enabled: true
    hosts:
      - host: digifischdok-stage-cluster.dsecurecloud.de
        paths:
          - path: /api
            pathType: Prefix

  externalDatabase:
    host: digifischdok-stage-postgres-lb.dsc.dataport.de
    port: 5433
    user: cluster_user
    password: ${STAGE_DB_PASSWORD}
    databaseName: digifischdok_stage_register

  externalKeycloak:
    issuerUri: "https://digifischdok-iam.dsecurecloud.de:8443/realms/digifischdok_stage"
    clientId: "backend"
    clientSecret: ${KC_CLIENT_SECRET_STAGE}

  osInbox:
    tokenUri: "https://idp.serviceportal-stage.gemeinsamonline.de/webidp2/connect/token"
    clientId: "urn:digifischdok:stage"
    clientSecret: "${INBOX_CLIENT_SECRET_STAGE}"
    clientScope: "access_urn:dataport:od:digifischdok:stage:go:DigiFischDok,default"
    resource: "urn:dataport:osi:postfach:rz2:stage:go"
    baseUrn: "https://api-gateway-stage.dataport.de:443"

  s3:
    endpoint: https://dstoragecloud.dataport.de:9021
    bucket: dsec-kd9pvjm7cgg89y4dsayubhpqdyc
    region: eu-central-1
    accessKey: ${S3_ACCESS_KEY}
    secretKey: ${S3_SECRET_KEY}

  externalMail:
    host: ************
    port: 25
    user: <EMAIL>
    password: none
    from: <EMAIL>

  properties:
    SPRING_PROFILES_ACTIVE: default,stage

  javaArgs:
    - "-Dhttp.proxyHost=${DATAPORT_PROXY_HOST}"
    - "-Dhttp.proxyPort=${DATAPORT_PROXY_PORT}"
    - "-Dhttps.proxyHost=${DATAPORT_PROXY_HOST}"
    - "-Dhttps.proxyPort=${DATAPORT_PROXY_PORT}"
    - "-Dhttp.nonProxyHosts=localhost|*.dsc.dataport.de|dstoragecloud.dataport.de"

  resources:
    requests:
      cpu: 1000m
      memory: 4Gi
    limits:
      cpu: 10000m
      memory: 4Gi

  networkPolicy:
    enabled: true
    podSelector:
      matchLabels:
        app.kubernetes.io/name: register-service
    ingress:
      - from:
          - namespaceSelector:
              matchLabels:
                kubernetes.io/metadata.name: kube-system
            podSelector:
              matchLabels:
                app.kubernetes.io/component: controller
                app.kubernetes.io/name: rke2-ingress-nginx
        ports:
          - port: 8080
            protocol: TCP
    egress:
      - ports:
          - port: 3128
            protocol: TCP
          - port: 5433
            protocol: TCP
          - port: 25
            protocol: TCP
          - port: 9021
            protocol: TCP
        to:
          - ipBlock:
              cidr: 10.0.0.0/8
      - ports:
          - port: 53
            protocol: UDP
        to:
          - namespaceSelector:
              matchLabels:
                kubernetes.io/metadata.name: kube-system
            podSelector:
              matchLabels:
                k8s-app: kube-dns

web-app:
  imagePullSecrets:
    - name: dfd-oci-registry

  ingress:
    enabled: true
    hosts:
      - host: digifischdok-stage-cluster.dsecurecloud.de
        paths:
          - path: /
            pathType: Prefix

  properties:
    API_URL: https://digifischdok-stage-cluster.dsecurecloud.de/api
    WEB_URL: https://digifischdok-stage-cluster.dsecurecloud.de
    KEYCLOAK_URL: https://digifischdok-iam.dsecurecloud.de:8443
    KEYCLOAK_CLIENT_ID: frontend
    KEYCLOAK_REALM: digifischdok_stage
    SELF_SERVICE_PASSWORD_CHANGE_LINK: https://pws.dataport.de/request-password-change
    ENVIRONMENT: stage

  resources:
    requests:
      cpu: 100m
      memory: 128Mi
    limits:
      cpu: 1000m
      memory: 128Mi

  networkPolicy:
    enabled: true
    defaultDenyEgress: true
    podSelector:
      matchLabels:
        app.kubernetes.io/name: web-app
    ingress:
      - from:
          - namespaceSelector:
              matchLabels:
                kubernetes.io/metadata.name: kube-system
            podSelector:
              matchLabels:
                app.kubernetes.io/component: controller
                app.kubernetes.io/name: rke2-ingress-nginx
        ports:
          # Use port 8080 because rootless nginx cannot run on port 80
          - port: 8080
            protocol: TCP