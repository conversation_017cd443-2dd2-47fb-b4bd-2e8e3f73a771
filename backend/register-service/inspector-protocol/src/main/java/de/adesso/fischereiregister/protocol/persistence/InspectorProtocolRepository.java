package de.adesso.fischereiregister.protocol.persistence;

import de.adesso.fischereiregister.protocol.persistence.result.model.ActiveInspectorsResult;
import de.adesso.fischereiregister.protocol.persistence.result.model.NumberOfInspectionsResult;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.UUID;

/**
 * Repository interface for managing Protocol entities.
 * This interface extends CrudRepository to provide basic CRUD operations.
 */
@Component
@Repository
public interface InspectorProtocolRepository extends CrudRepository<InspectorProtocol, UUID> {


    /**
     * Retrieves the number of active inspectors per year and federal state.
     *
     * @return a list of DistinctInspectorsResult containing the number of active inpectors, year, and federal state.
     */
    @Query("""
         SELECT new de.adesso.fischereiregister.protocol.persistence.result.model.ActiveInspectorsResult(
             CAST(COUNT(DISTINCT ip.inspectorUserId) AS INTEGER), 
             CAST(EXTRACT(YEAR FROM ip.inspectionTimestamp) AS INTEGER)
         )
         FROM InspectorProtocol ip
         WHERE (:federalState IS NULL OR ip.inspectorFederalState = :federalState)
            AND (:years IS NULL OR CAST(EXTRACT(YEAR FROM ip.inspectionTimestamp) AS INTEGER) IN :years)
         GROUP BY EXTRACT(YEAR FROM ip.inspectionTimestamp)
        """)
    List<ActiveInspectorsResult> selectActiveInspectors(@Param("years") List<Integer> years, @Param("federalState") String federalState);


    /**
     * Retrieves the number of inspections per year and federal state, optionally filtered by federal state and years.
     *
     * @param federalState The federal state to filter by (optional).
     * @param years The list of years to filter by (optional).
     * @return a list of NumberOfInspectionsResult containing the number of inspections, year, and federal state.
     */
    @Query("""
     SELECT new de.adesso.fischereiregister.protocol.persistence.result.model.NumberOfInspectionsResult(
         CAST(COUNT(*) AS INTEGER),
         CAST(EXTRACT(YEAR FROM ip.inspectionTimestamp) AS INTEGER)
     )
     FROM InspectorProtocol ip
     WHERE (:federalState IS NULL OR ip.inspectorFederalState = :federalState)
        AND (:years IS NULL OR CAST(EXTRACT(YEAR FROM ip.inspectionTimestamp) AS INTEGER) IN :years)
     GROUP BY EXTRACT(YEAR FROM ip.inspectionTimestamp)
    """)
    List<NumberOfInspectionsResult> selectNumberOfInspections(@Param("years") List<Integer> years, @Param("federalState") String federalState);

    /**
     * Retrieves all distinct years for which inspection data is available.
     *
     * @return A list of distinct years sorted in descending order.
     */
    @Query("""
     SELECT DISTINCT CAST(EXTRACT(YEAR FROM ip.inspectionTimestamp) AS INTEGER)
     FROM InspectorProtocol ip
     ORDER BY CAST(EXTRACT(YEAR FROM ip.inspectionTimestamp) AS INTEGER) DESC
    """)
    List<Integer> findDistinctYears();
}
