package de.adesso.fischereiregister.message.service.model;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@EqualsAndHashCode
public class OSBaseMessage {
    private String subject;
    private String body;
    private String displayName;
    private String originSender;
    private String replyAction; // Could be an enum if there are fixed values like "Replypossible"
    private String eidasLevel;  // Could be an enum if there are fixed values like "Low"
    private boolean isObligatory;
    private String sequenceNumber;
    private boolean isHtml;

    public OSBaseMessage(String subject, String displayName, String body) {
        this.subject = subject;
        this.displayName = displayName;
        this.body = body;
    }

    public OSBaseMessage(String subject, String displayName) {
        this.subject = subject;
        this.displayName = displayName;
    }
}
