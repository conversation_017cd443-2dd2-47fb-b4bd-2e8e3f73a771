package de.adesso.fischereiregister.message.service.model;

import lombok.EqualsAndHashCode;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

@Getter
@EqualsAndHashCode(callSuper = true)
public class OSMessageWithAttachments extends OSBaseMessage {
    private List<String> references = new ArrayList<>();

    public OSMessageWithAttachments(String subject, String diplayName, String body) {
        super(subject, diplayName, body);
    }
}

