package de.adesso.fischereiregister.message.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import de.adesso.fischereiregister.message.service.model.OSAttachement;
import de.adesso.fischereiregister.message.service.model.OSMessage;
import de.adesso.fischereiregister.message.service.model.OSMessageWithAttachments;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.MediaType;
import org.springframework.http.client.MultipartBodyBuilder;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class OSInboxServiceImpl implements OSInboxService {

    private static final String API_OSI_POSTFACH_1_0_0_MESSAGE_EXCHANGE_V_1 = "/api/osi_postfach/1.0.0/MessageExchange/v1/";
    public static final String EIDAS_LEVEL_LOW = "Low";
    public static final String REPLYFORBIDDEN = "Replyforbidden";
    private static final boolean HTML_EMAIL_FALSE = false;
    public static final String SEQUENCE_NUMBER = "0";

    private final WebClient webClient;

    public OSInboxServiceImpl(@Qualifier("messageServiceWebClient") WebClient webClient) {
        this.webClient = webClient;
    }

    @Override
    public void sendMessageWithAttachments(String inboxReference, OSMessageWithAttachments osMessageWithAttachments, List<OSAttachement> osAttachements) throws JsonProcessingException {
        prepareMessageDefaults(osMessageWithAttachments);

        final ObjectMapper objectMapper = new ObjectMapper();

        List<ByteArrayResource> fileResources = osAttachements.stream()
                .map(attachement -> new ByteArrayResource(attachement.getContent()) {
                    @Override
                    public String getFilename() {
                        return attachement.getFileName();
                    }
                })
                .collect(Collectors.toList());

        MultipartBodyBuilder builder = new MultipartBodyBuilder();

        for (ByteArrayResource resource : fileResources) {
            String filename = Objects.requireNonNullElse(resource.getFilename(), "default.pdf");

            builder.part("fileList", resource)
                    .contentType(MediaType.APPLICATION_PDF)
                    .filename(filename);
        }

        builder.part("sendRequest", objectMapper.writeValueAsString(osMessageWithAttachments))
                .contentType(MediaType.APPLICATION_JSON);

        webClient.post()
                .uri(API_OSI_POSTFACH_1_0_0_MESSAGE_EXCHANGE_V_1 + "Send/MessageWithAttachments/" + inboxReference)
                .contentType(MediaType.MULTIPART_FORM_DATA)
                .body(BodyInserters.fromMultipartData(builder.build())) // Correct way to send multipart data
                .retrieve()
                .onStatus(
                        HttpStatusCode::isError,
                        this::handleError)
                .bodyToMono(String.class)
                .doOnError(e -> log.error("OSMessageWithAttachments sending failed" + " - " + e.getMessage(), e))
                .subscribe(result -> log.info("OSMessageWithAttachments sent successfully" + " - " + result));
    }

    @Override
    public void sendMessage(String inboxReference, OSMessage osMessage) {
        prepareMessageDefaults(osMessage);

        webClient.post()
                .uri(API_OSI_POSTFACH_1_0_0_MESSAGE_EXCHANGE_V_1 + "Send/" + inboxReference)
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(osMessage)
                .retrieve()
                .onStatus(
                        HttpStatusCode::isError,
                        this::handleError)
                .bodyToMono(String.class)
                .doOnError(e -> log.error("OSMessage sending failed" + " - " + e.getMessage(), e))
                .subscribe(result -> log.info("OSMessage sent successfully" + " - " + result));
    }

    private void prepareMessageDefaults(OSMessage message) {
        message.setEidasLevel(EIDAS_LEVEL_LOW);
        message.setReplyAction(REPLYFORBIDDEN);
        message.setHtml(HTML_EMAIL_FALSE);
        message.setSequenceNumber(SEQUENCE_NUMBER);
        message.setObligatory(false);
    }

    private void prepareMessageDefaults(OSMessageWithAttachments message) {
        message.setEidasLevel(EIDAS_LEVEL_LOW);
        message.setReplyAction(REPLYFORBIDDEN);
        message.setHtml(HTML_EMAIL_FALSE);
        message.setObligatory(false);
        message.setSequenceNumber(SEQUENCE_NUMBER);
    }

    Mono<Throwable> handleError(org.springframework.web.reactive.function.client.ClientResponse response) {
        return response.bodyToMono(String.class)
                .flatMap(errorBody -> {
                    String message = "Error occurred: " + response.statusCode();
                    log.error(message + " - Details: " + errorBody);
                    return Mono.error(new RuntimeException("Error: " + errorBody));
                });
    }

}

