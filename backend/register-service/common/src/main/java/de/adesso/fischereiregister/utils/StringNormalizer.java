package de.adesso.fischereiregister.utils;

import org.apache.commons.lang3.StringUtils;

public class StringNormalizer {
    private StringNormalizer() {
        throw new UnsupportedOperationException("Utility class should not be instantiated");
    }

    public static String normalize(String input) {
        if (input == null) {
            return null;
        }

        return StringUtils.stripAccents(input)
                .trim() // trim unnecessary trailing whitespaces
                .replace("-", " ") // there is German names with hyphens
                .replaceAll("\\s+", " ") // Ensure that all whitespaces are single spaces
                .toLowerCase();
    }
}
