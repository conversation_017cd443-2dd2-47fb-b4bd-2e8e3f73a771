package de.adesso.fischereiregister.utils;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;

class HashUtilsTest {

    private static final int SALT_SUBSTRING_LENGTH = 10;

    @Test
    @DisplayName("HashUtils.sha512 should return the correct SHA-512 hash for the input")
    void testSha512() {
        String input = "b77c4f0b-62b6-4884-b078-4e1e5f872193;Ich1;Darfalles1;13.08.1997;e3e939b348";
        String expectedOutput = "3a9c7d7908b470ab30484adfe43fe9d007f503928939bf871b44161df00185064c692932b4cb65cf728102f4ffc0eb404a1cb8555484c964bf99224e9f38d53d";

        String actualOutput = HashUtils.sha512(input);

        assertEquals(expectedOutput, actualOutput);
    }

    @Test
    @DisplayName("HashUtils.gensalt should generate a salt of the correct length")
    void testGensaltLength() {
        String salt = HashUtils.gensalt();

        // Check that the salt is the expected length
        assertThat(salt).hasSize(SALT_SUBSTRING_LENGTH);
    }

    @Test
    @DisplayName("HashUtils.gensalt should generate a salt containing valid characters")
    void testGensaltCharacters() {
        String salt = HashUtils.gensalt();

        // Check that the salt is a valid BCrypt salt substring (contains only characters in [./A-Za-z0-9])
        assertThat(salt).matches("^[./A-Za-z0-9]+$");
    }
}
