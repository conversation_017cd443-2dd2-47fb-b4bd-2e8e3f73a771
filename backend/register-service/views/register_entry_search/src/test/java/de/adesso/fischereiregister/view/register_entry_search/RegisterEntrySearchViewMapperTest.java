package de.adesso.fischereiregister.view.register_entry_search;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import de.adesso.fischereiregister.view.register_entry_search.mapper.RegisterEntrySearchViewMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;
import org.openapitools.model.SearchItem;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

class RegisterEntrySearchViewMapperTest {

    private RegisterEntrySearchViewMapper mapper;

    @BeforeEach
    void setUp() {
        mapper = Mappers.getMapper(RegisterEntrySearchViewMapper.class);
    }

    @DisplayName("Test that the converted data from the search table have the correct format")
    @Test
    void testMapData() throws JsonProcessingException {
        // given
        SearchItem searchItem = new SearchItem();
        searchItem.setRegisterId("1234");

        // when
        String jsonResult = mapper.mapData(searchItem);
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule());
        String expectedJson = objectMapper.writeValueAsString(searchItem);

        // then
        assertNotNull(jsonResult);
        assertEquals(expectedJson, jsonResult);
    }
}
