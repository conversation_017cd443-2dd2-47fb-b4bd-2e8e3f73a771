package de.adesso.fischereiregister.view.register_entry_search.converter;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.deser.std.StdDeserializer;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import de.adesso.fischereiregister.core.model.Birthdate;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import org.openapitools.model.SearchItem;

import java.io.IOException;

@Converter
public class SearchRegisterEntryConverter implements AttributeConverter<SearchItem, String> {

    private final ObjectMapper objectMapper;

    public SearchRegisterEntryConverter(ObjectMapper objectMapper) {

        final StdSerializer<Birthdate> birthdateSerializer = new StdSerializer<>(Birthdate.class) {
            @Override
            public void serialize(Birthdate value, JsonGenerator jGen, SerializerProvider provider) throws IOException {
                jGen.writeString(value.toString());
            }
        };

        final StdDeserializer<Birthdate> birthdateDeserializer = new StdDeserializer<>(Birthdate.class) {
            @Override
            public Birthdate deserialize(JsonParser jp, DeserializationContext ctxt) throws IOException {
                return Birthdate.parse(jp.getText());
            }
        };

        final SimpleModule module = new SimpleModule()
                .addSerializer(birthdateSerializer)
                .addDeserializer(Birthdate.class, birthdateDeserializer);

        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.registerModule(module);
        this.objectMapper = objectMapper;

    }

    @Override
    public String convertToDatabaseColumn(SearchItem attribute) throws RuntimeException {
        try {
            return objectMapper.writeValueAsString(attribute);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Failed to serialize SearchRegisterEntry to JSON", e);
        }
    }

    @Override
    public SearchItem convertToEntityAttribute(String dbData) throws RuntimeException {
        try {
            return objectMapper.readValue(dbData, SearchItem.class);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Failed to deserialize JSON to SearchRegisterEntry", e);
        }
    }
}
