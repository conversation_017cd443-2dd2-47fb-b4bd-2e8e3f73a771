package de.adesso.fischereiregister.view.register_entry_search;

import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.QualificationsProof;
import lombok.Builder;
import lombok.Getter;

import java.util.UUID;

@Getter
@Builder
public class RegisterEntrySearchViewServiceParameters {
    private final UUID registerEntryId;
    private final Person person;
    private final FishingLicense license;
    private final QualificationsProof certificate;
}
