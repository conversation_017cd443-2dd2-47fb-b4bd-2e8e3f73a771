package de.adesso.fischereiregister.view.ban.services;

import de.adesso.fischereiregister.core.model.Ban;
import de.adesso.fischereiregister.view.ban.persistence.BanView;
import de.adesso.fischereiregister.view.ban.persistence.InMemoryBanViewRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(MockitoExtension.class)
class BanViewServiceImplTest {

    private static final UUID REGISTER_ENTRY_ID = UUID.randomUUID();
    private static final UUID BAN_ID = UUID.randomUUID();
    private static final String FILE_NUMBER = "FN-12345";
    private static final String REPORTED_BY = "John Doe";
    private static final LocalDate AT_DATE = LocalDate.of(2023, 1, 1);
    private static final LocalDate FROM_DATE = LocalDate.of(2023, 1, 1);
    private static final LocalDate TO_DATE = LocalDate.of(2023, 12, 31);

    private InMemoryBanViewRepository repository;

    private BanViewServiceImpl service;

    private Ban ban;

    @BeforeEach
    void setUp() {
        repository = new InMemoryBanViewRepository();
        service = new BanViewServiceImpl(repository);

        ban = new Ban();
        ban.setBanId(BAN_ID);
        ban.setFileNumber(FILE_NUMBER);
        ban.setReportedBy(REPORTED_BY);
        ban.setAt(AT_DATE);
        ban.setFrom(FROM_DATE);
        ban.setTo(TO_DATE);
    }

    @Test
    @DisplayName("BanViewServiceImpl.deleteAll should call repository.deleteAll")
    void deleteAll_ShouldCallRepositoryDeleteAll() {
        // When
        service.deleteAll();

        // Then
        List<BanView> allBans = repository.findAll();
        assertTrue(allBans.isEmpty());
    }

    @Test
    @DisplayName("BanViewServiceImpl.create should save BanView with correct data")
    void create_ShouldSaveBanViewWithCorrectData() {
        // Given
        String federalState = "BY";

        // When
        service.create(REGISTER_ENTRY_ID, ban, federalState);

        // Then
        Optional<BanView> savedBanView = repository.findById(REGISTER_ENTRY_ID);

        assertTrue(savedBanView.isPresent());
        assertEquals(REGISTER_ENTRY_ID, savedBanView.get().getRegisterEntryId());
        assertEquals(BAN_ID, savedBanView.get().getBanId());
        assertEquals(FILE_NUMBER, savedBanView.get().getFileNumber());
        assertEquals(REPORTED_BY, savedBanView.get().getReportedBy());
        assertEquals(AT_DATE, savedBanView.get().getAt());
        assertEquals(FROM_DATE, savedBanView.get().getFrom());
        assertEquals(TO_DATE, savedBanView.get().getTo());
        assertEquals(federalState, savedBanView.get().getFederalState());
    }

    @Test
    @DisplayName("BanViewServiceImpl.deleteByRegisterEntryId should call repository.deleteById with correct ID")
    void deleteByRegisterEntryId_ShouldCallRepositoryDeleteById() {
        // When
        service.deleteByRegisterEntryId(REGISTER_ENTRY_ID);

        // Then
        Optional<BanView> result = repository.findById(BAN_ID);
        assertFalse(result.isPresent());
    }

    @Test
    @DisplayName("BanViewServiceImpl.findRegisterEntryIdsWithExpiredBans should return list from repository")
    void findRegisterEntryIdsWithExpiredBans_ShouldReturnListFromRepository() {
        // Given
        UUID expiredBanRegisterEntryId = UUID.randomUUID();

        BanView expiredBanView = new BanView();
        expiredBanView.setRegisterEntryId(expiredBanRegisterEntryId);
        expiredBanView.setBanId(BAN_ID);
        expiredBanView.setTo(LocalDate.now().minusDays(1));
        repository.save(expiredBanView);

        // When
        List<UUID> result = service.findRegisterEntryIdsWithExpiredBans();

        // Then
        assertEquals(1, result.size());
        assertEquals(List.of(expiredBanRegisterEntryId), result);
    }

    @Test
    @DisplayName("BanViewServiceImpl.getAvailableYears should return years from ban data")
    void getAvailableYears_ShouldReturnYearsFromBanData() {
        // Given
        BanView ban2023 = new BanView();
        ban2023.setRegisterEntryId(UUID.randomUUID());
        ban2023.setBanId(UUID.randomUUID());
        ban2023.setAt(LocalDate.of(2023, 5, 15));
        ban2023.setFrom(LocalDate.of(2023, 6, 1));
        repository.save(ban2023);

        BanView ban2024 = new BanView();
        ban2024.setRegisterEntryId(UUID.randomUUID());
        ban2024.setBanId(UUID.randomUUID());
        ban2024.setAt(LocalDate.of(2024, 3, 10));
        ban2024.setFrom(LocalDate.of(2024, 4, 1));
        repository.save(ban2024);

        // When
        List<Integer> result = service.getAvailableYears();

        // Then
        assertEquals(2, result.size());
        assertTrue(result.contains(2023));
        assertTrue(result.contains(2024));
    }

    @Test
    @DisplayName("BanViewServiceImpl.getIssuedAmountByFederalStateAndYear should return issued count")
    void getIssuedAmountByFederalStateAndYear_ShouldReturnIssuedCount() {
        // Given
        BanView ban1 = new BanView();
        ban1.setRegisterEntryId(UUID.randomUUID());
        ban1.setBanId(UUID.randomUUID());
        ban1.setFederalState("BY");
        ban1.setAt(LocalDate.of(2023, 5, 15));
        repository.save(ban1);

        BanView ban2 = new BanView();
        ban2.setRegisterEntryId(UUID.randomUUID());
        ban2.setBanId(UUID.randomUUID());
        ban2.setFederalState("BY");
        ban2.setAt(LocalDate.of(2023, 8, 20));
        repository.save(ban2);

        BanView ban3 = new BanView();
        ban3.setRegisterEntryId(UUID.randomUUID());
        ban3.setBanId(UUID.randomUUID());
        ban3.setFederalState("HE");
        ban3.setAt(LocalDate.of(2023, 3, 10));
        repository.save(ban3);

        // When
        Integer result = service.getIssuedAmountByFederalStateAndYear("BY", 2023);

        // Then
        assertEquals(2, result);
    }

    @Test
    @DisplayName("BanViewServiceImpl.getIssuedAmountByFederalStateAndYear should return 0 for non-existing data")
    void getIssuedAmountByFederalStateAndYear_ShouldReturn0ForNonExistingData() {
        // When
        Integer result = service.getIssuedAmountByFederalStateAndYear("NonExisting", 2023);

        // Then
        assertEquals(0, result);
    }

    @Test
    @DisplayName("BanViewServiceImpl.getActiveBansAmount should count active bans")
    void getActiveBansAmount_ShouldCountActiveBans() {
        // Given
        LocalDate today = LocalDate.now();

        // Active ban (started yesterday, ends tomorrow)
        BanView activeBan = new BanView();
        activeBan.setRegisterEntryId(UUID.randomUUID());
        activeBan.setBanId(UUID.randomUUID());
        activeBan.setFrom(today.minusDays(1));
        activeBan.setTo(today.plusDays(1));
        repository.save(activeBan);

        // Expired ban
        BanView expiredBan = new BanView();
        expiredBan.setRegisterEntryId(UUID.randomUUID());
        expiredBan.setBanId(UUID.randomUUID());
        expiredBan.setFrom(today.minusDays(10));
        expiredBan.setTo(today.minusDays(1));
        repository.save(expiredBan);

        // Future ban
        BanView futureBan = new BanView();
        futureBan.setRegisterEntryId(UUID.randomUUID());
        futureBan.setBanId(UUID.randomUUID());
        futureBan.setFrom(today.plusDays(1));
        futureBan.setTo(today.plusDays(10));
        repository.save(futureBan);

        // When
        Integer result = service.getActiveBansAmount();

        // Then
        assertEquals(1, result);
    }

    @Test
    @DisplayName("BanViewServiceImpl.getActiveBansAmountByFederalState should count active bans for federal state")
    void getActiveBansAmountByFederalState_ShouldCountActiveBansForFederalState() {
        // Given
        LocalDate today = LocalDate.now();
        String federalState = "BY";

        // Active ban in Bayern
        BanView activeBanBayern = new BanView();
        activeBanBayern.setRegisterEntryId(UUID.randomUUID());
        activeBanBayern.setBanId(UUID.randomUUID());
        activeBanBayern.setFederalState(federalState);
        activeBanBayern.setFrom(today.minusDays(1));
        activeBanBayern.setTo(today.plusDays(1));
        repository.save(activeBanBayern);

        // Active ban in different state
        BanView activeBanOther = new BanView();
        activeBanOther.setRegisterEntryId(UUID.randomUUID());
        activeBanOther.setBanId(UUID.randomUUID());
        activeBanOther.setFederalState("HE");
        activeBanOther.setFrom(today.minusDays(1));
        activeBanOther.setTo(today.plusDays(1));
        repository.save(activeBanOther);

        // When
        Integer result = service.getActiveBansAmountByFederalState(federalState);

        // Then
        assertEquals(1, result);
    }

    @Test
    @DisplayName("BanViewServiceImpl.getIssuedAmountByYear should count all issued bans in year")
    void getIssuedAmountByYear_ShouldCountAllIssuedBansInYear() {
        // Given
        BanView ban1 = new BanView();
        ban1.setRegisterEntryId(UUID.randomUUID());
        ban1.setBanId(UUID.randomUUID());
        ban1.setFederalState("BY");
        ban1.setAt(LocalDate.of(2023, 5, 15));
        repository.save(ban1);

        BanView ban2 = new BanView();
        ban2.setRegisterEntryId(UUID.randomUUID());
        ban2.setBanId(UUID.randomUUID());
        ban2.setFederalState("HE");
        ban2.setAt(LocalDate.of(2023, 8, 20));
        repository.save(ban2);

        BanView ban3 = new BanView();
        ban3.setRegisterEntryId(UUID.randomUUID());
        ban3.setBanId(UUID.randomUUID());
        ban3.setFederalState("BY");
        ban3.setAt(LocalDate.of(2024, 3, 10));
        repository.save(ban3);

        // When
        Integer result = service.getIssuedAmountByYear(2023);

        // Then
        assertEquals(2, result);
    }

    @Test
    @DisplayName("BanViewServiceImpl.updateFederalState should update federal state when ban exists")
    void updateFederalState_ShouldUpdateFederalStateWhenBanExists() {
        // Given
        String originalFederalState = "BY";
        String newFederalState = "HE";

        // Create and save a ban view
        service.create(REGISTER_ENTRY_ID, ban, originalFederalState);

        // Verify initial state
        Optional<BanView> initialBanView = repository.findById(REGISTER_ENTRY_ID);
        assertTrue(initialBanView.isPresent());
        assertEquals(originalFederalState, initialBanView.get().getFederalState());

        // When
        service.updateFederalState(REGISTER_ENTRY_ID, newFederalState);

        // Then
        Optional<BanView> updatedBanView = repository.findById(REGISTER_ENTRY_ID);
        assertTrue(updatedBanView.isPresent());
        assertEquals(newFederalState, updatedBanView.get().getFederalState());

        // Verify other fields remain unchanged
        assertEquals(BAN_ID, updatedBanView.get().getBanId());
        assertEquals(FILE_NUMBER, updatedBanView.get().getFileNumber());
        assertEquals(REPORTED_BY, updatedBanView.get().getReportedBy());
        assertEquals(AT_DATE, updatedBanView.get().getAt());
        assertEquals(FROM_DATE, updatedBanView.get().getFrom());
        assertEquals(TO_DATE, updatedBanView.get().getTo());
    }

    @Test
    @DisplayName("BanViewServiceImpl.updateFederalState should throw error when ban does not exist")
    void updateFederalState_ShouldDoNothingWhenBanDoesNotExist() {
        // Given
        UUID nonExistentRegisterEntryId = UUID.randomUUID();
        String newFederalState = "HE";

        // When & Then
        assertThrows(IllegalStateException.class, () -> service.updateFederalState(nonExistentRegisterEntryId, newFederalState), "Ban not found for register entry id: " + nonExistentRegisterEntryId);

    }
}