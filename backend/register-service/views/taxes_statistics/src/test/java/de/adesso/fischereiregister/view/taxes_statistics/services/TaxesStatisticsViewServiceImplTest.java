package de.adesso.fischereiregister.view.taxes_statistics.services;

import de.adesso.fischereiregister.core.model.type.SubmissionType;
import de.adesso.fischereiregister.view.taxes_statistics.persistence.InMemoryTaxesStatisticsViewRepository;
import de.adesso.fischereiregister.view.taxes_statistics.persistence.TaxesStatisticsView;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

class TaxesStatisticsViewServiceImplTest {

    private InMemoryTaxesStatisticsViewRepository repository;
    private TaxesStatisticsViewService taxesStatisticsViewService;

    private static final String FEDERAL_STATE_SH = "SH";
    private static final String FEDERAL_STATE_NW = "NW";
    private static final String OFFICE_1 = "office1";
    private static final String OFFICE_2 = "office2";
    private static final SubmissionType SOURCE_ANALOG = SubmissionType.ANALOG;
    private static final SubmissionType SOURCE_ONLINE = SubmissionType.ONLINE;
    private static final int DURATION_1 = 1;
    private static final int DURATION_3 = 3;
    private static final int DURATION_5 = 5;
    private static final int YEAR_2022 = 2022;
    private static final int YEAR_2023 = 2023;
    private static final int YEAR_2024 = 2024;
    private static final double REVENUE_50 = 50.0;
    private static final double REVENUE_100 = 100.0;
    private static final double REVENUE_200 = 200.0;
    private static final double REVENUE_150 = 150.0;
    private static final double REVENUE_250 = 250.0;
    private static final long ID_1 = 1L;
    private static final long ID_2 = 2L;
    private static final long ID_3 = 3L;
    private static final long ID_4 = 4L;

    @BeforeEach
    void setUp() {
        repository = new InMemoryTaxesStatisticsViewRepository();
        taxesStatisticsViewService = new TaxesStatisticsViewServiceImpl(repository);
    }

    @Test
    @DisplayName("TaxesStatisticsViewService.updateOrCreateStatistic should create a new entry when none exists")
    void updateOrCreateStatistic_createsNewEntry() {
        // When
        taxesStatisticsViewService.updateOrCreateStatistic(
                FEDERAL_STATE_SH, OFFICE_1, SOURCE_ANALOG, DURATION_1, YEAR_2024, REVENUE_50);

        // Then
        Optional<TaxesStatisticsView> result = repository.findByFederalStateAndOfficeAndSourceAndDurationAndYear(
                FEDERAL_STATE_SH, OFFICE_1, SOURCE_ANALOG, DURATION_1, YEAR_2024);

        assertTrue(result.isPresent());
        TaxesStatisticsView view = result.get();
        assertNotNull(view.getId());
        assertEquals(1, view.getCount());
        assertEquals(REVENUE_50, view.getRevenue());
        assertEquals(FEDERAL_STATE_SH, view.getFederalState());
        assertEquals(OFFICE_1, view.getOffice());
        assertEquals(SOURCE_ANALOG, view.getSource());
        assertEquals(DURATION_1, view.getDuration());
        assertEquals(YEAR_2024, view.getYear());
    }

    @Test
    @DisplayName("TaxesStatisticsViewService.updateOrCreateStatistic should update existing entry by incrementing count and adding revenue")
    void updateOrCreateStatistic_updatesExistingEntry() {
        // Given
        TaxesStatisticsView existingEntry = createTaxView(
                ID_1, FEDERAL_STATE_SH, OFFICE_1, SOURCE_ANALOG,
                YEAR_2024, DURATION_1, 5, REVENUE_50);
        repository.save(existingEntry);

        // When
        taxesStatisticsViewService.updateOrCreateStatistic(
                FEDERAL_STATE_SH, OFFICE_1, SOURCE_ANALOG, DURATION_1, YEAR_2024, REVENUE_50);

        // Then
        Optional<TaxesStatisticsView> result = repository.findByFederalStateAndOfficeAndSourceAndDurationAndYear(
                FEDERAL_STATE_SH, OFFICE_1, SOURCE_ANALOG, DURATION_1, YEAR_2024);

        assertTrue(result.isPresent());
        assertEquals(6, result.get().getCount());
        assertEquals(REVENUE_50 + REVENUE_50, result.get().getRevenue());
    }

    @Test
    @DisplayName("TaxesStatisticsViewService.updateOrCreateStatistic should handle different durations correctly")
    void updateOrCreateStatistic_handlesDifferentDurations() {
        // When
        taxesStatisticsViewService.updateOrCreateStatistic(
                FEDERAL_STATE_SH, OFFICE_1, SOURCE_ANALOG, DURATION_1, YEAR_2024, REVENUE_50);

        // Then
        Optional<TaxesStatisticsView> result = repository.findByFederalStateAndOfficeAndSourceAndDurationAndYear(
                FEDERAL_STATE_SH, OFFICE_1, SOURCE_ANALOG, DURATION_1, YEAR_2024);
        assertTrue(result.isPresent());
        assertEquals(DURATION_1, result.get().getDuration());
    }

    @Test
    @DisplayName("TaxesStatisticsViewService.updateOrCreateStatistic should handle different sources correctly")
    void updateOrCreateStatistic_handlesDifferentSources() {
        // When
        taxesStatisticsViewService.updateOrCreateStatistic(
                FEDERAL_STATE_SH, OFFICE_1, SOURCE_ANALOG, DURATION_1, YEAR_2024, REVENUE_50);

        // Then
        Optional<TaxesStatisticsView> result = repository.findByFederalStateAndOfficeAndSourceAndDurationAndYear(
                FEDERAL_STATE_SH, OFFICE_1, SOURCE_ANALOG, DURATION_1, YEAR_2024);
        assertTrue(result.isPresent());
        assertEquals(SOURCE_ANALOG, result.get().getSource());
    }

    @Test
    @DisplayName("TaxesStatisticsViewService.getAvailableYears should return all distinct years in descending order")
    void getAvailableYears_returnsYearsInDescendingOrder() {
        // Given
        // SH data
        repository.save(createTaxView(ID_1, FEDERAL_STATE_SH, OFFICE_1, SOURCE_ANALOG, YEAR_2023, DURATION_1, 1, REVENUE_50));
        repository.save(createTaxView(ID_2, FEDERAL_STATE_SH, OFFICE_1, SOURCE_ANALOG, YEAR_2023, DURATION_3, 2, REVENUE_150));
        repository.save(createTaxView(ID_3, FEDERAL_STATE_SH, OFFICE_1, SOURCE_ONLINE, YEAR_2024, DURATION_1, 3, REVENUE_100));
        repository.save(createTaxView(ID_4, FEDERAL_STATE_SH, OFFICE_1, SOURCE_ONLINE, YEAR_2024, DURATION_5, 1, REVENUE_250));

        // NW data
        repository.save(createTaxView(5L, FEDERAL_STATE_NW, OFFICE_2, SOURCE_ANALOG, YEAR_2023, DURATION_1, 1, REVENUE_50));
        repository.save(createTaxView(6L, FEDERAL_STATE_NW, OFFICE_2, SOURCE_ANALOG, YEAR_2023, DURATION_3, 2, REVENUE_150));

        // Additional 2022 entry
        repository.save(createTaxView(7L, FEDERAL_STATE_SH, OFFICE_1, SOURCE_ANALOG, YEAR_2022, DURATION_1, 1, REVENUE_50));

        // When
        List<Integer> result = taxesStatisticsViewService.getAvailableYears();

        // Then
        assertEquals(3, result.size());
        assertEquals(List.of(YEAR_2024, YEAR_2023, YEAR_2022), result);
    }

    @Test
    @DisplayName("TaxesStatisticsViewService.deleteAll should delete all entries")
    void deleteAll_removesAllEntries() {
        // Given
        // SH data
        repository.save(createTaxView(ID_1, FEDERAL_STATE_SH, OFFICE_1, SOURCE_ANALOG, YEAR_2023, DURATION_1, 1, REVENUE_50));
        repository.save(createTaxView(ID_2, FEDERAL_STATE_SH, OFFICE_1, SOURCE_ANALOG, YEAR_2023, DURATION_3, 2, REVENUE_150));

        // NW data
        repository.save(createTaxView(5L, FEDERAL_STATE_NW, OFFICE_2, SOURCE_ANALOG, YEAR_2023, DURATION_1, 1, REVENUE_50));
        repository.save(createTaxView(6L, FEDERAL_STATE_NW, OFFICE_2, SOURCE_ANALOG, YEAR_2023, DURATION_3, 2, REVENUE_150));

        // When
        taxesStatisticsViewService.deleteAll();

        // Then
        assertEquals(0, repository.count());
    }

    @Test
    @DisplayName("TaxesStatisticsViewService.getAvailableOffices should return offices without filters")
    void getAvailableOffices_returnsOfficesWithoutFilters() {
        // Given
        repository.save(createTaxView(ID_1, FEDERAL_STATE_SH, "Office1", SOURCE_ONLINE, YEAR_2023, DURATION_1, 1, REVENUE_100));
        repository.save(createTaxView(ID_2, FEDERAL_STATE_NW, "Office2", SOURCE_ANALOG, YEAR_2024, DURATION_1, 1, REVENUE_150));

        // When
        List<String> result = taxesStatisticsViewService.getAvailableOffices();

        // Then
        assertEquals(2, result.size());
        assertTrue(result.contains("Office1"));
        assertTrue(result.contains("Office2"));
    }

    @Test
    @DisplayName("TaxesStatisticsViewService.getAvailableOfficesByYears should return offices filtered by years")
    void getAvailableOfficesByYears_returnsFilteredOffices() {
        // Given
        repository.save(createTaxView(ID_1, FEDERAL_STATE_SH, "Office1", SOURCE_ONLINE, YEAR_2023, DURATION_1, 1, REVENUE_100));
        repository.save(createTaxView(ID_2, FEDERAL_STATE_NW, "Office3", SOURCE_ANALOG, YEAR_2023, DURATION_1, 1, REVENUE_150));

        // When
        List<String> result = taxesStatisticsViewService.getAvailableOfficesByYears(List.of(YEAR_2023));

        // Then
        assertEquals(2, result.size());
        assertTrue(result.contains("Office1"));
        assertTrue(result.contains("Office3"));
    }

    @Test
    @DisplayName("TaxesStatisticsViewService.getAvailableOfficesByFederalState should return offices filtered by federal state")
    void getAvailableOfficesByFederalState_returnsFilteredOffices() {
        // Given
        repository.save(createTaxView(ID_1, FEDERAL_STATE_SH, "Office1", SOURCE_ONLINE, YEAR_2023, DURATION_1, 1, REVENUE_100));
        repository.save(createTaxView(ID_2, FEDERAL_STATE_SH, "Office4", SOURCE_ANALOG, YEAR_2024, DURATION_1, 1, REVENUE_200));

        // When
        List<String> result = taxesStatisticsViewService.getAvailableOfficesByFederalState(FEDERAL_STATE_SH);

        // Then
        assertEquals(2, result.size());
        assertTrue(result.contains("Office1"));
        assertTrue(result.contains("Office4"));
    }

    @Test
    @DisplayName("TaxesStatisticsViewService.getAvailableOfficesByYearsAndFederalState should return offices filtered by both years and federal state")
    void getAvailableOfficesByYearsAndFederalState_returnsFilteredOffices() {
        // Given
        repository.save(createTaxView(ID_1, FEDERAL_STATE_SH, "Office1", SOURCE_ONLINE, YEAR_2023, DURATION_1, 1, REVENUE_100));

        // When
        List<String> result = taxesStatisticsViewService
                .getAvailableOfficesByYearsAndFederalState(List.of(YEAR_2023), FEDERAL_STATE_SH);

        // Then
        assertEquals(1, result.size());
        assertTrue(result.contains("Office1"));
    }

    @Test
    @DisplayName("TaxesStatisticsViewService.getStatisticsByFederalStateAndYears should return statistics for specified federal state and years")
    void getStatisticsByFederalStateAndYears_returnsCorrectStatistics() {
        // Given
        // SH data
        repository.save(createTaxView(ID_1, FEDERAL_STATE_SH, OFFICE_1, SOURCE_ANALOG, YEAR_2023, DURATION_1, 1, REVENUE_50));
        repository.save(createTaxView(ID_2, FEDERAL_STATE_SH, OFFICE_1, SOURCE_ANALOG, YEAR_2023, DURATION_3, 2, REVENUE_150));
        repository.save(createTaxView(ID_3, FEDERAL_STATE_SH, OFFICE_1, SOURCE_ONLINE, YEAR_2024, DURATION_1, 3, REVENUE_100));
        repository.save(createTaxView(ID_4, FEDERAL_STATE_SH, OFFICE_1, SOURCE_ONLINE, YEAR_2024, DURATION_5, 1, REVENUE_250));

        // NW data with different office
        repository.save(createTaxView(5L, FEDERAL_STATE_NW, OFFICE_2, SOURCE_ANALOG, YEAR_2023, DURATION_1, 1, REVENUE_50));
        repository.save(createTaxView(6L, FEDERAL_STATE_NW, OFFICE_2, SOURCE_ANALOG, YEAR_2023, DURATION_3, 2, REVENUE_150));

        // When
        List<TaxesStatisticsView> result = taxesStatisticsViewService
                .getStatisticsByFederalStateAndYears(FEDERAL_STATE_SH, List.of(YEAR_2023, YEAR_2024));

        // Then
        assertEquals(4, result.size());
        assertTrue(result.stream().allMatch(view -> view.getFederalState().equals(FEDERAL_STATE_SH)));
        assertTrue(result.stream().allMatch(view -> List.of(YEAR_2023, YEAR_2024).contains(view.getYear())));
    }

    @Test
    @DisplayName("TaxesStatisticsViewService.getStatisticsByOfficeAndYears should return statistics for specified office and years")
    void getStatisticsByOfficeAndYears_returnsCorrectStatistics() {
        // Given
        // Office1 data
        repository.save(createTaxView(ID_1, FEDERAL_STATE_SH, OFFICE_1, SOURCE_ANALOG, YEAR_2023, DURATION_1, 1, REVENUE_50));
        repository.save(createTaxView(ID_2, FEDERAL_STATE_SH, OFFICE_1, SOURCE_ANALOG, YEAR_2023, DURATION_3, 2, REVENUE_150));
        repository.save(createTaxView(ID_3, FEDERAL_STATE_SH, OFFICE_1, SOURCE_ONLINE, YEAR_2024, DURATION_1, 3, REVENUE_100));
        repository.save(createTaxView(ID_4, FEDERAL_STATE_SH, OFFICE_1, SOURCE_ONLINE, YEAR_2024, DURATION_5, 1, REVENUE_250));

        // Office2 data
        repository.save(createTaxView(5L, FEDERAL_STATE_NW, OFFICE_2, SOURCE_ANALOG, YEAR_2023, DURATION_1, 1, REVENUE_50));
        repository.save(createTaxView(6L, FEDERAL_STATE_NW, OFFICE_2, SOURCE_ANALOG, YEAR_2023, DURATION_3, 2, REVENUE_150));

        // When
        List<TaxesStatisticsView> result = taxesStatisticsViewService
                .getStatisticsByOfficeAndYears(OFFICE_1, List.of(YEAR_2023, YEAR_2024));

        // Then
        assertEquals(4, result.size());
        assertTrue(result.stream().allMatch(view -> view.getOffice().equals(OFFICE_1)));
        assertTrue(result.stream().allMatch(view -> List.of(YEAR_2023, YEAR_2024).contains(view.getYear())));
    }

    @Test
    @DisplayName("TaxesStatisticsViewService.getStatisticsByYears should return statistics for specified years")
    void getStatisticsByYears_returnsCorrectStatistics() {
        // Given
        // SH data
        repository.save(createTaxView(ID_1, FEDERAL_STATE_SH, OFFICE_1, SOURCE_ANALOG, YEAR_2023, DURATION_1, 1, REVENUE_50));
        repository.save(createTaxView(ID_2, FEDERAL_STATE_SH, OFFICE_1, SOURCE_ANALOG, YEAR_2023, DURATION_3, 2, REVENUE_150));
        repository.save(createTaxView(ID_3, FEDERAL_STATE_SH, OFFICE_1, SOURCE_ONLINE, YEAR_2024, DURATION_1, 3, REVENUE_100));
        repository.save(createTaxView(ID_4, FEDERAL_STATE_SH, OFFICE_1, SOURCE_ONLINE, YEAR_2024, DURATION_5, 1, REVENUE_250));

        // NW data
        repository.save(createTaxView(5L, FEDERAL_STATE_NW, OFFICE_2, SOURCE_ANALOG, YEAR_2023, DURATION_1, 1, REVENUE_50));
        repository.save(createTaxView(6L, FEDERAL_STATE_NW, OFFICE_2, SOURCE_ANALOG, YEAR_2023, DURATION_3, 2, REVENUE_150));
        repository.save(createTaxView(7L, FEDERAL_STATE_NW, OFFICE_2, SOURCE_ONLINE, YEAR_2024, DURATION_1, 3, REVENUE_100));
        repository.save(createTaxView(8L, FEDERAL_STATE_NW, OFFICE_2, SOURCE_ONLINE, YEAR_2024, DURATION_5, 1, REVENUE_250));

        // When
        List<TaxesStatisticsView> result = taxesStatisticsViewService
                .getStatisticsByYears(List.of(YEAR_2023, YEAR_2024));

        // Then
        assertEquals(8, result.size());
        assertTrue(result.stream().allMatch(view -> List.of(YEAR_2023, YEAR_2024).contains(view.getYear())));
    }

    private TaxesStatisticsView createTaxView(
            long id, String federalState, String office, SubmissionType source,
            int year, int duration, int count, double revenue) {
        TaxesStatisticsView view = new TaxesStatisticsView();
        view.setId(id);
        view.setFederalState(federalState);
        view.setOffice(office);
        view.setSource(source);
        view.setYear(year);
        view.setDuration(duration);
        view.setCount(count);
        view.setRevenue(revenue);
        return view;
    }
}