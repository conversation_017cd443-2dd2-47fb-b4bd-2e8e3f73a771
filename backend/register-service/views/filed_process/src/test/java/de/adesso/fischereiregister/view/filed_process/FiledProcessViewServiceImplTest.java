package de.adesso.fischereiregister.view.filed_process;

import de.adesso.fischereiregister.core.model.Ban;
import de.adesso.fischereiregister.core.model.DeletionFlag;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.QualificationsProof;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.consent.ConsentInfo;
import de.adesso.fischereiregister.core.model.consent.JurisdictionConsentInfo;
import de.adesso.fischereiregister.core.model.consent.TaxConsentInfo;
import de.adesso.fischereiregister.core.model.type.DeletionReason;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import de.adesso.fischereiregister.view.filed_process.persistence.FiledProcessType;
import de.adesso.fischereiregister.view.filed_process.persistence.FiledProcessView;
import de.adesso.fischereiregister.view.filed_process.persistence.FiledProcessViewRepository;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Instant;
import java.time.LocalDate;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class FiledProcessViewServiceImplTest {

    @Mock
    private FiledProcessViewRepository repository;

    @InjectMocks
    private FiledProcessViewServiceImpl service;

    @Captor
    private ArgumentCaptor<FiledProcessView> viewCaptor;


    @Test
    void testCreateQualificationsProofCreatedProcess() {
        // ARRANGE
        final UUID registerEntryId = DomainTestData.registerId;
        final String actingInstitution = DomainTestData.getIssuedByOfficeAddress;
        final FederalState federalState = FederalState.SH;
        final Instant timestamp = Instant.now();
        final Person person = DomainTestData.createPerson();
        final QualificationsProof qualificationsProof = DomainTestData.createQualificationsProof();

        final ProcessHeaderData processHeaderData = ProcessHeaderData.builder()
                .registerEntryId(registerEntryId)
                .actingInstitution(actingInstitution)
                .federalStateOfInstitution(federalState)
                .timestamp(timestamp)
                .build();

        final FiledProcessData data = FiledProcessData.builder()
                .person(person)
                .qualificationsProof(qualificationsProof)
                .build();


        // ACT
        service.createQualificationsProofCreatedProcess(processHeaderData, data);

        // ASSERT
        verify(repository).save(viewCaptor.capture());

        final FiledProcessView capturedView = viewCaptor.getValue();
        assertEquals(registerEntryId, capturedView.getRegisterEntryId());

    }

    @Test
    void testCreateJurisdictionMovedProcess() {
        // ARRANGE
        RegisterEntry entry = DomainTestData.createRegisterEntry();
        final String actingInstitution = DomainTestData.getIssuedByOfficeAddress;
        final FederalState federalState = FederalState.SH;
        final Instant timestamp = Instant.now();
        final JurisdictionConsentInfo consentInfo = DomainTestData.createJurisdictionConsentInfo();

        final ProcessHeaderData processHeaderData = ProcessHeaderData.builder()
                .registerEntryId(entry.getRegisterId())
                .actingInstitution(actingInstitution)
                .federalStateOfInstitution(federalState)
                .timestamp(timestamp)
                .build();

        final FiledProcessData data = FiledProcessData.builder()
                .taxes(entry.getTaxes())
                .consentInfo(consentInfo)
                .identificationDocuments(entry.getIdentificationDocuments())
                .build();

        // ACT
        service.createJurisdictionMovedProcess(
                processHeaderData,
                data
        );

        // ASSERT
        verify(repository).save(viewCaptor.capture());

        final FiledProcessView capturedView = viewCaptor.getValue();
        assertEquals(entry.getRegisterId(), capturedView.getRegisterEntryId());
        assertEquals(federalState, capturedView.getFederalStateOfInstitution());
    }

    @Test
    void testCreateFishingLicenseCreatedProcess() {
        // ARRANGE
        RegisterEntry entry = DomainTestData.createRegisterEntry();
        final String actingInstitution = DomainTestData.getIssuedByOfficeAddress;
        final FederalState federalState = FederalState.SH;
        final Instant timestamp = Instant.now();
        final ConsentInfo consentInfo = DomainTestData.createConsentInfo();

        final ProcessHeaderData processHeaderData = ProcessHeaderData.builder()
                .registerEntryId(entry.getRegisterId())
                .actingInstitution(actingInstitution)
                .federalStateOfInstitution(federalState)
                .timestamp(timestamp)
                .build();

        final FiledProcessData data = FiledProcessData.builder()
                .person(entry.getPerson())
                .serviceAccountId(entry.getServiceAccountId())
                .taxes(entry.getTaxes())
                .fees(entry.getFees())
                .consentInfo(consentInfo)
                .identificationDocuments(entry.getIdentificationDocuments())
                .fishingLicense(entry.getFishingLicenses().getFirst())
                .build();

        // ACT
        service.createFishingLicenseCreatedProcess(
                processHeaderData,
                data
        );

        // ASSERT
        verify(repository).save(viewCaptor.capture());

        final FiledProcessView capturedView = viewCaptor.getValue();
        assertEquals(entry.getRegisterId(), capturedView.getRegisterEntryId());
    }

    @Test
    void testCreateFishingLicenseExtendedProcess() {
        // ARRANGE
        RegisterEntry entry = DomainTestData.createRegisterEntry();
        final String actingInstitution = DomainTestData.getIssuedByOfficeAddress;
        final FederalState federalState = FederalState.SH;
        final Instant timestamp = Instant.now();
        final ConsentInfo consentInfo = DomainTestData.createConsentInfo();

        final ProcessHeaderData processHeaderData = ProcessHeaderData.builder()
                .registerEntryId(entry.getRegisterId())
                .actingInstitution(actingInstitution)
                .federalStateOfInstitution(federalState)
                .timestamp(timestamp)
                .build();

        final FiledProcessData data = FiledProcessData.builder()
                .person(entry.getPerson())
                .serviceAccountId(entry.getServiceAccountId())
                .taxes(entry.getTaxes())
                .fees(entry.getFees())
                .consentInfo(consentInfo)
                .identificationDocuments(entry.getIdentificationDocuments())
                .fishingLicenseNumber(entry.getFishingLicenses().getFirst().getNumber())
                .build();

        // ACT
        service.createFishingLicenseExtendedProcess(
                processHeaderData,
                data
        );

        // ASSERT
        verify(repository).save(viewCaptor.capture());

        final FiledProcessView capturedView = viewCaptor.getValue();
        assertEquals(entry.getRegisterId(), capturedView.getRegisterEntryId());
        assertEquals(entry.getFishingLicenses().getFirst().getNumber(),
                capturedView.getFiledProcessData().getFishingLicenseNumber());
    }

    @Test
    void testCreateReplacementCardOrderedProcess() {
        // ARRANGE
        RegisterEntry entry = DomainTestData.createRegisterEntry();
        final String actingInstitution = DomainTestData.getIssuedByOfficeAddress;
        final FederalState federalState = FederalState.SH;
        final Instant timestamp = Instant.now();
        final ConsentInfo consentInfo = DomainTestData.createConsentInfo();

        final ProcessHeaderData processHeaderData = ProcessHeaderData.builder()
                .registerEntryId(entry.getRegisterId())
                .actingInstitution(actingInstitution)
                .federalStateOfInstitution(federalState)
                .timestamp(timestamp)
                .build();

        final FiledProcessData data = FiledProcessData.builder()
                .person(entry.getPerson())
                .serviceAccountId(entry.getServiceAccountId())
                .taxes(entry.getTaxes())
                .fees(entry.getFees())
                .consentInfo(consentInfo)
                .identificationDocuments(entry.getIdentificationDocuments())
                .fishingLicense(entry.getFishingLicenses().getFirst())
                .build();

        // ACT
        service.createReplacementCardOrderedProcess(
                processHeaderData,
                data
        );

        // ASSERT
        verify(repository).save(viewCaptor.capture());

        final FiledProcessView capturedView = viewCaptor.getValue();
        assertEquals(entry.getRegisterId(), capturedView.getRegisterEntryId());
        assertEquals(entry.getFishingLicenses().getFirst(), capturedView.getFiledProcessData().getFishingLicense());
    }

    @Test
    void testCreateFishingTaxPayedProcess() {
        // ARRANGE
        RegisterEntry entry = DomainTestData.createRegisterEntry();
        final String actingInstitution = DomainTestData.getIssuedByOfficeAddress;
        final FederalState federalState = FederalState.SH;
        final Instant timestamp = Instant.now();
        final TaxConsentInfo consentInfo = DomainTestData.createTaxConsentInfo();

        final ProcessHeaderData processHeaderData = ProcessHeaderData.builder()
                .registerEntryId(entry.getRegisterId())
                .actingInstitution(actingInstitution)
                .federalStateOfInstitution(federalState)
                .timestamp(timestamp)
                .build();

        final FiledProcessData data = FiledProcessData.builder()
                .person(entry.getPerson())
                .serviceAccountId(entry.getServiceAccountId())
                .taxes(entry.getTaxes())
                .consentInfo(consentInfo)
                .identificationDocuments(entry.getIdentificationDocuments())
                .build();

        // ACT
        service.createFishingTaxPayedProcess(
                processHeaderData,
                data
        );

        // ASSERT
        verify(repository).save(viewCaptor.capture());

        final FiledProcessView capturedView = viewCaptor.getValue();
        assertEquals(entry.getRegisterId(), capturedView.getRegisterEntryId());
        assertEquals(consentInfo, capturedView.getFiledProcessData().getConsentInfo());
    }

    @Test
    void testCreateBannedProcess() {
        // ARRANGE
        RegisterEntry entry = DomainTestData.createRegisterEntry();
        final String actingInstitution = DomainTestData.getIssuedByOfficeAddress;
        final FederalState federalState = FederalState.SH;
        final Instant timestamp = Instant.now();
        final Ban ban = new Ban();
        ban.setAt(LocalDate.now());
        ban.setBanId(mock(UUID.class));
        ban.setFileNumber("ABCDE");
        ban.setReportedBy("Kieler Angel Behörde");
        ban.setFrom(LocalDate.now());
        ban.setTo(LocalDate.now());

        final ProcessHeaderData processHeaderData = ProcessHeaderData.builder()
                .registerEntryId(entry.getRegisterId())
                .actingInstitution(actingInstitution)
                .federalStateOfInstitution(federalState)
                .timestamp(timestamp)
                .build();

        final FiledProcessData data = FiledProcessData.builder()
                .ban(ban)
                .build();

        // ACT
        service.createBannedProcess(processHeaderData, data);

        // ASSERT
        verify(repository).save(viewCaptor.capture());

        final FiledProcessView capturedView = viewCaptor.getValue();
        assertEquals(entry.getRegisterId(), capturedView.getRegisterEntryId());
        assertEquals(ban, capturedView.getFiledProcessData().getBan());
    }

    @Test
    void testCreateUnbannedProcess() {
        // ARRANGE
        final UUID registerEntryId = DomainTestData.registerId;
        final FederalState federalState = FederalState.SH;
        final Instant timestamp = Instant.now();

        final ProcessHeaderData processHeaderData = ProcessHeaderData.builder()
                .registerEntryId(registerEntryId)
                .federalStateOfInstitution(federalState)
                .timestamp(timestamp)
                .build();

        // ACT
        service.createUnbannedProcess(processHeaderData);

        // ASSERT
        verify(repository).save(viewCaptor.capture());

        final FiledProcessView capturedView = viewCaptor.getValue();
        assertEquals(registerEntryId, capturedView.getRegisterEntryId());
        assertNull(capturedView.getFiledProcessData().getBan());
    }

    @Test
    void testCreateMarkDeletedProcess() {
        // ARRANGE
        final UUID registerEntryId = DomainTestData.registerId;
        final FederalState federalState = FederalState.SH;
        final Instant timestamp = Instant.now();
        final DeletionFlag deletionFlag = new DeletionFlag(DeletionReason.GDPR_REQUEST);

        final ProcessHeaderData processHeaderData = ProcessHeaderData.builder()
                .registerEntryId(registerEntryId)
                .federalStateOfInstitution(federalState)
                .timestamp(timestamp)
                .build();

        final FiledProcessData data = FiledProcessData.builder()
                .deletionFlag(deletionFlag)
                .build();


        // ACT
        service.createMarkedForDeletionProcess(processHeaderData, data);

        // ASSERT
        verify(repository).save(viewCaptor.capture());

        final FiledProcessView capturedView = viewCaptor.getValue();
        assertEquals(registerEntryId, capturedView.getRegisterEntryId());
        assertEquals(capturedView.getFiledProcessData().getDeletionFlag(), deletionFlag);
        assertEquals(capturedView.getFiledProcessType(), FiledProcessType.MARKED_FOR_DELETION);
    }

}
