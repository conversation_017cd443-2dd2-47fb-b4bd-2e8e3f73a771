package de.adesso.fischereiregister.view.filed_process.persistence;

import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.view.filed_process.FiledProcessData;
import de.adesso.fischereiregister.view.filed_process.converter.FiledProcessDataConverter;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Lob;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.Instant;
import java.util.UUID;

@Entity
@Table
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class FiledProcessView {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;

    @Column(nullable = false)
    private UUID registerEntryId;

    /**
     * Institution that carried out the process
     */
    @Column
    private String actingInstitution;

    /**
     * FederalState of the acting institution
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private FederalState federalStateOfInstitution;

    /**
     * Timestamp when the process was carried out
     */
    @Column(nullable = false)
    private Instant processTimestamp;

    /**
     * The federal state of the process
     */
    @Enumerated(EnumType.STRING)
    @Column
    private FederalState federalState;

    /**
     * The institution issuing the fishing license or replacement card.
     */
    @Column
    private String issuedBy;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private FiledProcessType filedProcessType;

    @Lob
    @Column(columnDefinition = "TEXT", nullable = false)
    @Convert(converter = FiledProcessDataConverter.class)
    private FiledProcessData filedProcessData;
}
