package de.adesso.fischereiregister.view.licenses_statistics.persistance;

import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.core.model.type.SubmissionType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table
@Getter
@Setter
public class LicensesStatisticsView {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false)
    private String federalState;

    @Column(nullable = false)
    private String office;

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private LicenseType licenseType;

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private SubmissionType source;

    @Column(nullable = false)
    private int year;

    @Column(nullable = false)
    private int count;
}
