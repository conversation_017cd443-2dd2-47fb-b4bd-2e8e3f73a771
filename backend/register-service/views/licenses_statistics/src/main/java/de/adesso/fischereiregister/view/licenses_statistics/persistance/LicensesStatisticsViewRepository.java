package de.adesso.fischereiregister.view.licenses_statistics.persistance;

import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.core.model.type.SubmissionType;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

public interface LicensesStatisticsViewRepository extends CrudRepository<LicensesStatisticsView, Long> {
    @Query("""
            SELECT view FROM LicensesStatisticsView view
            WHERE view.federalState = :federalState
              AND COALESCE(view.office, '') = COALESCE(:office, '')
              AND view.source = :source
              AND view.licenseType = :licenseType
              AND view.year = :year
            """)
    Optional<LicensesStatisticsView> findByLicenseTypeAndFederalStateAndOfficeAndSourceAndYear(
            @Param("licenseType") LicenseType licenseType,
            @Param("federalState") String federalState,
            @Param("office") String office,
            @Param("source") SubmissionType source,
            @Param("year") int year
    );

    @Query("""
            SELECT view FROM LicensesStatisticsView view
            WHERE view.federalState = :federalState
              AND view.year IN :years
              AND view.licenseType = :licenseType
            """)
    List<LicensesStatisticsView> findByLicenseTypeAndFederalStateAndYearIn(
            @Param("licenseType") LicenseType licenseType,
            @Param("federalState") String federalState,
            @Param("years") List<Integer> years
    );

    @Query("""
            SELECT view FROM LicensesStatisticsView view
            WHERE COALESCE(view.office, '') = COALESCE(:office, '')
              AND view.year IN :years
              AND view.licenseType = :licenseType
            """)
    List<LicensesStatisticsView> findByLicenseTypeAndOfficeAndYearIn(
            @Param("licenseType") LicenseType licenseType,
            @Param("office") String office,
            @Param("years") List<Integer> years
    );

    @Query("""
            SELECT view FROM LicensesStatisticsView view
            WHERE view.year IN :years
              AND view.licenseType = :licenseType
            """)
    List<LicensesStatisticsView> findByLicenseTypeAndYearIn(
            @Param("licenseType") LicenseType licenseType,
            @Param("years") List<Integer> years
    );

    @Query("""
            SELECT DISTINCT view.year FROM LicensesStatisticsView view
            ORDER BY view.year
            """)
    List<Integer> findDistinctYears();

    @Query("""
            SELECT view FROM LicensesStatisticsView view
            WHERE view.federalState = :federalState
              AND view.year IN :years
            """)
    List<LicensesStatisticsView> findByFederalStateAndYearIn(
            @Param("federalState") String federalState,
            @Param("years") List<Integer> years
    );

    @Query("""
            SELECT view FROM LicensesStatisticsView view
            WHERE view.year IN :years
            """)
    List<LicensesStatisticsView> findByYearIn(
            @Param("years") List<Integer> years
    );

    @Query("""
            SELECT DISTINCT view.office FROM LicensesStatisticsView view
            ORDER BY view.office
            """)
    List<String> findDistinctOffices();

    @Query("""
            SELECT DISTINCT view.office FROM LicensesStatisticsView view
            WHERE view.year IN :years
            ORDER BY view.office
            """)
    List<String> findDistinctOfficesByYears(@Param("years") List<Integer> years);

    @Query("""
            SELECT DISTINCT view.office FROM LicensesStatisticsView view
            WHERE view.federalState = :federalState
            ORDER BY view.office
            """)
    List<String> findDistinctOfficesByFederalState(@Param("federalState") String federalState);

    @Query("""
            SELECT DISTINCT view.office FROM LicensesStatisticsView view
            WHERE view.federalState = :federalState
              AND view.year IN :years
            ORDER BY view.office
            """)
    List<String> findDistinctOfficesByFederalStateAndYears(
            @Param("federalState") String federalState,
            @Param("years") List<Integer> years
    );
}
