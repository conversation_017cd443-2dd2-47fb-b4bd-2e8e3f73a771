<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd"
                   context="licenses_statistics_view">

    <!-- ChangeSet for LicensesStatisticsView Table -->
    <changeSet id="1.0.0" author="alexis.ortiz">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="licenses_statistics_view"/>
            </not>
        </preConditions>
        <createTable tableName="licenses_statistics_view">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="federal_state" type="VARCHAR(2)">
                <constraints nullable="false"/>
            </column>
            <column name="office" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
            <column name="license_type" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="source" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="year" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="count" type="INTEGER">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <!-- Add unique constraint to ensure no duplicate combinations-->
        <addUniqueConstraint tableName="licenses_statistics_view"
                             columnNames="federal_state, office, license_type, source, year"
                             constraintName="unique_licenses_statistics"/>
    </changeSet>
</databaseChangeLog>
