package de.adesso.fischereiregister.view.certifications_statistics.eventhandling;

import de.adesso.fischereiregister.core.events.QualificationsProofCreatedEvent;
import de.adesso.fischereiregister.core.events.RegularLicenseDigitizedEvent;
import de.adesso.fischereiregister.core.model.QualificationsProof;
import de.adesso.fischereiregister.view.certifications_statistics.services.CertificationsStatisticsViewService;
import lombok.AllArgsConstructor;
import org.axonframework.config.ProcessingGroup;
import org.axonframework.eventhandling.EventHandler;
import org.axonframework.eventhandling.ResetHandler;
import org.springframework.stereotype.Component;

/**
 * Event handler for certification statistics.
 * Processes events related to certifications and updates statistics accordingly.
 */
@Component
@ProcessingGroup("certifications-statistics")
@AllArgsConstructor
public class CertificationsStatisticsViewEventHandler {

    private final CertificationsStatisticsViewService certificationsStatisticsViewService;

    /**
     * Resets the statistics by deleting all entries.
     * Called when the event processor is reset.
     */
    @ResetHandler
    public void onReset() {
        certificationsStatisticsViewService.deleteAll(); // clear projection table before replay
    }

    /**
     * Process certification statistics from QualificationsProofCreatedEvent
     */
    @EventHandler
    public void on(QualificationsProofCreatedEvent event) {
        certificationsStatisticsViewService.updateOrCreateStatistic(
                event.qualificationsProof().getFederalState(),
                event.qualificationsProof().getIssuedBy(), // issuer is the office that issued the certification
                event.qualificationsProof().getPassedOn().getYear()
        );
    }

    /**
     * Process certification statistics from RegularLicenseDigitizedEvent
     * Processes each qualification proof in the event
     */
    @EventHandler
    public void on(RegularLicenseDigitizedEvent event) {
        // Process each qualification proof in the event
        if (event.qualificationsProofs() != null && !event.qualificationsProofs().isEmpty()) {
            for (QualificationsProof proof : event.qualificationsProofs()) {
                certificationsStatisticsViewService.updateOrCreateStatistic(
                        proof.getFederalState(),
                        proof.getIssuedBy(), // issuer is the office that issued the certification
                        proof.getPassedOn().getYear()
                );
            }
        }
    }
}
