package de.adesso.fischereiregister.registerservice.validate_fishing_license.model;

import com.fasterxml.jackson.annotation.JsonValue;

public enum FishingLicenseValidationStatus {
    VALID("VALID"),
    INVALID("INVALID"),
    BANNED("BANNED");

    private String value;

    private FishingLicenseValidationStatus(String value) {
        this.value = value;
    }

    @JsonValue
    public String getValue() {
        return this.value;
    }

    @Override
    public String toString() {
        return String.valueOf(this.value);
    }

}
