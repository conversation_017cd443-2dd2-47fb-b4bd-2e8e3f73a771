package de.adesso.fischereiregister.registerservice.register_entry;

import api.RegisterEntryApi;
import de.adesso.fischereiregister.core.commands.MarkRegisterEntryForDeletionCommand;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.user.UserDetails;
import de.adesso.fischereiregister.registerservice.domain.mapper.DeletionReasonMapper;
import de.adesso.fischereiregister.registerservice.register_entry_view.RegisterEntryView;
import de.adesso.fischereiregister.registerservice.register_entry_view.RegisterEntryViewService;
import de.adesso.fischereiregister.registerservice.security.UserDetailsService;
import de.adesso.fischereiregister.view.register_entry_search.RegisterEntrySearchViewService;
import jakarta.persistence.EntityNotFoundException;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.axonframework.commandhandling.gateway.CommandGateway;
import org.openapitools.model.DeletionReason;
import org.openapitools.model.SearchItem;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.ExecutionException;

@Slf4j
@RestController
@RequestMapping()
@AllArgsConstructor
public class RegisterEntriesController implements RegisterEntryApi {

    private final RegisterEntrySearchViewService registerEntrySearchViewService;
    private final RegisterEntryViewService registerEntryViewService;
    private final UserDetailsService userDetailsService;
    private final CommandGateway commandGateway;

    @Override
    public ResponseEntity<?> registerEntriesControllerSearch(String search) {
        if (isSearchQueryInvalid(search)) {
            return ResponseEntity.badRequest().build();
        }

        List<SearchItem> results = registerEntrySearchViewService.search(search);

        if (results.isEmpty()) {
            return ResponseEntity.notFound().build();
        }
        if (results.size() > 100) {
            return ResponseEntity.badRequest().build();
        }

        return ResponseEntity.ok(results);
    }

    private boolean isSearchQueryInvalid(String search) {
        return search == null || search.isBlank() || Arrays.stream(search.split(",")).allMatch(String::isBlank);
    }

    @Override
    public ResponseEntity<?> registerEntriesControllerGet(String registerEntryId) {

        try {
            final UserDetails userDetails = userDetailsService.getUserDetails()
                    .orElseThrow(() -> {
                        log.error("Failed to retrieve user details: User details are missing or corrupted.");
                        return new IllegalStateException("The User Auth Information is missing or corrupted");
                    });
            RegisterEntryView registerEntryView = registerEntryViewService.findByRegisterId(UUID.fromString(registerEntryId), userDetails.getRoles());
            RegisterEntry registerEntry = registerEntryView.getData();

            return ResponseEntity.ok(registerEntry);
        } catch (EntityNotFoundException ex) {
            log.warn("RegisterEntry with id {} not found, error response: {}", registerEntryId, ex.getMessage());
            return ResponseEntity.notFound().build();

        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseEntity.badRequest().build();
        }

    }

    @Override
    @SneakyThrows
    public ResponseEntity<?> registerEntriesControllerDelete(String registerEntryId, @NotNull @Valid DeletionReason deletionReason) {
        MarkRegisterEntryForDeletionCommand command = new MarkRegisterEntryForDeletionCommand(
                UUID.fromString(registerEntryId),
                DeletionReasonMapper.INSTANCE.toDomain(deletionReason),
                userDetailsService.getUserDetails()
                        .orElseThrow(() -> new IllegalStateException("The User Auth Information is missing or corrupted"))
        );

        try {
            log.info("Sending DeleteRegisterEntryCommand for registerEntryId: {}", command.registerId());

            commandGateway.send(command).get();

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Delete Register Entry thread interrupted for registerEntryId {}", command.registerId(), e);
            throw new IllegalStateException("InterruptedException for registerEntryId:" + command.registerId(), e);
        } catch (ExecutionException e) {
            Throwable cause = e.getCause();

            log.error("Delete Register Entry thread ExecutionException for RegisterID {}", command.registerId(),
                    e);

            if (cause != null) {
                throw cause;
            } else {
                throw e;
            }
        }
        return ResponseEntity.ok().build();
    }
}