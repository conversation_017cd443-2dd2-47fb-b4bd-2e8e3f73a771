package de.adesso.fischereiregister.registerservice.domain.mapper;

import de.adesso.fischereiregister.core.model.consent.LimitedLicenseConsentInfo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "default")
public interface LimitedLicenseConsentInfoMapper {

    LimitedLicenseConsentInfoMapper INSTANCE = Mappers.getMapper(LimitedLicenseConsentInfoMapper.class);

    LimitedLicenseConsentInfo toLimitedLicenseConsentInfo(org.openapitools.model.LimitedLicenseConsentInfo dto);
}
