package de.adesso.fischereiregister.registerservice.drools.dmn.tax.validation;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class TaxValidationInput {
    private LocalDate legislativeValidityDate;
    private Integer age; // alter der beantragenden person
    private Integer taxYear;
    private Integer calculationYear;

    public TaxValidationInput(Integer age, Integer taxYear, LocalDate legislativeValidityDate) {
        this.age = age;
        this.legislativeValidityDate = legislativeValidityDate;
        this.taxYear = taxYear;
        this.calculationYear = legislativeValidityDate.getYear();
    }
}
