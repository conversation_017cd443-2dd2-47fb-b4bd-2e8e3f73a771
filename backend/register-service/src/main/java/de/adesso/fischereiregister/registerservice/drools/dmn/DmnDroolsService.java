package de.adesso.fischereiregister.registerservice.drools.dmn;

import de.adesso.fischereiregister.core.exceptions.RulesProcessingException;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.registerservice.drools.dmn.license.configuration.input.DmnLicenseType;
import de.adesso.fischereiregister.registerservice.drools.dmn.license.configuration.input.LicenseInformationInput;
import de.adesso.fischereiregister.registerservice.drools.dmn.license.configuration.output.LicenseInformationOutput;
import de.adesso.fischereiregister.registerservice.drools.dmn.license.validation.input.LicenseValidationInput;
import de.adesso.fischereiregister.registerservice.drools.dmn.license.validation.output.LicenseValidationOutput;
import de.adesso.fischereiregister.registerservice.drools.dmn.mapper.DmnLicenseTypeMapper;
import de.adesso.fischereiregister.registerservice.drools.dmn.model.DmnProcessingType;
import de.adesso.fischereiregister.registerservice.drools.dmn.qualifications_proof.QualificationsProofValidationInput;
import de.adesso.fischereiregister.registerservice.drools.dmn.qualifications_proof.QualificationsProofValidationOutput;
import de.adesso.fischereiregister.registerservice.drools.dmn.tax.configuration.TaxInformationInput;
import de.adesso.fischereiregister.registerservice.drools.dmn.tax.configuration.TaxInformationOutput;
import de.adesso.fischereiregister.registerservice.drools.dmn.tax.validation.TaxValidationInput;
import de.adesso.fischereiregister.registerservice.drools.dmn.tax.validation.TaxValidationOutput;
import lombok.extern.slf4j.Slf4j;
import org.kie.api.runtime.KieSession;
import org.kie.dmn.api.core.DMNContext;
import org.kie.dmn.api.core.DMNDecisionResult;
import org.kie.dmn.api.core.DMNMessage;
import org.kie.dmn.api.core.DMNModel;
import org.kie.dmn.api.core.DMNRuntime;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.EnumSet;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 *
 */
@Service
@Slf4j
public class DmnDroolsService {

    public static final String MODEL_NAMESPACE = "urn:register-service:dmn:_8378122a-5c22-465d-a54b-6963412ac323";

    private static final String ABGABEN = "Abgaben";
    private static final String KONFIGURATION_FISCHEREISCHEIN = "Konfiguration Fischereischein";
    private static final String VALIDIERUNG_FISCHEREISCHEIN = "Fischereischein Regeln";
    private static final String VALIDIERUNG_ABGABEN = "Abgaben Regeln";
    private static final String VALIDIERUNG_PRUEFUNG = "Fischereiprüfungsregeln";

    private final KieSession kieSession;

    public DmnDroolsService(@Qualifier("dmn-kie-session") KieSession kieSession) {
        this.kieSession = kieSession;
    }

    public boolean doesModelForTenantExist(DMNRuntime dmnRuntime, String modelName) {
        DMNModel model = dmnRuntime.getModel(MODEL_NAMESPACE, modelName);
        return model != null;
    }

    public List<LicenseInformationOutput> evaluateLicenseRules(LicenseInformationInput input, FederalState federalState) throws RulesProcessingException {
        return evaluateRules(KONFIGURATION_FISCHEREISCHEIN, LicenseInformationOutput::new, input, federalState.toString());
    }

    public List<TaxInformationOutput> evaluateTaxRules(de.adesso.fischereiregister.registerservice.drools.dmn.tax.configuration.TaxInformationInput input, FederalState federalState) throws RulesProcessingException {
        return evaluateRules(ABGABEN, TaxInformationOutput::new, input, federalState.toString());
    }

    public List<LicenseValidationOutput> evaluateLicenseValidationRules(LicenseValidationInput input, FederalState federalState) throws RulesProcessingException {
        return evaluateRules(VALIDIERUNG_FISCHEREISCHEIN, LicenseValidationOutput::new, input, federalState.toString());
    }

    public List<TaxValidationOutput> evaluateTaxValidationRules(TaxValidationInput input, FederalState federalState) throws RulesProcessingException {
        return evaluateRules(VALIDIERUNG_ABGABEN, TaxValidationOutput::new, input, federalState.toString());
    }

    public List<QualificationsProofValidationOutput> evaluateQualificationValidationRules(QualificationsProofValidationInput input, FederalState federalState) throws RulesProcessingException {
        return evaluateRules(VALIDIERUNG_PRUEFUNG, QualificationsProofValidationOutput::new, input, federalState.toString());
    }

    public <T, I> List<T> evaluateRules(String tableName, Function<Map<String, Object>, T> creator, I input, String federalState) throws RulesProcessingException {
        DMNRuntime dmnRuntime = kieSession.getKieRuntime(DMNRuntime.class);

        // Get the DMN model
        String modelName = getModelName(federalState, dmnRuntime);
        DMNModel dmnModel = dmnRuntime.getModel(MODEL_NAMESPACE, modelName);

        if (dmnModel == null) {
            throw new IllegalStateException("DMN Model not found: " + modelName);
        }

        DMNContext dmnContext = DmnContextFactory.createContext(input, dmnRuntime);

        DMNDecisionResult decisionResult = dmnRuntime
                .evaluateByName(dmnModel, dmnContext, tableName)
                .getDecisionResultByName(tableName);

        List<T> results = new ArrayList<>();

        if (decisionResult != null
                && decisionResult.getEvaluationStatus() == DMNDecisionResult.DecisionEvaluationStatus.SUCCEEDED
                && decisionResult.getResult() != null) {

            log.info("Rules match: " + decisionResult.getResult());

            Object result = decisionResult.getResult();

            if (result instanceof List<?>) {
                ((List<?>) result).forEach(item ->
                        results.add(creator.apply((Map<String, Object>) item))
                );
            } else if (result instanceof Map<?, ?>) {
                results.add(creator.apply((Map<String, Object>) result));
            } else {
                throw new IllegalArgumentException("Unsupported result type");
            }

        } else if (isDecisionInvalid(decisionResult)) {
            throw new RulesProcessingException(formatDecisionError(decisionResult));
        }

        return results;
    }

    boolean isDecisionInvalid(DMNDecisionResult decisionResult) {
        return decisionResult != null && EnumSet.of(
                DMNDecisionResult.DecisionEvaluationStatus.SKIPPED,
                DMNDecisionResult.DecisionEvaluationStatus.FAILED,
                DMNDecisionResult.DecisionEvaluationStatus.NOT_EVALUATED
        ).contains(decisionResult.getEvaluationStatus());
    }

    String formatDecisionError(DMNDecisionResult decisionResult) {
        return "Rules error: " + decisionResult.getMessages().stream()
                .map(DMNMessage::getText)
                .collect(Collectors.joining(" | "));
    }

    protected String getModelName(String federalState, DMNRuntime dmnRuntime) {
        String modelName = "default";
        String tenantModelName = federalState;
        if (doesModelForTenantExist(dmnRuntime, tenantModelName)) {
            modelName = tenantModelName;
        }
        return modelName;
    }

    public List<LicenseInformationOutput> getLicenseInformation(FederalState federalState, DmnLicenseType dmnLicenseType, DmnProcessingType dmnProcessingType) throws RulesProcessingException {
        List<LicenseInformationOutput> outputList = evaluateLicenseRules(new LicenseInformationInput(dmnLicenseType,
                        LocalDate.now(),
                        dmnProcessingType,
                        BigDecimal.valueOf(LocalDate.now().getMonth().getValue()),
                        BigDecimal.valueOf(LocalDate.now().getYear())),
                federalState);

        outputList.forEach(obj -> obj.setLicenseType(DmnLicenseTypeMapper.INSTANCE.toLicenseType(dmnLicenseType)));

        return outputList;
    }

    public List<TaxInformationOutput> getTaxInformation(FederalState federalState, boolean officeFeeAlreadyPayed) throws RulesProcessingException {
        return evaluateTaxRules(new TaxInformationInput(
                LocalDate.now(),
                BigDecimal.valueOf(0),
                DmnProcessingType.ANALOG,
                officeFeeAlreadyPayed), federalState);
    }

}
