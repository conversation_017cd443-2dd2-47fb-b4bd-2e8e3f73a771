package de.adesso.fischereiregister.registerservice.mail.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum MailTemplate {
    REGULAR_FISHING_LICENSE_CREATED(
            "mail_template.regular_fishing_license.created.subject",
            "mail_template.regular_fishing_license.created.text"
    ),
    REGULAR_FISHING_LICENSE_REPLACED(
            "mail_template.regular_fishing_license.replaced.subject",
            "mail_template.regular_fishing_license.replaced.text"
    ),
    REGULAR_FISHING_LICENSE_INFO(
            "mail_template.regular_fishing_license.info.subject",
            "mail_template.regular_fishing_license.info.text"
    ),
    VACATION_FISHING_LICENSE_CREATED(
            "mail_template.vacation_fishing_license.created.subject",
            "mail_template.vacation_fishing_license.created.text"
    ),
    VACATION_FISHING_LICENSE_EXTENDED(
            "mail_template.vacation_fishing_license.extended.subject",
            "mail_template.vacation_fishing_license.extended.text"
    ),
    VACATION_FISHING_LICENSE_INFO(
            "mail_template.vacation_fishing_license.info.subject",
            "mail_template.vacation_fishing_license.info.text"
    ),
    LIMITED_FISHING_LICENSE_CREATED(
            "mail_template.limited_fishing_license.created.subject",
            "mail_template.limited_fishing_license.created.text"
    ),
    LIMITED_FISHING_LICENSE_REPLACED(
            "mail_template.limited_fishing_license.replaced.subject",
            "mail_template.limited_fishing_license.replaced.text"
    ),
    LIMITED_FISHING_LICENSE_INFO(
            "mail_template.limited_fishing_license.info.subject",
            "mail_template.limited_fishing_license.info.text"
    ),
    FISHING_TAX_CREATED(
            "mail_template.fishing_tax.created.subject",
            "mail_template.fishing_tax.created.text"
    ),
    FISHING_TAX_INFO(
            "mail_template.fishing_tax.info.subject",
            "mail_template.fishing_tax.info.text"
    );

    private final String subjectKey;
    private final String textKey;
}