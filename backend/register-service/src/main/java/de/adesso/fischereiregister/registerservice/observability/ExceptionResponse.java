package de.adesso.fischereiregister.registerservice.observability;

import lombok.Getter;
import lombok.NonNull;

import java.io.Serial;
import java.io.Serializable;

@Getter
public class ExceptionResponse implements Serializable {

    @Serial
    private static final long serialVersionUID = -7721905614574884013L;

    private final String traceId;
    private final String errorDescription;

    ExceptionResponse(@NonNull Exception ex, String traceId) {
        this.errorDescription = ex.getMessage();
        this.traceId = traceId;
    }
}

