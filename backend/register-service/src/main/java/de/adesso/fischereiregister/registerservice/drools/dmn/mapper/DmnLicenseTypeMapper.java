package de.adesso.fischereiregister.registerservice.drools.dmn.mapper;

import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.registerservice.drools.dmn.license.configuration.input.DmnLicenseType;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "default")
public interface DmnLicenseTypeMapper {
    DmnLicenseTypeMapper INSTANCE = Mappers.getMapper(DmnLicenseTypeMapper.class);

    default DmnLicenseType toDmnLicenseType(LicenseType licenseType) {
        if (licenseType == null) {
            throw new IllegalArgumentException("License type must not be null");
        }
        return switch (licenseType) {
            case REGULAR -> DmnLicenseType.REGULAR;
            case VACATION -> DmnLicenseType.VACATION;
            case LIMITED -> DmnLicenseType.LIMITED;
            default -> throw new IllegalArgumentException("Unknown license type: " + licenseType);
        };
    }

    default LicenseType toLicenseType(DmnLicenseType dmnLicenseType) {
        if (dmnLicenseType == null) {
            throw new IllegalArgumentException("DMN License type must not be null");
        }
        return switch (dmnLicenseType) {
            case REGULAR -> LicenseType.REGULAR;
            case VACATION -> LicenseType.VACATION;
            case LIMITED -> LicenseType.LIMITED;
            default -> throw new IllegalArgumentException("Unknown license type: " + dmnLicenseType);
        };
    }
}
