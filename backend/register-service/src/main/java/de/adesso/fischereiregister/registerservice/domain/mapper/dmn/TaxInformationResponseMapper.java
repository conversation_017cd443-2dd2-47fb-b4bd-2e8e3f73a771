package de.adesso.fischereiregister.registerservice.domain.mapper.dmn;

import de.adesso.fischereiregister.registerservice.drools.dmn.tax.configuration.TaxInformationOutput;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface TaxInformationResponseMapper {

    public static final TaxInformationResponseMapper INSTANCE = Mappers.getMapper(TaxInformationResponseMapper.class);

    @Mapping(target = "price", source = "summOfTaxAmount")
    org.openapitools.model.TaxInformation toResponse(TaxInformationOutput dmnResult);

    List< org.openapitools.model.TaxInformation> toResponse(List<TaxInformationOutput> dmnResult);

}
