package de.adesso.fischereiregister.registerservice.statistics.inspections_statistics;

import de.adesso.fischereiregister.protocol.service.model.InspectionsStatisticsResult;

import java.util.List;

/**
 * Transforms {@link InspectionsStatisticsResult} objects into {@link InspectionsStatistics} objects, grouped by year.
 */
public interface InspectionsStatisticsTransformationService {

    /**
     * Converts a list of {@link InspectionsStatisticsResult} objects into a list of {@link InspectionsStatistics} objects.
     * Returns entries for all requested years, filling missing data with zeroes.
     *
     * @param statisticsResults The list of raw statistical data to transform.
     * @param yearsToQuery The complete list of years that should be included in the response.
     * @return A list of transformed {@link InspectionsStatistics} objects for all requested years.
     */
    List<InspectionsStatistics> transformToInspectionsStatistics(List<InspectionsStatisticsResult> statisticsResults, List<Integer> yearsToQuery);
}
