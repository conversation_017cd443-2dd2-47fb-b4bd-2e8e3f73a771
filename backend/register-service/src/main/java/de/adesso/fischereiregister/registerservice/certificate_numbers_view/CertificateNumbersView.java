package de.adesso.fischereiregister.registerservice.certificate_numbers_view;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.util.UUID;

@Entity
@Table
@Getter
@Setter
public class CertificateNumbersView {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id; // technical ID

	@Column(nullable = false)
	private UUID registerEntryId;

	@Column(unique = true, nullable = false)
	private String certificateNumber;
}
