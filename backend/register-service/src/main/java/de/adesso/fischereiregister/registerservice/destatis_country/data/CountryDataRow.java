package de.adesso.fischereiregister.registerservice.destatis_country.data;

import lombok.Getter;
import lombok.Setter;

import static java.util.Arrays.stream;

@Getter
@Setter
public class CountryDataRow {
    private String id;
    private String name;
    private String adjective;
    private String shortName;
    private String fullName;
    private String existenceFrom;
    private String existenceTo;
    private String isoAlpha3;
    private String isoAlpha2;
    private String continent;
    private String note;

    private static final String[] INVALID_NOTES = new String[]{"ehemaliger Staat", "–", "keinem Staat zugeordnet"};

    /**
     * Matches if the adjective (Staatsangehörigkeit) equals the given value.
     */
    public boolean matchesAdjective(String value) {
        return value != null && value.equalsIgnoreCase(this.adjective);
    }

    public boolean isValid() {
        return this.note != null && stream(INVALID_NOTES).noneMatch(this.note.strip()::equals);
    }

}
