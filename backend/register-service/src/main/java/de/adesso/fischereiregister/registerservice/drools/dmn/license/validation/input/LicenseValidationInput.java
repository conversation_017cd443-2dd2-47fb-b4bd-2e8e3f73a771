package de.adesso.fischereiregister.registerservice.drools.dmn.license.validation.input;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import de.adesso.fischereiregister.registerservice.drools.dmn.license.configuration.input.DmnLicenseType;
import lombok.Getter;

import java.time.LocalDate;

@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class LicenseValidationInput {
    private Integer age;
    private DmnLicenseType licenseType;
    private LocalDate legislativeValidityDate;

    public LicenseValidationInput(Integer age, DmnLicenseType licenseType, LocalDate legislativeValidityDate) {
        this.age = age;
        this.licenseType = licenseType;
        this.legislativeValidityDate = legislativeValidityDate;
    }
}
