package de.adesso.fischereiregister.registerservice.domain.mapper.response;

import de.adesso.fischereiregister.core.commands.results.ChangePersonCommandResult;
import de.adesso.fischereiregister.registerservice.domain.mapper.DocumentMapper;
import de.adesso.fischereiregister.registerservice.domain.mapper.PersonMapper;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.openapitools.model.ChangePersonalDataResponse;

@Mapper(componentModel = "default", uses = {DocumentMapper.class, PersonMapper.class})
public interface ChangePersonalDataResponseMapper {
    ChangePersonalDataResponseMapper INSTANCE = Mappers.getMapper(ChangePersonalDataResponseMapper.class);

    ChangePersonalDataResponse toResponse(ChangePersonCommandResult commandResult);
}
