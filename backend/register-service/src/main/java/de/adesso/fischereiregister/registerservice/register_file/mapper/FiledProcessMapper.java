package de.adesso.fischereiregister.registerservice.register_file.mapper;

import de.adesso.fischereiregister.view.filed_process.persistence.FiledProcessView;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;
import org.openapitools.model.FiledProcess;

import java.util.List;

@Mapper(uses = {FiledProcessDataMapper.class})
public interface FiledProcessMapper {
    FiledProcessMapper INSTANCE = Mappers.getMapper(FiledProcessMapper.class);

    @Mapping(target = "processType", source = "filedProcessType")
    FiledProcess toDto(FiledProcessView filedProcessView);

    List<FiledProcess> toDtos(List<FiledProcessView> filedProcessViews);
}
