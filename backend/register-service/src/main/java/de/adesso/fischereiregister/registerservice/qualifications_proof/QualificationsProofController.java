package de.adesso.fischereiregister.registerservice.qualifications_proof;

import de.adesso.fischereiregister.core.commands.CreateFishingCertificateCommand;
import de.adesso.fischereiregister.core.commands.results.CertificateCommandResult;
import de.adesso.fischereiregister.core.commands.ESCreateFishingCertificateCommand;
import de.adesso.fischereiregister.core.exceptions.CommandResultMismatchException;
import de.adesso.fischereiregister.core.exceptions.RulesProcessingException;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.user.UserDetails;
import de.adesso.fischereiregister.registerservice.domain.mapper.FederalStateMapper;
import de.adesso.fischereiregister.registerservice.domain.mapper.PersonMapper;
import de.adesso.fischereiregister.registerservice.fishing_license_export.FishingLicenseExportService;
import de.adesso.fischereiregister.registerservice.fishing_license_export.model.RenderedContent;
import de.adesso.fischereiregister.registerservice.security.UserDetailsService;
import de.adesso.fischereiregister.utils.DateUtils;
import jakarta.persistence.EntityNotFoundException;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import net.sf.jasperreports.engine.JRRuntimeException;
import org.axonframework.commandhandling.gateway.CommandGateway;
import org.openapitools.model.CertificateFromExaminationApplicationRequest;
import org.openapitools.model.CertificateFromExaminationApplicationResponse;
import org.openapitools.model.PreliminaryRegisterEntryRequest;
import org.openapitools.model.PreliminaryRegisterEntryResponse;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.util.UUID;
import java.util.concurrent.ExecutionException;

@RestController
@RequestMapping
@Slf4j
@AllArgsConstructor
public class QualificationsProofController implements api.PreliminaryRegisterEntryApi {


    private final FishingLicenseExportService fishingLicenseExportService;
    private final CommandGateway commandGateway;
    private final UserDetailsService userDetailsService;

    @SneakyThrows
    @Override
    public ResponseEntity<?> preliminaryRegisterEntriesControllerCreate(PreliminaryRegisterEntryRequest request) {

        final Person person = PersonMapper.INSTANCE.toPerson(request.getPerson());
        final LocalDate passedOnDate = DateUtils.parseGermanDate(request.getCertificate().getPassedOn());

        final CreateFishingCertificateCommand command = createCreateFishingCertificateCommand(person, passedOnDate);

        try {
            Object result = commandGateway.send(command).get();

            if (result instanceof CertificateCommandResult commandResult) {
                PreliminaryRegisterEntryResponse response = new PreliminaryRegisterEntryResponse();
                response.setFishingCertificateId(commandResult.getFishingCertificateId());

                return new ResponseEntity<>(response, HttpStatus.CREATED);
            } else {
                throw new CommandResultMismatchException(CertificateCommandResult.class, result.getClass());
            }

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Thread interrupted while creating a QualificationsProof for the registerId:{}",
                    command.registerEntryId(), e);
            throw new IllegalStateException("InterruptedException for registerId:" + command.registerEntryId(), e);
        } catch (Throwable e) {
            Throwable cause = e.getCause();
            log.error("ExecutionException while creating a QualificationsProof for registerId: {}",
                    command.registerEntryId(), e);
            if (cause != null) {
                throw cause;
            } else {
                throw e;
            }
        }

    }


    @SneakyThrows
    @Override
    public ResponseEntity<?> preliminaryRegisterEntriesControllerCreateFromExamSoftware(CertificateFromExaminationApplicationRequest request) {
        final Person person = PersonMapper.INSTANCE.toPerson(request.getPerson());
        final LocalDate passedOnDate = DateUtils.parseGermanDate(request.getCertificate().getPassedOn());
        final String federalState = FederalStateMapper.INSTANCE.toFederalState(request.getFederalState()).name();
        final ESCreateFishingCertificateCommand command = createESCreateFishingCertificateCommand(person, federalState, passedOnDate);

        try {
            Object result = commandGateway.send(command).get();

            if (result instanceof CertificateCommandResult commandResult) {
                CertificateFromExaminationApplicationResponse response = new CertificateFromExaminationApplicationResponse();
                response.setFishingCertificateId(commandResult.getFishingCertificateId());

                return new ResponseEntity<>(response, HttpStatus.CREATED);
            } else {
                throw new CommandResultMismatchException(CertificateCommandResult.class, result.getClass());
            }

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Thread interrupted while creating a QualificationsProof for the registerId:{}",
                    command.registerEntryId(), e);
            throw new IllegalStateException("InterruptedException for registerId:" + command.registerEntryId(), e);
        } catch (ExecutionException e) {
            Throwable cause = e.getCause();
            log.error("ExecutionException while creating a QualificationsProof for registerId: {}",
                    command.registerEntryId(), e);
            if (cause != null) {
                throw cause;
            } else {
                throw e;
            }
        }
    }

    @Override
    public ResponseEntity<?> fishingCertificateControllerGetPDF(String fishingCertificateId) {
        try {
            final RenderedContent renderedContent = fishingLicenseExportService.exportFishingCertificate(fishingCertificateId);
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + renderedContent.getFullFilename() + "\"")
                    .contentType(renderedContent.type().getMediaType())
                    .body(new ByteArrayResource(renderedContent.content()));
        } catch (EntityNotFoundException e) {
            log.error("EntityNotFoundException while exporting PDF", e);
            return ResponseEntity.notFound().build();
        } catch (JRRuntimeException e) {
            log.error("JRException while exporting PDF", e);
            return ResponseEntity.unprocessableEntity().build();
        } catch (RulesProcessingException e) {
            return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    private CreateFishingCertificateCommand createCreateFishingCertificateCommand(Person person, LocalDate passedOn) {
        final UserDetails userDetails = userDetailsService.getUserDetails()
                .orElseThrow(() -> {
                    log.error("Failed to retrieve user details: User details are missing or corrupted.");
                    return new IllegalStateException("The User Auth Information is missing or corrupted");
                });
        return new CreateFishingCertificateCommand(
                UUID.randomUUID(),
                passedOn,
                person,
                userDetails);
    }

    private ESCreateFishingCertificateCommand createESCreateFishingCertificateCommand(Person person, String federalState, LocalDate passedOn) {
        final UserDetails userDetails = userDetailsService.getUserDetails()
                .orElseThrow(() -> {
                    log.error("Failed to retrieve user details: User details are missing or corrupted.");
                    return new IllegalStateException("The User Auth Information is missing or corrupted");
                });
        return new ESCreateFishingCertificateCommand(
                UUID.randomUUID(),
                passedOn,
                person,
                federalState,
                userDetails
        );
    }

}
