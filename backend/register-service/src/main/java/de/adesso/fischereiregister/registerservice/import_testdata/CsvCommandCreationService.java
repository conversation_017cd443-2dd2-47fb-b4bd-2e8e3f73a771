package de.adesso.fischereiregister.registerservice.import_testdata;

import de.adesso.fischereiregister.core.commands.BanPermanentlyCommand;
import de.adesso.fischereiregister.core.commands.BanTemporarilyCommand;
import de.adesso.fischereiregister.core.commands.CreateFishingCertificateCommand;
import de.adesso.fischereiregister.core.commands.CreateLimitedLicenseCommand;
import de.adesso.fischereiregister.core.commands.CreatePersonCommand;
import de.adesso.fischereiregister.core.commands.CreateVacationLicenseCommand;
import de.adesso.fischereiregister.core.commands.DigitizeRegularLicenseCommand;
import de.adesso.fischereiregister.core.commands.MoveJurisdictionCommand;
import de.adesso.fischereiregister.core.model.Fee;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.QualificationsProof;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.consent.ConsentInfo;
import de.adesso.fischereiregister.core.model.consent.JurisdictionConsentInfo;
import de.adesso.fischereiregister.core.model.consent.TaxConsentInfo;
import de.adesso.fischereiregister.core.model.user.UserDetails;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVRecord;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;

import static de.adesso.fischereiregister.utils.DateUtils.parseGermanDate;
import static java.util.Collections.emptyList;

/**
 * Service to build commands from a csv data row.
 */
@Profile({"dev", "localdev", "test", "stage"})
@Slf4j
@Service
@AllArgsConstructor
public class CsvCommandCreationService {
    private static final String BAN_FILE_NUMBER = "TEST_FILE_0";
    private static final String USER_ID = "SYSTEM";

    private final CsvDomainCreationService csvDomainCreationService;

    public DigitizeRegularLicenseCommand toDigitizeFishingLicenseCommand(CSVRecord csvRecord)
            throws DateTimeParseException {
        final List<Tax> taxes = csvDomainCreationService.buildTaxes(csvRecord, true);
        final List<Fee> fees = csvDomainCreationService.buildFees(csvRecord);
        final Person person = csvDomainCreationService.buildPerson(csvRecord);
        final UserDetails userDetails = csvDomainCreationService.buildUserDetails(csvRecord);

        final UUID registerId = UUID.fromString(csvRecord.get(HeaderConstants.REGISTER_ID));
        final String salt = csvRecord.get(HeaderConstants.SALT);

        final List<QualificationsProof> qualificationsProofs = new ArrayList<>();

        // set certificate data if not empty
        final String fishingCertificateId = csvRecord.get(HeaderConstants.CERTIFICATE_ID);
        if (fishingCertificateId != null && !fishingCertificateId.isEmpty()) {
            QualificationsProof qualificationsProof = csvDomainCreationService.buildCertificate(csvRecord);

            qualificationsProofs.add(qualificationsProof);
        }

        final ConsentInfo consentInfo = csvDomainCreationService.buildConsentInfo();

        return new DigitizeRegularLicenseCommand(
                registerId,
                salt,
                person,
                fees,
                taxes,
                Collections.emptyList(),
                qualificationsProofs,
                consentInfo,
                userDetails);

    }

    public CreateVacationLicenseCommand toCreateVacationLicenseCommand(CSVRecord csvRecord) {
        final UUID registerId = UUID.fromString(csvRecord.get(HeaderConstants.REGISTER_ID));

        return new CreateVacationLicenseCommand(
                registerId,
                csvRecord.get(HeaderConstants.SALT),
                csvDomainCreationService.buildPerson(csvRecord),
                csvDomainCreationService.buildFees(csvRecord),
                csvDomainCreationService.buildTaxes(csvRecord, true),
                csvDomainCreationService.buildConsentInfo(),
                csvDomainCreationService.buildValidityPeriod(csvRecord, null),
                csvDomainCreationService.buildUserDetails(csvRecord)
        );
    }

    public CreateLimitedLicenseCommand toCreateLimitedlicenseCommand(CSVRecord csvRecord) {
        final UUID registerId = UUID.fromString(csvRecord.get(HeaderConstants.REGISTER_ID));

        return new CreateLimitedLicenseCommand(
                registerId,
                csvRecord.get(HeaderConstants.SALT),
                csvDomainCreationService.buildLimitedLicenseConsentInfo(),
                csvDomainCreationService.buildPerson(csvRecord),
                csvDomainCreationService.buildFees(csvRecord),
                csvDomainCreationService.buildTaxes(csvRecord, true),
                csvDomainCreationService.buildValidityPeriod(csvRecord, LocalDate.now()),
                csvDomainCreationService.buildLimitedLicenseApproval(),
                csvDomainCreationService.buildUserDetails(csvRecord)
        );
    }

    public BanTemporarilyCommand toCreateLimitedBanCommand(CSVRecord csvRecord) {
        final UUID registerId = UUID.fromString(csvRecord.get(HeaderConstants.REGISTER_ID));

        final String banFrom = csvRecord.get(HeaderConstants.BAN_FROM);
        final String banTo = csvRecord.get(HeaderConstants.BAN_UNTIL);

        final UserDetails userDetails = csvDomainCreationService.buildUserDetails(csvRecord);

        return new BanTemporarilyCommand(
                registerId, UUID.randomUUID(),
                BAN_FILE_NUMBER,
                USER_ID,
                parseGermanDate(banFrom),
                parseGermanDate(banTo),
                userDetails);
    }

    public BanPermanentlyCommand toCreateIndefiniteBanCommand(CSVRecord csvRecord) {
        final UUID registerId = UUID.fromString(csvRecord.get(HeaderConstants.REGISTER_ID));

        final String banFrom = csvRecord.get(HeaderConstants.BAN_FROM);

        final UserDetails userDetails = csvDomainCreationService.buildUserDetails(csvRecord);

        return new BanPermanentlyCommand(registerId, UUID.randomUUID(), BAN_FILE_NUMBER, USER_ID, parseGermanDate(banFrom), userDetails);
    }

    public CreateFishingCertificateCommand toCreateFishingCertificateCommand(CSVRecord csvRecord) {
        final UUID registerId = UUID.fromString(csvRecord.get(HeaderConstants.REGISTER_ID));

        final Person person = csvDomainCreationService.buildPerson(csvRecord);
        // Nationality should not be set in when a certificate is created
        person.setNationality(null);
        final LocalDate passedOn = parseGermanDate(csvRecord.get(HeaderConstants.EXAMINATION_PASSED_ON));

        final UserDetails userDetails = csvDomainCreationService.buildUserDetails(csvRecord);

        return new CreateFishingCertificateCommand(registerId,
                passedOn,
                person,
                userDetails);
    }

    public CreatePersonCommand toCreatePersonCommand(CSVRecord csvRecord) {
        final UUID registerId = UUID.fromString(csvRecord.get(HeaderConstants.REGISTER_ID));

        final Person person = csvDomainCreationService.buildPerson(csvRecord);
        final String salt = csvRecord.get(HeaderConstants.SALT);

        final List<Tax> taxes = csvDomainCreationService.buildTaxes(csvRecord, false);

        final TaxConsentInfo consentInfo = csvDomainCreationService.buildTaxConsentInfo();

        final UserDetails userDetails = csvDomainCreationService.buildUserDetails(csvRecord);

        return new CreatePersonCommand(
                registerId,
                person,
                taxes,
                List.of(),
                salt,
                consentInfo,
                userDetails);
    }


    public Record toMoveJurisdictionCommand(CSVRecord csvRecord) {
        final UUID registerId = UUID.fromString(csvRecord.get(HeaderConstants.REGISTER_ID));
        final String salt = csvRecord.get(HeaderConstants.SALT);

        final UserDetails userDetails = csvDomainCreationService.buildUserDetails(csvRecord);
        final JurisdictionConsentInfo jurisdictionConsentInfo = csvDomainCreationService.buildJurisdictionConsentInfo();

        return new MoveJurisdictionCommand(registerId, jurisdictionConsentInfo, salt, emptyList(), userDetails);
    }

}
