package de.adesso.fischereiregister.registerservice.domain.mapper;

import de.adesso.fischereiregister.core.model.Ban;
import de.adesso.fischereiregister.utils.DateUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper
public interface BanMapper {

    BanMapper INSTANCE = Mappers.getMapper(BanMapper.class);


    @Mapping(target = "from", source = "from", dateFormat = DateUtils.GERMAN_DATE_TIME_PATTERN)
    @Mapping(target = "to", source = "to", dateFormat = DateUtils.GERMAN_DATE_TIME_PATTERN)
    @Mapping(target = "banId", expression = "java(null)")
    @Mapping(target = "at", ignore = true)
    Ban toBan(org.openapitools.model.TemporaryBan ban);


    @Mapping(target = "from", source = "from", dateFormat = DateUtils.GERMAN_DATE_TIME_PATTERN)
    @Mapping(target = "to", expression = "java(null)")
    @Mapping(target = "banId", expression = "java(null)")
    @Mapping(target = "at", ignore = true)
    Ban toBan(org.openapitools.model.PermanentBan ban);


}
