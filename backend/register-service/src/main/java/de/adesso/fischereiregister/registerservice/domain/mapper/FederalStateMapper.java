package de.adesso.fischereiregister.registerservice.domain.mapper;

import de.adesso.fischereiregister.core.model.type.FederalState;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.openapitools.model.FederalStateAbbreviation;

@Mapper(componentModel = "default")
public interface FederalStateMapper {

    FederalStateMapper INSTANCE = Mappers.getMapper(FederalStateMapper.class);

    FederalState toFederalState(FederalStateAbbreviation federalStateAbbreviation);
}
