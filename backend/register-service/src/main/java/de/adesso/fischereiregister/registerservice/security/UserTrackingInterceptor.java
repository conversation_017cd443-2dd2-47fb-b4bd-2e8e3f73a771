package de.adesso.fischereiregister.registerservice.security;


import lombok.extern.slf4j.Slf4j;
import org.axonframework.commandhandling.CommandMessage;
import org.axonframework.messaging.MessageDispatchInterceptor;
import org.axonframework.messaging.MetaData;

import javax.annotation.Nonnull;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.function.BiFunction;


/**
 * checks if the command is sent in the context of a logged-in user and writes
 * the user id into the metadata of the command
 * <p></p>
 * this has to happen in a DispatchInterceptor, not in a HandlerInterceptor because
 * Axon guarantees only the execution of the DispatchInterceptor to happen in the context
 * of the caller (Controller), where we have access to the user information
 *
 * <p><strong>Important:</strong> The metadata assigned here is strictly for auditing purposes
 * and should not be relied upon for business logic or domain decisions. Embedding such information
 * into metadata ensures separation of concerns, avoiding coupling between domain logic and
 * operational metadata. If user-related information is needed for domain behavior, it must be
 * included explicitly as part of the command payload.</p>
 */
@Slf4j
public class UserTrackingInterceptor implements MessageDispatchInterceptor<CommandMessage<?>> {

    public static final String USER_ID_METADATA_TAG = "userId";
    public static final String TIMESTAMP_METADATA_TAG = "commandTimestamp";


    private final UserDetailsService userDetailsService;


    public UserTrackingInterceptor(UserDetailsService userDetailsService)
    {
        this.userDetailsService = userDetailsService;
    }


    @Nonnull
    @Override
    public BiFunction<Integer, CommandMessage<?>, CommandMessage<?>> handle(@Nonnull List<? extends CommandMessage<?>> messages) {
        return (index, command) -> {
            final Optional<String> userIdOptional = userDetailsService.getUserId();
            if(userIdOptional.isPresent())
            {
                final String userId = userIdOptional.get();
                final String timestamp = Instant.now().toString();


                final MetaData metaData = MetaData.with(USER_ID_METADATA_TAG, userId)
                        .and(TIMESTAMP_METADATA_TAG, timestamp);

                return command.andMetaData(metaData);
            } else {
                // I When importing Test data, there is no user logged in
                log.warn("There was no logged in User while executing command. appending userId 'SYSTEM' instead.");

                final String timestamp = Instant.now().toString();
                final MetaData metaData = MetaData.with(USER_ID_METADATA_TAG, "SYSTEM").and(TIMESTAMP_METADATA_TAG, timestamp);

                return command.andMetaData(metaData);
            }
        };
    }
}