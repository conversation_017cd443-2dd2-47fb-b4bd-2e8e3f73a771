package de.adesso.fischereiregister.registerservice.statistics.taxes_statistics;

import de.adesso.fischereiregister.view.taxes_statistics.persistence.TaxesStatisticsView;

import java.util.List;

public interface TaxesStatisticsTransformationService {
    /**
     * Transforms a list of TaxesStatisticsView entities into a list of TaxesStatistics objects.
     * Returns entries for all requested years, filling missing data with zeroes.
     *
     * @param statisticsViews The list of TaxesStatisticsView entities to transform.
     * @param yearsToQuery The complete list of years that should be included in the response.
     * @return A list of TaxesStatistics objects for all requested years.
     */
    List<TaxesStatistics> transformToTaxesStatistics(List<TaxesStatisticsView> statisticsViews, List<Integer> yearsToQuery);
}
