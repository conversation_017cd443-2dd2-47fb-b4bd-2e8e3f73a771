package de.adesso.fischereiregister.registerservice.security;

import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Map;
import java.util.stream.Collectors;

@Service
class JwtRolesConverterImpl implements JwtRolesConverter {

    private final SecurityProperties properties;

    public JwtRolesConverterImpl(SecurityProperties properties) {
        super();
        this.properties = properties;
    }

    @Override
    public AbstractAuthenticationToken convert(Jwt source) {
        return new JwtAuthenticationToken(source, extractResourceRoles(source));
    }

    /**
     * Extracts the realm and resource level roles from a JWT token distinguishing
     * between them using prefixes.
     */
    public Collection<GrantedAuthority> extractResourceRoles(Jwt jwt) {
        // Collection that will hold the extracted roles
        Collection<GrantedAuthority> grantedAuthorities = new ArrayList<>();

        // Realm roles
        // Get the part of the access token that holds the roles assigned on realm level
        Map<String, Collection<String>> realmAccess = jwt.getClaim(properties.getRealmAccessClaim());

        // Verify that the claim exists and is not empty
        if (realmAccess != null && !realmAccess.isEmpty()) {
            // From the realm_access claim get the roles
            Collection<String> roles = realmAccess.get(properties.getRolesClaim());
            // Check if any roles are present
            if (roles != null && !roles.isEmpty()) {
                // Iterate of the roles and add them to the granted authorities
                Collection<GrantedAuthority> realmRoles = roles.stream()
                        // Prefix all realm roles with "ROLE_realm_"
                        .map(role -> new SimpleGrantedAuthority(properties.getPrefixRealmRole() + role)).collect(Collectors.toList());
                grantedAuthorities.addAll(realmRoles);
            }
        }

        // Resource (client) roles
        // A user might have access to multiple resources all containing their own
        // roles. Therefore, it is a map of
        // resource each possibly containing a "roles" property.
        Map<String, Map<String, Collection<String>>> resourceAccess = jwt.getClaim(properties.getResourceAccessClaim());

        // Check if resources are assigned
        if (resourceAccess != null && !resourceAccess.isEmpty()) {
            // Iterate of all the resources
            resourceAccess.forEach((resource, resourceClaims) ->
                    // Iterate of the "roles" claim inside the resource claims
                    resourceClaims.get(properties.getRolesClaim()).forEach(
                            // Add the role to the granted authority prefixed with ROLE_ and the name of the
                            // resource
                            role -> grantedAuthorities
                                    .add(new SimpleGrantedAuthority(properties.getPrefixResourceRole() + resource + "_" + role)))
            );
        }

        return grantedAuthorities;
    }
}
