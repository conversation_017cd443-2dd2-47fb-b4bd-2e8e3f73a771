package de.adesso.fischereiregister.registerservice.pay_fishing_tax;

import api.TaxApi;
import de.adesso.fischereiregister.core.commands.PayFishingTaxCommand;
import de.adesso.fischereiregister.core.commands.results.FishingTaxCommandResult;
import de.adesso.fischereiregister.core.exceptions.CommandResultMismatchException;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.consent.TaxConsentInfo;
import de.adesso.fischereiregister.core.model.user.UserDetails;
import de.adesso.fischereiregister.registerservice.domain.mapper.PersonMapper;
import de.adesso.fischereiregister.registerservice.domain.mapper.TaxConsentInfoMapper;
import de.adesso.fischereiregister.registerservice.domain.mapper.TaxMapper;
import de.adesso.fischereiregister.registerservice.domain.mapper.response.ExtendTaxesResponseMapper;
import de.adesso.fischereiregister.registerservice.security.UserDetailsService;
import de.adesso.fischereiregister.utils.HashUtils;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.axonframework.commandhandling.gateway.CommandGateway;
import org.openapitools.model.ExtendTaxesRequest;
import org.openapitools.model.ExtendTaxesResponse;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.net.URI;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.ExecutionException;

@RestController
@RequestMapping
@Slf4j
@AllArgsConstructor
public class TaxController implements TaxApi {

    private final CommandGateway commandGateway;
    private final UserDetailsService userDetailsService;

    @SneakyThrows
    @Override
    public ResponseEntity<?> taxControllerExtend(String registerEntryId, ExtendTaxesRequest request) {
        final TaxConsentInfo consentInfo = TaxConsentInfoMapper.INSTANCE.toTaxConsentInfo(request.getConsentInfo());
        final Person person = PersonMapper.INSTANCE.toPerson(request.getPerson());
        final List<Tax> taxes = TaxMapper.INSTANCE.toTaxes(request.getTaxes());
        final String salt = HashUtils.gensalt();

        final UserDetails userDetails = userDetailsService.getUserDetails()
                .orElseThrow(() -> {
                    log.error("Failed to retrieve user details: User details are missing or corrupted.");
                    return new IllegalStateException("The User Auth Information is missing or corrupted");
                });

        final PayFishingTaxCommand command = new PayFishingTaxCommand(UUID.fromString(registerEntryId), consentInfo, person, salt, taxes, userDetails);
        try {
            Object result = commandGateway.send(command).get();

            if (result instanceof FishingTaxCommandResult commandResult) {
                final ExtendTaxesResponse response = ExtendTaxesResponseMapper.INSTANCE.toResponse(commandResult);
                final URI location = URI.create("/api/register-entries/" + command.registerId());

                return ResponseEntity.created(location).body(response);
            } else {
                throw new CommandResultMismatchException(FishingTaxCommandResult.class, result.getClass());
            }
        } catch (InterruptedException e) {
            log.error("Error processing Tax Payment for license for RegisterEntry with registerId {}", registerEntryId, e);
            Thread.currentThread().interrupt();
            throw new IllegalStateException(e);
        } catch (ExecutionException e) {
            Throwable cause = e.getCause();
            log.error("Error processing Tax Payment for RegisterEntry with registerId {}", registerEntryId, e);
            if (cause != null) {
                throw cause;
            } else {
                throw e;
            }
        }
    }
}
