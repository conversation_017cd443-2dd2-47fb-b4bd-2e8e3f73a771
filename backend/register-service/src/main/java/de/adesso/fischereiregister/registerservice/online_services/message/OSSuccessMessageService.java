package de.adesso.fischereiregister.registerservice.online_services.message;

import com.fasterxml.jackson.core.JsonProcessingException;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.type.FederalState;

import java.util.List;
import java.util.UUID;

public interface OSSuccessMessageService {

    void handleRegularLicenseCreationSuccess(String inboxReference, FederalState federalState, Person person, UUID registerEntryId, String salt, List<IdentificationDocument> identificationDocumentList) throws JsonProcessingException;

    void handleVacationLicenseCreationSuccess(String inboxReference, FederalState federalState, Person person, UUID registerEntryId, String salt, List<IdentificationDocument> identificationDocumentList) throws JsonProcessingException;

    void handleLicenseReplacementSuccess(String inboxReference, Person person, UUID registerEntryId, FederalState federalState, String salt, List<IdentificationDocument> identificationDocumentList) throws JsonProcessingException;

    void handleTaxPaymentSuccess(String inboxReference, Person person, UUID registerEntryId, String salt, List<IdentificationDocument> identificationDocumentList) throws JsonProcessingException;

    void handleFishingLicenseExtendedSuccess(String inboxReference, Person person, UUID registerEntryId, String salt, List<IdentificationDocument> identificationDocuments) throws JsonProcessingException;

    void handleLimitedLinseApplicationPending(String inboxReference, FederalState federalState, Person person, UUID registerEntryId);

    void handleLimitedLicenseApplicationRejected(String inboxReference, FederalState federalState, Person person, UUID registerEntryId);

    void handleLimitedLicenseCreated(String inboxReference,
                                     FederalState federalState,
                                     Person person,
                                     UUID registerEntryId,
                                     String salt,
                                     List<IdentificationDocument> identificationDocuments) throws JsonProcessingException;

}