package de.adesso.fischereiregister.registerservice.domain.mapper.response;

import de.adesso.fischereiregister.core.commands.results.FishingTaxCommandResult;
import de.adesso.fischereiregister.registerservice.domain.mapper.DocumentMapper;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.openapitools.model.ExtendTaxesResponse;

@Mapper(componentModel = "default", uses = {DocumentMapper.class})
public interface ExtendTaxesResponseMapper {

    ExtendTaxesResponseMapper INSTANCE = Mappers.getMapper(ExtendTaxesResponseMapper.class);

    ExtendTaxesResponse toResponse(FishingTaxCommandResult commandResult);

}
