package de.adesso.fischereiregister.registerservice.drools.dmn.mapper;

import de.adesso.fischereiregister.core.model.type.PaymentType;
import de.adesso.fischereiregister.registerservice.drools.dmn.model.DmnProcessingType;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "default")
public interface DmnProcessingTypeMapper {
    DmnProcessingTypeMapper INSTANCE = Mappers.getMapper(DmnProcessingTypeMapper.class);

    default DmnProcessingType toDmnProcessingType(PaymentType paymentType) {
        return switch (paymentType) {
            case CASH, CARD, BANK_TRANSFER -> DmnProcessingType.ANALOG;
            case ONLINE -> DmnProcessingType.DIGITAL;
        };
    }
}
