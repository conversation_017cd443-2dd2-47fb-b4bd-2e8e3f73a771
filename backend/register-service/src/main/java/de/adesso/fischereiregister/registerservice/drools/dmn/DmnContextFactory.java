package de.adesso.fischereiregister.registerservice.drools.dmn;

import de.adesso.fischereiregister.registerservice.drools.dmn.license.configuration.input.LicenseInformationInput;
import de.adesso.fischereiregister.registerservice.drools.dmn.license.validation.input.LicenseValidationInput;
import de.adesso.fischereiregister.registerservice.drools.dmn.qualifications_proof.QualificationsProofValidationInput;
import de.adesso.fischereiregister.registerservice.drools.dmn.tax.configuration.TaxInformationInput;
import de.adesso.fischereiregister.registerservice.drools.dmn.tax.validation.TaxValidationInput;
import org.kie.dmn.api.core.DMNContext;
import org.kie.dmn.api.core.DMNRuntime;

public class DmnContextFactory {

    public static final String GESETZLICHE_GUELTIGKEIT = "Gesetzliche Gültigkeit";
    public static final String VERARBEITUNGSTYP = "Verarbeitungstyp";
    public static final String LIZENZTYP = "Lizenztyp";
    public static final String TYP_DES_FISCHEREISCHEINS = "Typ des Fischereischeins";
    public static final String AKTUELLES_JAHR = "aktuellesJahr";
    public static final String AKTUELLER_MONAT = "aktuellerMonat";
    public static final String JAHRE = "Jahre";
    public static final String ALTER_DER_BEANTRAGENDEN_PERSON = "Alter der beantragenden Person";
    private static final String TAX_YEAR = "taxYear";
    private static final String CALCULATION_YEAR = "calculationYear";
    public static final String ZUSATZZAHLUNG_GEBUEHR = "Gebühr Behörde schon bezahlt";

    private DmnContextFactory() {
    }

    public static <T> DMNContext createContext(T input, DMNRuntime dmnRuntime) {
        DMNContext dmnContext = dmnRuntime.newContext();

        if (input instanceof TaxInformationInput taxInput) {
            dmnContext.set(GESETZLICHE_GUELTIGKEIT, taxInput.getLegislativeValidityDate());
            dmnContext.set(VERARBEITUNGSTYP, taxInput.getDmnProcessingType().getValue());
            dmnContext.set(JAHRE, taxInput.getYears());
            dmnContext.set(ZUSATZZAHLUNG_GEBUEHR, taxInput.getOfficeFeeAlreadyPayed());

        } else if (input instanceof LicenseInformationInput licenseInput) {
            dmnContext.set(GESETZLICHE_GUELTIGKEIT, licenseInput.getLegislativeValidityDate());
            dmnContext.set(VERARBEITUNGSTYP, licenseInput.getDmnProcessingType().getValue());
            dmnContext.set(LIZENZTYP, licenseInput.getDmnLicenseType().getValue());
            dmnContext.set(AKTUELLES_JAHR, licenseInput.getCurrentYear());
            dmnContext.set(AKTUELLER_MONAT, licenseInput.getCurrentMonth());

        } else if (input instanceof LicenseValidationInput fishingLicenseDmnInput) {
            dmnContext.set(GESETZLICHE_GUELTIGKEIT, fishingLicenseDmnInput.getLegislativeValidityDate());
            dmnContext.set(ALTER_DER_BEANTRAGENDEN_PERSON, fishingLicenseDmnInput.getAge());
            dmnContext.set(TYP_DES_FISCHEREISCHEINS, fishingLicenseDmnInput.getLicenseType().getValue());

        } else if (input instanceof QualificationsProofValidationInput qualificationsProofValidationInput) {
            dmnContext.set(GESETZLICHE_GUELTIGKEIT, qualificationsProofValidationInput.getLegislativeValidityDate());
            dmnContext.set(ALTER_DER_BEANTRAGENDEN_PERSON, qualificationsProofValidationInput.getAge());

        } else if (input instanceof TaxValidationInput fishingLicenseDmnInput) {
            dmnContext.set(GESETZLICHE_GUELTIGKEIT, fishingLicenseDmnInput.getLegislativeValidityDate());
            dmnContext.set(ALTER_DER_BEANTRAGENDEN_PERSON, fishingLicenseDmnInput.getAge());
            dmnContext.set(TAX_YEAR, fishingLicenseDmnInput.getTaxYear());
            dmnContext.set(CALCULATION_YEAR, fishingLicenseDmnInput.getCalculationYear());

        } else {
            throw new IllegalArgumentException("Unsupported input type: " + input.getClass().getSimpleName());
        }

        return dmnContext;
    }
}
