package de.adesso.fischereiregister.registerservice.domain.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "default")
public interface ValidityPeriodMapper {
    ValidityPeriodMapper INSTANCE = Mappers.getMapper(ValidityPeriodMapper.class);

    de.adesso.fischereiregister.core.model.ValidityPeriod toValidityPeriod(org.openapitools.model.ValidityPeriod dto);
}
