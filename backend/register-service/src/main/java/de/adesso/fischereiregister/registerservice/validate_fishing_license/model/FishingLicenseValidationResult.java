package de.adesso.fischereiregister.registerservice.validate_fishing_license.model;

import de.adesso.fischereiregister.core.model.type.LicenseType;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public class FishingLicenseValidationResult {

    private String licenseNumber;
    private LicenseType licenseType;
    private FishingLicenseValidationStatus licenseStatus;
    private String licenseNote;
    private Boolean taxValid;
    private String taxNote;
    private String title;
    private String givenNames;
    private String surname;
    private String birthdate;
}
