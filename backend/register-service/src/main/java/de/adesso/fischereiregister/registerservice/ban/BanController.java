package de.adesso.fischereiregister.registerservice.ban;

import de.adesso.fischereiregister.core.commands.BanPermanentlyCommand;
import de.adesso.fischereiregister.core.commands.BanTemporarilyCommand;
import de.adesso.fischereiregister.core.commands.UnbanCommand;
import de.adesso.fischereiregister.core.commands.results.BanCommandResult;
import de.adesso.fischereiregister.core.exceptions.CommandResultMismatchException;
import de.adesso.fischereiregister.core.model.Ban;
import de.adesso.fischereiregister.core.model.user.UserDetails;
import de.adesso.fischereiregister.registerservice.domain.mapper.BanMapper;
import de.adesso.fischereiregister.registerservice.domain.mapper.response.BanResponseMapper;
import de.adesso.fischereiregister.registerservice.security.UserDetailsService;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.axonframework.commandhandling.gateway.CommandGateway;
import org.openapitools.model.CreateBanResponse;
import org.openapitools.model.PermanentBanRequest;
import org.openapitools.model.TemporaryBanRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.UUID;
import java.util.concurrent.ExecutionException;

@RestController
@RequestMapping
@Slf4j
@AllArgsConstructor
public class BanController implements api.BanApi {

    private final UserDetailsService userDetailsService;
    private final CommandGateway commandGateway;

    @SneakyThrows
    @Override
    public ResponseEntity<?> banControllerDelete(String registerEntryId) {
        final UserDetails userDetails = getUserDetails();

        final UnbanCommand unbanCommand = new UnbanCommand(UUID.fromString(registerEntryId), userDetails);

        try {
            commandGateway.send(unbanCommand).get();
        } catch (InterruptedException e) {
            log.error("Failed to delete ban", e);
            Thread.currentThread().interrupt();
            throw e;
        } catch (ExecutionException e) {
            Throwable cause = e.getCause();

            log.error("Failed to delete ban", e);

            if (cause != null) {
                throw cause;
            } else {
                throw e;
            }
        }

        return ResponseEntity.ok().build();
    }

    @SneakyThrows
    @Override
    public ResponseEntity<?> banControllerCreatePermanent(String registerEntryId, PermanentBanRequest banRequest) {

        final Ban ban = BanMapper.INSTANCE.toBan(banRequest.getBan());
        final UserDetails userDetails = getUserDetails();

        final BanPermanentlyCommand command = new BanPermanentlyCommand(
                UUID.fromString(registerEntryId),
                ban.getBanId(),
                ban.getFileNumber(),
                ban.getReportedBy(),
                ban.getFrom(),
                userDetails
        );

        try {
            final BanCommandResult commandResult = sendCommand(command);
            final CreateBanResponse response = BanResponseMapper.INSTANCE.toResponse(commandResult);
            return ResponseEntity.status(HttpStatus.CREATED).body(response);
        } catch (InterruptedException e) {
            log.error("Failed to create permanent ban", e);
            Thread.currentThread().interrupt();
            throw e;
        } catch (ExecutionException e) {
            Throwable cause = e.getCause();

            log.error("Failed to create permanent ban", e);

            if (cause != null) {
                throw cause;
            } else {
                throw e;
            }
        }
    }

    @SneakyThrows
    @Override
    public ResponseEntity<?> banControllerCreateTemporary(String registerEntryId, TemporaryBanRequest banRequest) {

        final Ban ban = BanMapper.INSTANCE.toBan(banRequest.getBan());
        final UserDetails userDetails = getUserDetails();

        final BanTemporarilyCommand command = new BanTemporarilyCommand(
                UUID.fromString(registerEntryId),
                ban.getBanId(),
                ban.getFileNumber(),
                ban.getReportedBy(),
                ban.getFrom(),
                ban.getTo(),
                userDetails
        );

        try {
            final BanCommandResult commandResult = sendCommand(command);
            final CreateBanResponse response = BanResponseMapper.INSTANCE.toResponse(commandResult);
            return ResponseEntity.status(HttpStatus.CREATED).body(response);
        } catch (InterruptedException e) {
            log.error("Failed to create temporary ban", e);
            Thread.currentThread().interrupt();
            throw e;
        } catch (ExecutionException e) {
            Throwable cause = e.getCause();

            log.error("Failed to create temporary ban", e);

            if (cause != null) {
                throw cause;
            } else {
                throw e;
            }
        }
    }

    private BanCommandResult sendCommand(Object command) throws ExecutionException, InterruptedException, CommandResultMismatchException {
        final Object result = commandGateway.send(command).get();
        if (result instanceof BanCommandResult commandResult) {
            return commandResult;
        } else {
            throw new CommandResultMismatchException(BanCommandResult.class, result.getClass());
        }
    }

    private UserDetails getUserDetails() {
        return userDetailsService.getUserDetails()
                .orElseThrow(() -> {
                    log.error("Failed to retrieve user details: User details are missing or corrupted.");
                    return new IllegalStateException("The User Auth Information is missing or corrupted");
                });
    }
}