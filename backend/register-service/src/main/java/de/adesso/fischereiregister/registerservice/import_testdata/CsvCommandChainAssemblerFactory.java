package de.adesso.fischereiregister.registerservice.import_testdata;

import lombok.AllArgsConstructor;
import org.apache.commons.csv.CSVRecord;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
@Profile({"dev", "localdev", "test", "stage"})
public class CsvCommandChainAssemblerFactory {

    private final CsvCommandCreationService csvCommandCreationService;
    private final CsvDomainCreationService csvDomainCreationService;

    public CsvCommandChainAssembler createAssembler(CSVRecord csvRecord) {
        return new CsvCommandChainAssembler(csvDomainCreationService, csvCommandCreationService, csvRecord);
    }
}
