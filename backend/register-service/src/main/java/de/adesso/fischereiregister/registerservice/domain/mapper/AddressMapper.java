package de.adesso.fischereiregister.registerservice.domain.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "default")
public interface AddressMapper {
    AddressMapper INSTANCE = Mappers.getMapper(AddressMapper.class);

    de.adesso.fischereiregister.core.model.Address toAddress(org.openapitools.model.Address dto);

    de.adesso.fischereiregister.core.model.Address toAddress(org.openapitools.model.AddressOS dto);
}
