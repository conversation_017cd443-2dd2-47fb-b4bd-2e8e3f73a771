<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet id="1.0.8" author="paul.lindt">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="preliminary_register_entry_view" />
            <columnExists tableName="preliminary_register_entry_view" columnName="examiner" />
            <not>
                <columnExists tableName="preliminary_register_entry_view" columnName="examiner_id" />
            </not>
        </preConditions>
        <renameColumn tableName="preliminary_register_entry_view" oldColumnName="examiner"
                      newColumnName="examiner_id" />
        <addColumn tableName="preliminary_register_entry_view">
            <column name="fishing_certificate_id" type="varchar(255)" />
        </addColumn>

    </changeSet>

</databaseChangeLog>