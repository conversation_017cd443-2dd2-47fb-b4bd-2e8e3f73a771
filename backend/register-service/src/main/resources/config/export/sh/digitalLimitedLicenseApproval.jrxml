<!-- Created with Jaspersoft Studio version 7.0.3.final using JasperReports Library version 7.0.3-41034ca841d452f3305ba55b9042260aaa1ab5dd  -->
<jasperReport name="digitalLimitedLicenseApproval" language="java" columnCount="1" pageWidth="595" pageHeight="842" orientation="Landscape" columnWidth="471" leftMargin="62" rightMargin="62" topMargin="52" bottomMargin="0" uuid="8b569b21-d605-4652-a54b-3e3479dc8334">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<property name="net.sf.jasperreports.export.pdfa.conformance" value="pdfa1a"/>
	<property name="net.sf.jasperreports.export.pdfa.icc.profile.path" value="config/export/common/sRGB_v4_ICC_preference.icc"/>
	<property name="net.sf.jasperreports.export.pdf.tagged" value="true"/>
	<property name="net.sf.jasperreports.export.pdf.tag.language" value="de-DE"/>
	<property name="net.sf.jasperreports.export.pdf.classic.document.language" value="de-DE"/>
	<property name="net.sf.jasperreports.export.pdf.display.metadata.title" value="true"/>
	<property name="net.sf.jasperreports.default.pdf.embedded" value="true"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<style name="primaryText" default="true" mode="Transparent" forecolor="#000000" fontName="Arial" fontSize="10.0"/>
	<parameter name="fileNumber" class="java.lang.String"/>
	<parameter name="employeePersonalSign" class="java.lang.String"/>
	<parameter name="formattedDateToday" class="java.lang.String"/>
	<parameter name="subject" class="java.lang.String"/>
	<parameter name="formattedLimitedLicenseApplicationDate" class="java.lang.String"/>
	<parameter name="subjectSecondPart" class="java.lang.String"/>
	<parameter name="salutation" class="java.lang.String"/>
	<parameter name="firstParagraph" class="java.lang.String"/>
	<parameter name="durationParagraph" class="java.lang.String"/>
	<parameter name="headerNotes" class="java.lang.String"/>
	<parameter name="textNotesBulletPoints" class="java.lang.String"/>
	<parameter name="headerFeesAndTaxes" class="java.lang.String"/>
	<parameter name="textFeesAndTaxes" class="java.lang.String"/>
	<parameter name="paymentAccount" class="java.lang.String"/>
	<parameter name="closing" class="java.lang.String"/>
	<parameter name="headerInstructionsOnLegalRemedies" class="java.lang.String"/>
	<parameter name="textInstructionsOnLegalRemedies" class="java.lang.String"/>
	<parameter name="footer" class="java.lang.String"/>
	<parameter name="officeAddress" class="java.lang.String"/>
	<parameter name="officePhone" class="java.lang.String"/>
	<parameter name="officeEmail" class="java.lang.String"/>
	<parameter name="nameEmployee" class="java.lang.String"/>
	<parameter name="deliveryAddress" class="java.lang.String"/>
	<parameter name="previewText" class="java.lang.String"/>
	<parameter name="isPreview" class="java.lang.Boolean"/>
	<title splitType="Stretch"/>
	<detail>
		<band height="578" splitType="Stretch">
			<element kind="frame" uuid="369f8187-ada7-4fba-9888-6d1a8ff507e3" x="0" y="211" width="472" height="367">
				<element kind="textField" uuid="95fcb02f-c82a-46fb-b181-e6f69e62d6c6" x="0" y="-153" width="240" height="26" markup="html" fontSize="8.0" blankWhenNull="true">
					<expression><![CDATA[$P{officeAddress}]]></expression>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</element>
				<element kind="textField" uuid="54fa3b49-f3cb-45a0-b2e8-8a1a86cdda68" x="0" y="-120" width="240" height="70" markup="html" fontSize="12.0" blankWhenNull="true">
					<expression><![CDATA[$P{deliveryAddress}]]></expression>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</element>
				<element kind="textField" uuid="7e0fa093-47fa-4927-8f8f-aca2e5dc34d1" x="0" y="-4" width="472" height="20" markup="html" fontSize="12.0" textAdjust="StretchHeight" blankWhenNull="true" bold="true">
					<expression><![CDATA[$P{subject}]]></expression>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</element>
				<element kind="textField" uuid="99f06511-0293-4f15-8a8b-9fb9325457ab" positionType="Float" x="0" y="26" width="472" height="19" markup="html" fontSize="12.0" textAdjust="StretchHeight" blankWhenNull="true">
					<expression><![CDATA[$P{subjectSecondPart}]]></expression>
				</element>
				<element kind="textField" uuid="9952ab78-bd39-4935-b5f3-0d83d49f8d31" positionType="Float" x="0" y="65" width="472" height="290" markup="html" fontSize="12.0" textAdjust="StretchHeight" blankWhenNull="true">
					<paragraph tabStopWidth="0"/>
					<expression><![CDATA[
                        $P{salutation} + $P{firstParagraph} + $P{durationParagraph} + $P{headerNotes} + $P{textNotesBulletPoints} + $P{headerFeesAndTaxes} + $P{textFeesAndTaxes} + $P{paymentAccount} + $P{closing} + $P{nameEmployee} + $P{headerInstructionsOnLegalRemedies} + $P{textInstructionsOnLegalRemedies}]]></expression>
					<property name="net.sf.jasperreports.allow.element.overlap" value="false"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.tabStopWidth" value="px"/>
				</element>
			</element>
			<element kind="image" uuid="3d5e62ac-abdc-459b-8e66-aa4693413791" x="-19" y="10" width="89" height="29">
				<expression><![CDATA[
                    this.getClass().getClassLoader().getResourceAsStream("config/export/sh/assets/slogan-logo-black.png")]]></expression>
				<hyperlinkTooltipExpression><![CDATA[
                    "Schleswig-Holstein der echte Norden"]]></hyperlinkTooltipExpression>
				<property name="net.sf.jasperreports.export.pdf.tag.figure" value="full"/>
				<property name="net.sf.jasperreports.accessibility.role" value="image"/>
			</element>
			<element kind="image" uuid="20a3da5d-4144-43bb-b6b4-d60f73284857" stretchType="NoStretch" x="230" y="0" width="241" height="53">
				<expression><![CDATA[
                    this.getClass().getClassLoader().getResourceAsStream("config/export/sh/state-logo-large.png")]]></expression>
				<hyperlinkTooltipExpression><![CDATA[
                    "Schleswig - Holstein Landesamt für Landwirftschaft und nachhaltige Landentwicklung"]]></hyperlinkTooltipExpression>
				<property name="net.sf.jasperreports.export.pdf.tag.figure" value="full"/>
				<property name="net.sf.jasperreports.accessibility.role" value="image"/>
			</element>
			<element kind="textField" uuid="29f8fd6e-3259-4824-bead-137caeaeb1f0" positionType="Float" x="240" y="72" width="232" height="70" markup="html" fontSize="9.5" blankWhenNull="true" hTextAlign="Right">
				<expression><![CDATA["Ihre Nachricht vom: "+$P{formattedLimitedLicenseApplicationDate} + "\n" +
"Mein Zeichen: "+ $P{fileNumber} + " - " + $P{employeePersonalSign} + "\n" +
$P{nameEmployee} + "\n" +
"E-Mail: "+$P{officeEmail} + "\n" +
"Telefon: "+$P{officePhone}]]></expression>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
			</element>
			<element kind="textField" uuid="2c51f344-e38f-4c31-bc6e-3ba59a2178c7" x="330" y="183" width="142" height="20" markup="html" fontSize="12.0" blankWhenNull="true" hTextAlign="Right">
				<expression><![CDATA[$P{formattedDateToday}]]></expression>
			</element>
			<property name="com.jaspersoft.studio.layout"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</band>
	</detail>
	<pageFooter height="212">
		<element kind="textField" uuid="1057a334-130c-4033-82c6-22693515da3a" positionType="Float" x="0" y="100" width="472" height="50" markup="html" fontSize="8.0" textAdjust="StretchHeight" blankWhenNull="true">
			<expression><![CDATA[$P{footer}]]></expression>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</element>
		<element kind="textField" uuid="7efb1c8a-0f99-46bd-a994-df4be9b9c9c6" mode="Opaque" x="-62" y="162" width="596" height="50" forecolor="#FFFFFF" backcolor="#FF0000" fontSize="18.0" hTextAlign="Center" vTextAlign="Middle">
			<printWhenExpression><![CDATA[$P{isPreview} == true]]></printWhenExpression>
			<expression><![CDATA[$P{previewText}]]></expression>
		</element>
		<property name="com.jaspersoft.studio.unit.height" value="px"/>
	</pageFooter>
</jasperReport>
