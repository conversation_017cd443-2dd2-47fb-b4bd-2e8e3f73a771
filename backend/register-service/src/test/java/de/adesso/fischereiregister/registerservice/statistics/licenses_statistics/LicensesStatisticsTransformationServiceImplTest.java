package de.adesso.fischereiregister.registerservice.statistics.licenses_statistics;

import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.core.model.type.SubmissionType;
import de.adesso.fischereiregister.view.licenses_statistics.persistance.LicensesStatisticsView;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

class LicensesStatisticsTransformationServiceImplTest {

    private LicensesStatisticsTransformationServiceImpl transformationService;

    @BeforeEach
    void setUp() {
        transformationService = new LicensesStatisticsTransformationServiceImpl();
    }

    @Test
    @DisplayName("LicensesStatisticsTransformationServiceImpl.transformToLicensesStatistics Should correctly group and sum counts by year and submission type, regardless of license type and region")
    void transformToLicensesStatistics_ShouldGroupAndSumCountsCorrectly() {
        // given
        LicensesStatisticsView entry1 = createStatisticsView(2023, SubmissionType.ONLINE, LicenseType.REGULAR, "BE", "Senatsverwaltung für Umwelt, Verkehr und Klimaschutz", 5);
        LicensesStatisticsView entry2 = createStatisticsView(2023, SubmissionType.ANALOG, LicenseType.REGULAR, "BE", "Senatsverwaltung für Umwelt, Verkehr und Klimaschutz", 3);
        LicensesStatisticsView entry3 = createStatisticsView(2023, SubmissionType.ONLINE, LicenseType.VACATION, "NW", "Regierungspräsidium Düsseldorf", 4);
        LicensesStatisticsView entry4 = createStatisticsView(2023, SubmissionType.ONLINE, LicenseType.LIMITED, "HE", "Regierungspräsidium Gießen", 2);
        LicensesStatisticsView entry5 = createStatisticsView(2024, SubmissionType.ONLINE, LicenseType.REGULAR, "NW", "Regierungspräsidium Freiburg", 11);
        LicensesStatisticsView entry6 = createStatisticsView(2024, SubmissionType.ANALOG, LicenseType.VACATION, "HE", "Regierungspräsidium Gießen", 2);
        LicensesStatisticsView entry7 = createStatisticsView(2023, SubmissionType.ANALOG, LicenseType.LIMITED, "SA", "Sächsisches Staatsministerium für Energie, Klimaschutz, Umwelt und Landwirtschaft", 8);
        LicensesStatisticsView entry8 = createStatisticsView(2024, SubmissionType.ANALOG, LicenseType.VACATION, "HE", "Regierungspräsidium Gießen", 10);
        LicensesStatisticsView entry9 = createStatisticsView(2024, SubmissionType.ONLINE, LicenseType.REGULAR, "NW", "Regierungspräsidium Freiburg", 12);

        List<LicensesStatisticsView> views = List.of(entry1, entry2, entry3, entry4, entry5, entry6, entry7, entry8, entry9);
        List<Integer> yearsToQuery = List.of(2023, 2024);

        // when
        List<LicensesStatistics> result = transformationService.transformToLicensesStatistics(views, yearsToQuery);

        // then
        assertThat(result).hasSize(2);

        // Check 2023 statistics
        LicensesStatistics stats2023 = result.stream()
                .filter(s -> s.year() == 2023)
                .findFirst()
                .orElseThrow();
        assertThat(stats2023.year()).isEqualTo(2023);
        assertThat(stats2023.data()).hasSize(2); // ONLINE and ANALOG
        assertThat(getTotalCount(stats2023, SubmissionType.ONLINE)).isEqualTo(11); // 5 + 4 + 2
        assertThat(getTotalCount(stats2023, SubmissionType.ANALOG)).isEqualTo(11); // 3 + 8

        // Check 2024 statistics
        LicensesStatistics stats2024 = result.stream()
                .filter(s -> s.year() == 2024)
                .findFirst()
                .orElseThrow();
        assertThat(stats2024.year()).isEqualTo(2024);
        assertThat(stats2024.data()).hasSize(2); // ONLINE and ANALOG
        assertThat(getTotalCount(stats2024, SubmissionType.ONLINE)).isEqualTo(23); // 11 + 12
        assertThat(getTotalCount(stats2024, SubmissionType.ANALOG)).isEqualTo(12); // 2 + 10
    }

    @Test
    @DisplayName("LicensesStatisticsTransformationServiceImpl.transformToLicensesStatistics Should handle empty list")
    void transformToLicensesStatistics_ShouldHandleEmptyList() {
        // given
        List<LicensesStatisticsView> views = List.of();
        List<Integer> yearsToQuery = List.of(2023, 2024);

        // when
        List<LicensesStatistics> result = transformationService.transformToLicensesStatistics(views, yearsToQuery);

        // then
        assertThat(result).hasSize(2); // Should return entries for all requested years with zero values

        result.forEach(stats -> {
            assertThat(stats.data()).hasSize(2); // ONLINE and ANALOG
            assertThat(getTotalCount(stats, SubmissionType.ONLINE)).isEqualTo(0);
            assertThat(getTotalCount(stats, SubmissionType.ANALOG)).isEqualTo(0);
        });
    }

    @Test
    @DisplayName("LicensesStatisticsTransformationServiceImpl.transformToLicensesStatistics Should handle single entry")
    void transformToLicensesStatistics_ShouldHandleSingleEntry() {
        // given
        LicensesStatisticsView entry = createStatisticsView(2023, SubmissionType.ONLINE, LicenseType.REGULAR, "BE", "Senatsverwaltung für Umwelt", 5);
        List<LicensesStatisticsView> views = List.of(entry);
        List<Integer> yearsToQuery = List.of(2023);

        // when
        List<LicensesStatistics> result = transformationService.transformToLicensesStatistics(views, yearsToQuery);

        // then
        assertThat(result).hasSize(1);
        assertThat(result.get(0).year()).isEqualTo(2023);
        assertThat(result.get(0).data()).hasSize(2); // ONLINE and ANALOG
        assertThat(getTotalCount(result.get(0), SubmissionType.ONLINE)).isEqualTo(5);
        assertThat(getTotalCount(result.get(0), SubmissionType.ANALOG)).isEqualTo(0);
    }

    @Test
    @DisplayName("LicensesStatisticsTransformationServiceImpl.transformToLicensesStatistics Should handle empty years list")
    void transformToLicensesStatistics_ShouldHandleEmptyYearsList() {
        // given
        LicensesStatisticsView entry = createStatisticsView(2023, SubmissionType.ONLINE, LicenseType.REGULAR, "BE", "Senatsverwaltung für Umwelt", 5);
        List<LicensesStatisticsView> views = List.of(entry);
        List<Integer> yearsToQuery = List.of();

        // when
        List<LicensesStatistics> result = transformationService.transformToLicensesStatistics(views, yearsToQuery);

        // then
        assertThat(result).isEmpty();
    }

    @Test
    @DisplayName("LicensesStatisticsTransformationServiceImpl.transformToLicensesStatistics Should fill missing years with zero values")
    void transformToLicensesStatistics_ShouldFillMissingYearsWithZeroValues() {
        // given
        LicensesStatisticsView entry = createStatisticsView(2023, SubmissionType.ONLINE, LicenseType.REGULAR, "BE", "Senatsverwaltung für Umwelt", 5);
        // Note: No data for 2024
        List<LicensesStatisticsView> views = List.of(entry);
        List<Integer> yearsToQuery = List.of(2023, 2024);

        // when
        List<LicensesStatistics> result = transformationService.transformToLicensesStatistics(views, yearsToQuery);

        // then
        assertThat(result).hasSize(2);

        // Check 2023 has data
        LicensesStatistics stats2023 = result.stream()
                .filter(s -> s.year() == 2023)
                .findFirst()
                .orElseThrow();
        assertThat(getTotalCount(stats2023, SubmissionType.ONLINE)).isEqualTo(5);

        // Check 2024 has zero values
        LicensesStatistics stats2024 = result.stream()
                .filter(s -> s.year() == 2024)
                .findFirst()
                .orElseThrow();
        assertThat(getTotalCount(stats2024, SubmissionType.ONLINE)).isEqualTo(0);
        assertThat(getTotalCount(stats2024, SubmissionType.ANALOG)).isEqualTo(0);
    }

    @Test
    @DisplayName("LicensesStatisticsTransformationServiceImpl.transformToLicensesStatistics Should sort results by year in descending order")
    void transformToLicensesStatistics_ShouldSortByYearDescending() {
        // given
        LicensesStatisticsView entry1 = createStatisticsView(2022, SubmissionType.ONLINE, LicenseType.REGULAR, "BE", "Office1", 1);
        LicensesStatisticsView entry2 = createStatisticsView(2024, SubmissionType.ONLINE, LicenseType.REGULAR, "BE", "Office1", 3);
        LicensesStatisticsView entry3 = createStatisticsView(2023, SubmissionType.ONLINE, LicenseType.REGULAR, "BE", "Office1", 2);

        List<LicensesStatisticsView> views = List.of(entry1, entry2, entry3);
        List<Integer> yearsToQuery = List.of(2022, 2023, 2024);

        // when
        List<LicensesStatistics> result = transformationService.transformToLicensesStatistics(views, yearsToQuery);

        // then
        assertThat(result).hasSize(3);
        assertThat(result.get(0).year()).isEqualTo(2024); // First should be highest year
        assertThat(result.get(1).year()).isEqualTo(2023);
        assertThat(result.get(2).year()).isEqualTo(2022); // Last should be lowest year
    }

    private int getTotalCount(LicensesStatistics stats, SubmissionType submissionType) {
        return stats.data().stream()
                .filter(entry -> entry.submissionType() == submissionType)
                .mapToInt(LicensesStatisticsDataEntry::count)
                .sum();
    }

    private LicensesStatisticsView createStatisticsView(int year, SubmissionType submissionType, LicenseType licenseType, String federalState, String office, int count) {
        LicensesStatisticsView view = new LicensesStatisticsView();
        view.setYear(year);
        view.setSource(submissionType);
        view.setLicenseType(licenseType);
        view.setFederalState(federalState);
        view.setOffice(office);
        view.setCount(count);
        return view;
    }
}
