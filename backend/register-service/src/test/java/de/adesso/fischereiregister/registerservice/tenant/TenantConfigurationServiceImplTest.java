package de.adesso.fischereiregister.registerservice.tenant;

import com.fasterxml.jackson.databind.JsonNode;
import de.adesso.fischereiregister.core.model.type.FederalState;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.hamcrest.CoreMatchers.containsString;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(MockitoExtension.class)
public class TenantConfigurationServiceImplTest {

    private final TenantConfigurationServiceImpl tenantConfigurationService = new TenantConfigurationServiceImpl();

    @Test
    @DisplayName("TenantConfigurationServiceImpl.loadTenantConfiguration() should load the tenant configuration.")
    public void testGetTenantConfiguration() {
        // Given
        String partialExpectedJson = "\"login\":{\"button\":\"Schleswig-Holstein-ButtonValue\"}";

        // When
        JsonNode tenantConfiguration = tenantConfigurationService.getTenantConfiguration(FederalState.SH);

        // Then
        assertThat(tenantConfiguration.toString(), containsString(partialExpectedJson));
    }

    @Test
    @DisplayName("TenantConfigurationServiceImpl.loadTenantConfiguration() should load the the default config is no specific config is defined yet.")
    void testGetTenantConfigurationDefault() {
        // Given
        String partialExpectedJson = "\"login\":{\"button\":\"DefaultButtonValue\"}";

        // When
        JsonNode tenantConfiguration = tenantConfigurationService.getTenantConfiguration(FederalState.NI);

        // Then
        assertThat(tenantConfiguration.toString(), containsString(partialExpectedJson));
    }


    @Test
    @DisplayName("TenantConfigurationServiceImpl.getValue() should return the correct value for a given key.")
    void testGetValue() {
        // Given
        String key = "header.federal_state_logo.url";
        String expectedValue = "https://upload.wikimedia.org/wikipedia/commons/thumb/e/e4/Schleswig-Holstein.svg/800px-Schleswig-Holstein.svg.png";

        // When
        String actualValue = tenantConfigurationService.getValue(FederalState.SH, key);

        // Then
        assertEquals(expectedValue, actualValue);
    }


    @Test
    @DisplayName("TenantConfigurationServiceImpl.getValue() should return the default value for a given key if not defined yet.")
    void testGetValueDefault() {
        // Given
        String key = "header.federal_state_logo.url";
        String expectedValue = "URL";

        // When
        String actualValue = tenantConfigurationService.getValue(FederalState.NI, key);

        // Then
        assertEquals(expectedValue, actualValue);
    }


    @Test
    @DisplayName("TenantConfigurationServiceImpl.getValue() should return the key if the value is not in the config existent")
    void testGetValueNonExistent() {
        // Given
        String key = "non_existent.key";

        // When
        String actualValue = tenantConfigurationService.getValue(FederalState.SH, key);

        // Then
        assertEquals(key, actualValue);
    }

    @Test
    @DisplayName("TenantConfigurationServiceImpl.getTenantConfigurationsForNamespace() should return the merged tenant configurations for a given namespace.")
    void testNamespaceMerging() {
        // Given
        final String namespace = "login";

        // When
        final var actualValue = tenantConfigurationService.getTenantConfigurationsForNamespace(namespace);

        // Then
        assertEquals(16, actualValue.size());
        assertTrue(actualValue.containsKey(FederalState.SH));
        assertTrue(actualValue.containsKey(FederalState.BW));

        final var shValue = actualValue.get(FederalState.SH);
        assertTrue(shValue.has("button"));
        assertTrue(shValue.get("button").isTextual());
        assertEquals("Schleswig-Holstein-ButtonValue", shValue.get("button").asText());

        final var bwValue = actualValue.get(FederalState.BW);
        assertTrue(bwValue.has("button"));
        assertTrue(bwValue.get("button").isTextual());
        assertEquals("DefaultButtonValue", bwValue.get("button").asText());
    }
}
