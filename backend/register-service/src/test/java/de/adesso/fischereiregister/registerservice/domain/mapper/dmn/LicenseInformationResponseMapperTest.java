package de.adesso.fischereiregister.registerservice.domain.mapper.dmn;

import de.adesso.fischereiregister.registerservice.drools.dmn.license.configuration.output.LicenseInformationOutput;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;
import org.openapitools.model.LicenseInformation;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

class LicenseInformationResponseMapperTest {

    private final LicenseInformationResponseMapper mapper = Mappers.getMapper(LicenseInformationResponseMapper.class);

    @Test
    @DisplayName("Tests the LicenseInformationResponseMapper to ensure the response will be valid")
    void testToResponse_SingleMapping() {
        // Arrange
        Map<String, Object> result = new HashMap<>();
        result.put(LicenseInformationOutput.GEBUEHR_IN_EURO, BigDecimal.valueOf(100));
        result.put(LicenseInformationOutput.SPAETESTES_BESTELLDATUM, LocalDate.of(2023, 12, 31));
        result.put(LicenseInformationOutput.GUELTIGKEITSTYP, "VOLLES_JAHR");
        result.put(LicenseInformationOutput.GUELTIGKEITSOPTIONEN, new ArrayList<BigDecimal>() {{
            add(BigDecimal.valueOf(1));
            add(BigDecimal.valueOf(2));
            add(BigDecimal.valueOf(3));
        }});
        result.put(LicenseInformationOutput.IST_VERLAENGERBAR, true);
        result.put(LicenseInformationOutput.IST_VERFUEGBAR, true);
        result.put(LicenseInformationOutput.IST_BEFRISTBAR, true);
        LicenseInformationOutput dmnResult = new LicenseInformationOutput(result);

        // Act
        LicenseInformation response = mapper.toResponse(dmnResult);

        // Assert
        assertNotNull(response);
        assertEquals("FULL_YEAR", response.getDurationType().getValue());
    }

    @Test
    @DisplayName("Tests the LicenseInformationResponseMapper to ensure the response will be a valid List.")
    void testToResponse_ListMapping() {
        // Arrange
        Map<String, Object> result = new HashMap<>();
        result.put(LicenseInformationOutput.GEBUEHR_IN_EURO, BigDecimal.valueOf(100));
        result.put(LicenseInformationOutput.SPAETESTES_BESTELLDATUM, LocalDate.of(2023, 12, 31));
        result.put(LicenseInformationOutput.GUELTIGKEITSTYP, "TAG");
        result.put(LicenseInformationOutput.GUELTIGKEITSOPTIONEN, new ArrayList<BigDecimal>() {{
            add(BigDecimal.valueOf(1));
            add(BigDecimal.valueOf(2));
            add(BigDecimal.valueOf(3));
        }});
        result.put(LicenseInformationOutput.IST_VERLAENGERBAR, true);
        result.put(LicenseInformationOutput.IST_VERFUEGBAR, true);
        result.put(LicenseInformationOutput.IST_BEFRISTBAR, true);
        LicenseInformationOutput result1 = new LicenseInformationOutput(result);


        Map<String, Object> result2Map = new HashMap<>();
        result2Map.put(LicenseInformationOutput.GEBUEHR_IN_EURO, BigDecimal.valueOf(100));
        result2Map.put(LicenseInformationOutput.SPAETESTES_BESTELLDATUM, LocalDate.of(2023, 12, 31));
        result2Map.put(LicenseInformationOutput.GUELTIGKEITSTYP, "LEBENSLANG");
        result2Map.put(LicenseInformationOutput.GUELTIGKEITSOPTIONEN, new ArrayList<BigDecimal>() {{
            add(BigDecimal.valueOf(1));
            add(BigDecimal.valueOf(2));
            add(BigDecimal.valueOf(3));
        }});
        result2Map.put(LicenseInformationOutput.IST_VERLAENGERBAR, true);
        result2Map.put(LicenseInformationOutput.IST_VERFUEGBAR, true);
        result2Map.put(LicenseInformationOutput.IST_BEFRISTBAR, true);

        LicenseInformationOutput result2 = new LicenseInformationOutput(result2Map);

        List<LicenseInformationOutput> dmnResults = List.of(result1, result2);

        // Act
        List<LicenseInformation> responses = mapper.toResponse(dmnResults);

        // Assert
        assertNotNull(responses);
        assertEquals(2, responses.size());
        assertEquals("DAY", responses.get(0).getDurationType().getValue());
        assertEquals("LIFE_LONG", responses.get(1).getDurationType().getValue());
    }
}
