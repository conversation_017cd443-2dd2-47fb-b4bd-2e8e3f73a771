package de.adesso.fischereiregister.registerservice.drools.dmn.adapter;

import de.adesso.fischereiregister.core.exceptions.RulesProcessingException;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.registerservice.drools.dmn.DmnDroolsService;
import de.adesso.fischereiregister.registerservice.drools.dmn.tax.configuration.TaxInformationInput;
import de.adesso.fischereiregister.registerservice.drools.dmn.tax.configuration.TaxInformationOutput;
import de.adesso.fischereiregister.registerservice.fishing_license_export.port.model.TaxInformationResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class TenantRulesValuesAdapterTest {

    @Mock
    private DmnDroolsService dmnDroolsService;

    @InjectMocks
    private TenantRulesValuesAdapter tenantRulesValuesAdapter;

    private Tax tax;


    @BeforeEach
    void setUp() {
        tax = new Tax();
        tax.setValidFrom(LocalDate.of(2022, 1, 1));
        tax.setValidTo(LocalDate.of(2025, 1, 1));
    }

    @Test
    @DisplayName("Tests the TenantRulesValuesAdapter to ensure no exceptions are thrown when retrieving tax information.")
    void testGetTaxInformation() throws RulesProcessingException {
        FederalState federalState = FederalState.SH;
        HashMap<String, Object> resultMap = new HashMap<>();
        resultMap.put(TaxInformationOutput.NETTOBETRAG_ABGABE, BigDecimal.valueOf(100));
        resultMap.put(TaxInformationOutput.VERWALTUNGSGEBUEHR, BigDecimal.valueOf(10));
        resultMap.put(TaxInformationOutput.GEBUEHR_BEHOERDENGANG, BigDecimal.valueOf(2));
        resultMap.put(TaxInformationOutput.JAHRE, BigDecimal.valueOf(2));
        TaxInformationOutput mockResult1 = new TaxInformationOutput(resultMap);

        // Use ArgumentMatchers to match the TaxInformationInput argument
        when(dmnDroolsService.evaluateTaxRules(
                org.mockito.ArgumentMatchers.any(TaxInformationInput.class),
                org.mockito.ArgumentMatchers.eq(federalState)))
                .thenReturn(List.of(mockResult1));

        TaxInformationResult result = tenantRulesValuesAdapter.retrieveTaxInformation(federalState);

        assertNotNull(result);
        assertTrue(result.administrativeFee().compareTo(BigDecimal.TEN) >= 0);
        assertTrue(result.officeFee().compareTo(BigDecimal.TWO) >= 0);
        assertTrue(result.duration() == 2);
    }

}