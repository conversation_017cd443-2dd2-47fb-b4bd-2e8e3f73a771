package de.adesso.fischereiregister.registerservice.common;

import java.lang.reflect.Field;


/**
 * Our mocking libraries do not like to inject strings. But sometimes we need to inject them into private fields anyway
 * this is in particular the case when we use config beans. This class allows us to do so
 */
public class StringInjectionUtil {
    public static  void injectPrivateField(Object targetObject, String fieldName, Object value)  {
        try {
            final Field field = targetObject.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            field.set(targetObject, value );
        } catch (NoSuchFieldException | IllegalAccessException e) {
            throw new RuntimeException("Failed to inject Value: " + value + " into field: "+fieldName, e);
        }
    }

}
