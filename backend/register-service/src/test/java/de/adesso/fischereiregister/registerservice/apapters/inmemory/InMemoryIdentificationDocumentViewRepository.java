package de.adesso.fischereiregister.registerservice.apapters.inmemory;

import de.adesso.fischereiregister.registerservice.identification_document_view.IdentificationDocumentView;
import de.adesso.fischereiregister.registerservice.identification_document_view.IdentificationDocumentViewRepository;
import org.apache.commons.lang3.NotImplementedException;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

public class InMemoryIdentificationDocumentViewRepository extends InMemoryCrudRepository<IdentificationDocumentView, String> implements IdentificationDocumentViewRepository {

    @Override
    protected String getID(IdentificationDocumentView entity) {
        return entity.getIdentificationDocumentId();
    }

    @Override
    public Optional<IdentificationDocumentView> findByLicenseNumber(String licenseId) {
        return Optional.of((save.entrySet().stream()
                .filter(entry -> entry.getValue().getLicenseNumber().equalsIgnoreCase(licenseId)).findFirst().get().getValue()));
    }

    @Override
    public List<IdentificationDocumentView> findAllByLicenseNumber(String licenseId) {
        return List.of((save.entrySet().stream()
                .filter(entry -> entry.getValue().getLicenseNumber().equalsIgnoreCase(licenseId)).findFirst().get().getValue()));
    }

    @Override
    public List<String> findAllNumbers() {
        return List.of();
    }

    @Override
    public List<String> findAllDocumentNumbers() {
        return List.of();
    }

    @Override
    public Optional<IdentificationDocumentView> findByIdentificationDocumentId(String identificationDocumentId) {
        return Optional.empty();
    }

    @Override
    public IdentificationDocumentView[] findByRegisterId(UUID registerEntryId) {
        return new IdentificationDocumentView[0];
    }

    @Override
    public Optional<IdentificationDocumentView> findById(String s) {
        return Optional.empty();
    }

    @Override
    public boolean existsById(String s) {
        throw new NotImplementedException("Not implemented yet.");
    }

    @Override
    public void deleteById(String s) {
        throw new NotImplementedException("Not implemented yet.");
    }
}
