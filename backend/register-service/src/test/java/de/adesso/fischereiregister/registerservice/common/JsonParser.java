package de.adesso.fischereiregister.registerservice.common;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;

public class JsonParser {

	private JsonParser() {
	}

	public static String asJsonString(final Object obj) throws JsonProcessingException {
		final ObjectMapper mapper = new ObjectMapper();
		mapper.registerModule(new JavaTimeModule()); // so that it can handle LocalDate
		return mapper.writeValueAsString(obj);
	}

	public static Object toObject(final String jsonStr, final Class<?> targetClass) throws JsonProcessingException {
		final ObjectMapper mapper = new ObjectMapper();
		return mapper.readValue(jsonStr, targetClass);
	}

}
