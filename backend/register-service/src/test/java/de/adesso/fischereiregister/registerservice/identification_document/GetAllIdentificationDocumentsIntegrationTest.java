package de.adesso.fischereiregister.registerservice.identification_document;

import de.adesso.fischereiregister.RegisterServiceApplication;
import de.adesso.fischereiregister.registerservice.common.security.mocking.WithMockSecurityContext;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.util.List;

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.MOCK, classes = RegisterServiceApplication.class)
@AutoConfigureMockMvc(addFilters = false)
@WithMockSecurityContext()
public class GetAllIdentificationDocumentsIntegrationTest {

    private static final String IDENTIFICATION_DOCUMENTS_PATH = "/register-entries/{registerEntryId}/identification-documents";

    @Autowired
    private MockMvc mvc;

    @Test
    @DisplayName("""
            GET /api/register-entries/{registerEntryId}/identification-documents.
            Verify that the endpoint returns a 200 OK status code and the correct documents.
            """)
    void getIdentificationDocumentsSuccess() throws Exception {
        // Given
        // this comes from the test-data.xml
        final String documentId1 = "bda83b17-1929-42fa-9379-f1dcba14fcd5";
        final String documentId2 = "401be53c-a1d4-4600-bdc0-fe0d9772539b";
        final String registerEntryId = "e4b6af13-1feb-4a4d-9c46-76298a0611cf";

        final URI uri = UriComponentsBuilder.fromPath(IDENTIFICATION_DOCUMENTS_PATH)
                .queryParam("documentId", List.of(documentId1, documentId2))
                .buildAndExpand(registerEntryId)
                .toUri();

        // When and Then
        mvc.perform(MockMvcRequestBuilders
                        .get(uri)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.length()").value(2))
                .andExpect(jsonPath("$[0].documentId").value(documentId1))
                .andExpect(jsonPath("$[0].issuedDate").value("2024-09-17"))
                .andExpect(jsonPath("$[1].documentId").value(documentId2))
                .andExpect(jsonPath("$[1].issuedDate").value("2024-09-17"));
    }


    @Test
    @DisplayName("""
            GET /api/register-entries/{registerEntryId}/identification-documents.
            Verify that the endpoint returns a 404 Not Found status code when at least one documentId doesn't match.
            """)
    void getIdentificationDocumentsNotFound() throws Exception {
        // Given
        // this comes from the test-data.xml
        final String documentId1 = "bda83b17-1929-42fa-9379-f1dcba14fcd5"; // valid documentId
        final String invalidDocumentId = "non-existent-id"; // invalid documentId
        final String registerEntryId = "e4b6af13-1feb-4a4d-9c46-76298a0611cf";

        final URI uri = UriComponentsBuilder.fromPath(IDENTIFICATION_DOCUMENTS_PATH)
                .queryParam("documentId", List.of(documentId1, invalidDocumentId))
                .buildAndExpand(registerEntryId)
                .toUri();

        // When and Then
        mvc.perform(MockMvcRequestBuilders
                        .get(uri)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isNotFound());
    }

}
