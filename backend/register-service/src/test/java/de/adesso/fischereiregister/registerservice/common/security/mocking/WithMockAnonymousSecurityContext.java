package de.adesso.fischereiregister.registerservice.common.security.mocking;

import org.junit.jupiter.api.extension.ExtendWith;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;


/**
 * Add to our Tests to mock the security context for AnonymousAuthentication
 *
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@ExtendWith(AnonymousSecurityContextExtension.class)
public @interface WithMockAnonymousSecurityContext {

}