package de.adesso.fischereiregister.registerservice.tenant;

import de.adesso.fischereiregister.RegisterServiceApplication;
import de.adesso.fischereiregister.registerservice.common.security.mocking.WithMockSecurityContext;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.MOCK, classes = RegisterServiceApplication.class)
@AutoConfigureMockMvc(addFilters = false)
@WithMockSecurityContext()
@DisplayName("Test for all Tenant Configuration from the DMN rules tables which can be requested by the client through a rest interface.")
public class NamingsControllerIntegrationTest {
    private static final String NAMINGS_ROUTE = "http://localhost:8080/namings";

    @Autowired
    private MockMvc mvc;

    @DisplayName("""
            	GET /api/namings
            	Verify that the get namings endpoint returns a 200 OK status code and also checks the response object is valid
            """)
    @Test
    void testGetNamings() throws Exception {
        ResultActions result = mvc
                .perform(MockMvcRequestBuilders.get(NAMINGS_ROUTE)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON));

        result.andExpect(status().isOk());

        result.andExpect(jsonPath("$.length()").value(16));
        result.andExpect(jsonPath("SH.licenses.vacation").value("Urlauberfischereischein"));
        result.andExpect(jsonPath("NW.licenses.vacation").value("Ausländerfischereischein"));
    }
}
