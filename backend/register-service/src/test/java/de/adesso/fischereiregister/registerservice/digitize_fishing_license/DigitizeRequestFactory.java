package de.adesso.fischereiregister.registerservice.digitize_fishing_license;

import org.openapitools.model.Address;
import org.openapitools.model.ConsentInfo;
import org.openapitools.model.DigitizeFishingLicenseRequest;
import org.openapitools.model.FederalStateAbbreviation;
import org.openapitools.model.Fee;
import org.openapitools.model.FishingLicense;
import org.openapitools.model.Jurisdiction;
import org.openapitools.model.LicenseType;
import org.openapitools.model.PaymentInfo;
import org.openapitools.model.PaymentType;
import org.openapitools.model.PersonWithAddress;
import org.openapitools.model.QualificationsProof;
import org.openapitools.model.Tax;
import org.openapitools.model.ValidityPeriod;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

public class DigitizeRequestFactory {

    private DigitizeRequestFactory() {
    }

    public static DigitizeFishingLicenseRequest validDigitizeDto() {
        final Address address = new Address();
        address.setStreet("street");
        address.setStreetNumber("12");
        address.setPostcode("12334");
        address.setCity("Kiel");

        final PersonWithAddress person = new PersonWithAddress();
        person.setTitle("Dr.");
        person.setFirstname("Marc");
        person.setLastname("lastname");
        person.setBirthname("birthname");
        person.setBirthdate("01.01.2001");
        person.setNationality("deutsch");
        person.setBirthplace("city");
        person.setAddress(address);

        final Tax tax = new Tax();
        final PaymentInfo taxPaymentInfo = new PaymentInfo();
        taxPaymentInfo.setType(PaymentType.CASH);
        taxPaymentInfo.setAmount(BigDecimal.valueOf(17.0)); // 17.0 for year for federal state SH like its set in the rule tables
        tax.setValidFrom(LocalDate.of(LocalDate.now().getYear(), 1, 1).toString());
        tax.setValidTo(LocalDate.of(LocalDate.now().getYear(), 12, 31).toString());
        tax.setFederalState(FederalStateAbbreviation.SH);
        tax.setPaymentInfo(taxPaymentInfo);

        final Tax payedTax = new Tax();
        final PaymentInfo payedTaxPaymentInfo = new PaymentInfo();
        payedTaxPaymentInfo.setType(PaymentType.CASH);
        payedTaxPaymentInfo.setAmount(BigDecimal.valueOf(0));
        payedTax.setValidFrom(LocalDate.of(LocalDate.now().getYear(), 1, 1).toString());
        payedTax.setValidTo(LocalDate.of(LocalDate.now().getYear(), 12, 31).toString());
        payedTax.setFederalState(FederalStateAbbreviation.SH);
        payedTax.setPaymentInfo(payedTaxPaymentInfo);

        final Fee feePaymentItem = new Fee();
        PaymentInfo feePaymentInfo = new PaymentInfo();
        feePaymentInfo.setType(PaymentType.CASH);
        feePaymentInfo.setAmount(BigDecimal.valueOf(32.0));
        feePaymentItem.setValidFrom(LocalDate.now().toString());
        feePaymentItem.setValidTo(LocalDate.now().plusYears(1).toString());
        feePaymentItem.setFederalState(FederalStateAbbreviation.SH);
        feePaymentItem.setPaymentInfo(feePaymentInfo);

        final ConsentInfo consent = new ConsentInfo();
        consent.setGdprAccepted(true);
        consent.setSelfDisclosureAccepted(true);
        consent.setSubmittedByThirdParty(false);


        final FishingLicense fishingLicense = new FishingLicense();
//        fishingLicense.setLegacyNumber("11112");
        fishingLicense.setType(LicenseType.REGULAR);
//        fishingLicense.setIssuedBy("Comune X");
        final ValidityPeriod validityPeriod = new ValidityPeriod();
        validityPeriod.setValidFrom(LocalDate.now().toString());
//        validityPeriod.setVali(LocalDate.now().plusYears(1));
        fishingLicense.setValidityPeriods(new ArrayList<>());
        fishingLicense.getValidityPeriods().add(validityPeriod);

        final Jurisdiction jurisdiction = new Jurisdiction();
        jurisdiction.setFederalState(FederalStateAbbreviation.SH);

        final QualificationsProof fishingCertificate = new QualificationsProof();
        fishingCertificate.setFishingCertificateId("333");
        fishingCertificate.setPassedOn(LocalDate.now().minusYears(1).toString());
        fishingCertificate.setFederalState(FederalStateAbbreviation.BE);
        fishingCertificate.setIssuedBy("Fischschule Fischbude GmbH");

        final DigitizeFishingLicenseRequest request = new DigitizeFishingLicenseRequest();
        request.setPerson(person);
//        .setFishingLicenses(List.of(fishingLicense));
//        .setJurisdiction(jurisdiction);
        request.setQualificationsProofs(List.of(fishingCertificate));
        request.setTaxes(List.of(tax));
        request.setPayedTaxes(List.of(payedTax));
        request.setFees(List.of(feePaymentItem));
        request.setConsentInfo(consent);


        return request;
    }


}
