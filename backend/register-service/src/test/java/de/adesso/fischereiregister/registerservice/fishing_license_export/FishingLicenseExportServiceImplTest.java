package de.adesso.fischereiregister.registerservice.fishing_license_export;

import de.adesso.fischereiregister.core.exceptions.RulesProcessingException;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.LimitedLicenseApproval;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.ValidityPeriod;
import de.adesso.fischereiregister.registerservice.common.TestDataUtil;
import de.adesso.fischereiregister.registerservice.fishing_license_export.model.RenderedContent;
import de.adesso.fischereiregister.registerservice.fishing_license_export.model.RenderedContentType;
import de.adesso.fischereiregister.registerservice.fishing_license_export.port.TenantRulesValuesPort;
import de.adesso.fischereiregister.registerservice.fishing_license_export.port.model.TaxInformationResult;
import de.adesso.fischereiregister.registerservice.identification_document_view.IdentificationDocumentView;
import de.adesso.fischereiregister.registerservice.identification_document_view.IdentificationDocumentViewRepository;
import de.adesso.fischereiregister.registerservice.register_entry_view.RegisterEntryView;
import de.adesso.fischereiregister.registerservice.register_entry_view.RegisterEntryViewRepository;
import de.adesso.fischereiregister.registerservice.security.HashingAdapter;
import de.adesso.fischereiregister.registerservice.tenant.TenantConfigurationService;
import de.adesso.fischereiregister.view.register_entry_search.RegisterEntrySearchViewRepository;
import jakarta.persistence.EntityNotFoundException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.openapitools.model.SearchItem;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class FishingLicenseExportServiceImplTest {

    @Mock
    private RegisterEntryViewRepository registerEntryViewRepository;

    @Mock
    private RegisterEntrySearchViewRepository registerEntrySearchViewRepository;

    @Mock
    private TenantRulesValuesPort tenantRulesValuesService;

    @Mock
    private HashingAdapter hashingAdapter;

    @Mock
    private IdentificationDocumentViewRepository identificationDocumentViewRepository;

    @Mock
    private TenantConfigurationService tenantConfigurationService;

    @InjectMocks
    private FishingLicenseExportServiceImpl service;

    @Test
    @DisplayName("FishingLicenseExportServiceImpl.exportFishingLicenseDocument should return data")
    void testExportDigitizedFishingLicensePDF() {
        //GIVEN
        final String expectedFullFilename = "digitaler-fischereischein-dr-max-mustermann-sh.pdf";

        final UUID registerId = UUID.fromString("12345678-1234-1234-1234-123456789012");
        final String documentId = "12345678-1234-1234-1234-123456789012";

        final RegisterEntryView registerEntryView = TestDataUtil.createRegisterEntryView();
        final IdentificationDocumentView identificationDocumentView = TestDataUtil.createIdentificationDocumentView();

        when(registerEntryViewRepository.findById(registerId)).thenReturn(Optional.of(registerEntryView));
        when(identificationDocumentViewRepository.findByIdentificationDocumentId(documentId)).thenReturn(Optional.of(identificationDocumentView));

        final RenderedContent renderedContent = service.exportFishingLicense(registerId, documentId);

        //THEN
        assertNotNull(renderedContent.content());
        assertEquals(expectedFullFilename, renderedContent.getFullFilename());
    }

    @Test
    @DisplayName("FishingLicenseExportServiceImpl.exportLimitedLicenseApproval should return data")
    void testExportLimitedLicenseApprovalPDF() throws RulesProcessingException {
        //GIVEN
        final String expectedFullFilename = "digitale-fischereischein-bewilligung-dr-max-mustermann-sh.pdf";

        final UUID registerId = UUID.fromString("12345678-1234-1234-1234-123456789012");
        final String documentId = "12345678-1234-1234-1234-123456789012";

        final RegisterEntryView registerEntryView = TestDataUtil.createRegisterEntryViewForLimitedLicense();
        final IdentificationDocumentView identificationDocumentView = TestDataUtil.createIdentificationDocumentView();

        when(registerEntryViewRepository.findById(registerId)).thenReturn(Optional.of(registerEntryView));
        when(identificationDocumentViewRepository.findByIdentificationDocumentId(documentId)).thenReturn(Optional.of(identificationDocumentView));
        when(tenantRulesValuesService.retrieveFeeAnalog(any())).thenReturn(BigDecimal.TEN);
        when(tenantRulesValuesService.retrieveTaxInformation(any())).thenReturn(new TaxInformationResult(
                BigDecimal.TEN, BigDecimal.ZERO, BigDecimal.ZERO, 1, BigDecimal.ZERO));

        when(tenantConfigurationService.getValue(any(), any())).thenReturn("some tenant value");

        //WHEN
        final RenderedContent renderedContent = service.exportLimitedLicenseApproval(registerId, documentId);

        //THEN
        assertNotNull(renderedContent.content());
        assertEquals(expectedFullFilename, renderedContent.getFullFilename());
    }

    @Test
    @DisplayName("FishingLicenseExportServiceImpl.exportLimitedLicenseApprovalPreview should return data")
    void testExportLimitedLicenseApprovalPreview() throws RulesProcessingException {
        //GIVEN
        final String expectedFullFilename = "digitale-fischereischein-bewilligung-vorschau-dr-max-mustermann-sh.pdf";

        Person person = TestDataUtil.createPersonWithAddress();

        ValidityPeriod validityPeriod = new ValidityPeriod();
        validityPeriod.setValidFrom(LocalDate.now());
        validityPeriod.setValidTo(LocalDate.now().plusYears(1));

        String federalState = "SH";
        LimitedLicenseApproval approval = TestDataUtil.createLimitedLicenseApproval();

        when(tenantRulesValuesService.retrieveFeeAnalog(any())).thenReturn(BigDecimal.TEN);
        when(tenantRulesValuesService.retrieveTaxInformation(any())).thenReturn(new TaxInformationResult(
                BigDecimal.TEN, BigDecimal.ZERO, BigDecimal.ZERO, 1, BigDecimal.ZERO));
        when(tenantConfigurationService.getValue(any(), any())).thenReturn("some tenant value");

        //WHEN
        final RenderedContent renderedContent = service.exportLimitedLicenseApprovalPreview(
                person, validityPeriod, federalState, approval);

        //THEN
        assertNotNull(renderedContent.content());
        assertEquals(expectedFullFilename, renderedContent.getFullFilename());
    }

    @Test
    @DisplayName("FishingLicenseExportServiceImpl.exportFishingLicenseDocument should return data with endless validity")
    void testExportDigitizedFishingLicenseWithEndlessValidityPDF() {
        //GIVEN
        final String expectedFullFilename = "digitaler-fischereischein-dr-max-mustermann-sh.pdf";

        final UUID registerId = UUID.fromString("12345678-1234-1234-1234-123456789012");
        final String documentId = "12345678-1234-1234-1234-123456789012";

        final RegisterEntryView registerEntryView = TestDataUtil.createRegisterEntryView();

        registerEntryView.getData().getFishingLicenses().forEach(fl -> fl.getValidityPeriods().add(
                TestDataUtil.createValidityPeriod(LocalDate.now().minusYears(1),
                        null)));

        final IdentificationDocumentView identificationDocumentView = TestDataUtil.createIdentificationDocumentView();

        when(registerEntryViewRepository.findById(registerId)).thenReturn(Optional.of(registerEntryView));
        when(identificationDocumentViewRepository.findByIdentificationDocumentId(documentId)).thenReturn(Optional.of(identificationDocumentView));

        //WHEN
        final RenderedContent renderedContent = service.exportFishingLicense(registerId, documentId);

        //THEN
        assertNotNull(renderedContent.content());
        assertEquals(expectedFullFilename, renderedContent.getFullFilename());
    }

    @Test
    @DisplayName("FishingLicenseExportServiceImpl.exportFishingTaxDocument should return data")
    void testExportDigitizedFishingTaxPDF() {
        //GIVEN
        final String expectedFullFilename = "digitale-fischereiabgabe-dr-max-mustermann-sh-von-2025-bis-2024.pdf";

        final UUID registerId = UUID.fromString("12345678-1234-1234-1234-123456789012");
        final String documentId = "12345678-1234-1234-1234-123456789013";

        final Tax tax = new Tax();
        tax.setFederalState("SH");
        tax.setValidTo(LocalDate.of(2024, 1, 1));
        tax.setValidFrom(LocalDate.of(2025, 1, 1));

        final RegisterEntryView registerEntryView = TestDataUtil.createRegisterEntryView();
        final IdentificationDocument identificationDocument = registerEntryView.getData().getIdentificationDocuments().getFirst();
        identificationDocument.setDocumentId(documentId);
        identificationDocument.setTax(tax);

        final IdentificationDocumentView identificationDocumentView = TestDataUtil.createIdentificationDocumentView();
        identificationDocumentView.setIdentificationDocumentId(documentId);

        when(registerEntryViewRepository.findById(registerId)).thenReturn(Optional.of(registerEntryView));
        when(identificationDocumentViewRepository.findByIdentificationDocumentId(documentId)).thenReturn(Optional.of(identificationDocumentView));

        //WHEN
        final RenderedContent renderedContent = service.exportFishingTaxDocument(registerId, documentId);

        //THEN
        assertNotNull(renderedContent.content());
        assertEquals(expectedFullFilename, renderedContent.getFullFilename());
    }

    @Test
    @DisplayName("FishingLicenseExportServiceImpl.exportFishingTaxDocument should throw exception when tax is missing")
    void testExportFailOnMissingTaxInDocument() {
        //GIVEN
        final UUID registerId = UUID.fromString("12345678-1234-1234-1234-123456789012");
        final String documentId = "12345678-1234-1234-1234-123456789013";

        final RegisterEntryView registerEntryView = TestDataUtil.createRegisterEntryView();
        final IdentificationDocumentView identificationDocumentView = TestDataUtil.createIdentificationDocumentView();

        when(registerEntryViewRepository.findById(registerId)).thenReturn(Optional.of(registerEntryView));
        when(identificationDocumentViewRepository.findByIdentificationDocumentId(documentId)).thenReturn(Optional.of(identificationDocumentView));

        //WHEN & THEN
        assertThrows(EntityNotFoundException.class, () -> service.exportFishingTaxDocument(registerId, documentId));
    }

    @Test
    @DisplayName("FishingLicenseExportServiceImpl.exportFishingCertificate should return data")
    void testExportDigitizedFishingCertificatePDF() throws RulesProcessingException {
        //GIVEN
        final String expectedFullFilename = "digitale-fischereinachweis-dr-max-mustermann-sh.pdf";

        final UUID registerId = UUID.fromString("12345678-1234-1234-1234-123456789012");
        final String fishingCertificateId = TestDataUtil.fishingCertificateId;

        final RegisterEntryView registerEntryView = TestDataUtil.createRegisterEntryViewWithCertificate();
        final SearchItem result = new SearchItem();
        result.setRegisterId(String.valueOf(registerId));
        BigDecimal feeOD = new BigDecimal(11);
        BigDecimal feeAnalog = new BigDecimal(33);

        when(registerEntrySearchViewRepository.findByIdentificationNumber(fishingCertificateId)).thenReturn(Optional.of(result));
        when(registerEntryViewRepository.findById(registerId)).thenReturn(Optional.of(registerEntryView));
        when(tenantRulesValuesService.retrieveFeeAnalog(any())).thenReturn(feeAnalog);
        when(tenantRulesValuesService.retrieveFeeDigital(any())).thenReturn(feeOD);

        //WHEN
        final RenderedContent renderedContent = service.exportFishingCertificate(fishingCertificateId);

        //THEN
        assertNotNull(renderedContent.content());
        assertEquals(expectedFullFilename, renderedContent.getFullFilename());
    }

    @Test
    @DisplayName("FishingLicenseExportServiceImpl.exportFishingLicense should return data")
    void testExportFishingLicense_Success() {
        // GIVEN
        final UUID registerEntryId = UUID.randomUUID();
        final String salt = "testSalt";
        final String expectedFullFilename = "digitaler-fischereischein-dr-max-mustermann-sh.pdf";
        final byte[] expectedContent = new byte[]{37, 2, 3};

        final Person person = TestDataUtil.createPerson();
        final IdentificationDocument licenseDocument = mock(IdentificationDocument.class);
        final FishingLicense fishingLicense = TestDataUtil.createFishingLicense();

        when(licenseDocument.getFishingLicense()).thenReturn(fishingLicense);
        when(licenseDocument.getDocumentId()).thenReturn("doc123");

        FishingLicenseExportServiceImpl spyService = spy(service);
        doReturn(new RenderedContent(expectedFullFilename, RenderedContentType.PDF, expectedContent))
                .when(spyService).generatePdfDocumentForFishingLicense(person, licenseDocument, "FL-12345", "doc123", "hash", fishingLicense);

        // WHEN
        RenderedContent result = spyService.exportFishingLicense(registerEntryId, salt, person, licenseDocument);

        // THEN
        assertNotNull(result);
        assertEquals(expectedFullFilename, result.getFullFilename());
    }

    @Test
    @DisplayName("FishingLicenseExportServiceImpl.exportFishingTaxDocument should return data")
    void testExportFishingTaxDocument_Success() {
        // GIVEN
        final UUID registerEntryId = UUID.randomUUID();
        final String salt = "testSalt";
        final String documentId = "doc123";
        final String expectedFullFilename = "digitale-fischereiabgabe-dr-max-mustermann-sh-von-2025-bis-2025.pdf";
        final byte[] expectedContent = new byte[]{1, 2, 3};

        final Person person = TestDataUtil.createPerson();
        final IdentificationDocument document = mock(IdentificationDocument.class);
        final Tax tax = TestDataUtil.createAnalogTax();

        when(document.getTax()).thenReturn(tax);
        when(document.getDocumentId()).thenReturn(documentId);

        FishingLicenseExportServiceImpl spyService = spy(service);
        doReturn(new RenderedContent(expectedFullFilename, RenderedContentType.PDF, expectedContent))
                .when(spyService).generatePdfDocumentForTax(person, tax, documentId, "hash");

        // WHEN
        RenderedContent result = spyService.exportFishingTaxDocument(registerEntryId, salt, person, document);

        // THEN
        assertNotNull(result);
        assertEquals(expectedFullFilename, result.getFullFilename());
    }

    @Test
    @DisplayName("FishingLicenseExportServiceImpl.exportFishingTaxDocument should throw exception when tax is missing")
    void testExportFishingTaxDocument_ThrowsExceptionWhenTaxIsNull() {
        // GIVEN
        final UUID registerEntryId = UUID.randomUUID();
        final String salt = "testSalt";
        final Person person = TestDataUtil.createPerson();
        final IdentificationDocument document = TestDataUtil.createIdentificationDocuments().get(0);

        // WHEN & THEN
        EntityNotFoundException exception = assertThrows(EntityNotFoundException.class, () ->
                service.exportFishingTaxDocument(registerEntryId, salt, person, document));

        assertEquals("document found but contained not tax information", exception.getMessage());
    }
}