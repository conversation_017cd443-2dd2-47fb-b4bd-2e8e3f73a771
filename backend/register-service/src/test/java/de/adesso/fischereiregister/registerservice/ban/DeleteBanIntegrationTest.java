package de.adesso.fischereiregister.registerservice.ban;

import de.adesso.fischereiregister.RegisterServiceApplication;
import de.adesso.fischereiregister.registerservice.common.security.mocking.WithMockSecurityContext;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.MOCK, classes = RegisterServiceApplication.class)
@AutoConfigureMockMvc(addFilters = false)
@WithMockSecurityContext()
class DeleteBanIntegrationTest {

    @Autowired
    private MockMvc mvc;

    @Test
    @DisplayName("""
            	DELETE /api/register-entries/{registerEntryId}/ban
            	Verify that the delete ban endpoint returns a 200 OK status code.
            """)
    void banControllerDelete() throws Exception {

        String registerId = "e4b6af13-1feb-4a4d-9c46-76298a0611cf";

        mvc.perform(MockMvcRequestBuilders
                        .delete(getRequestURLFor(registerId))
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    @DisplayName("""
            DELETE /api/register-entries/{registerEntryId}/ban
            Verify that the delete ban endpoint returns 400 Request, when no ban existed.
            """)
    void banControllerDeleteNonExisting() throws Exception {
        String registerEntryId = "e4b6af13-1feb-4a4d-9c46-76298a0611cf";

        mvc.perform(MockMvcRequestBuilders
                        .delete(getRequestURLFor(registerEntryId))
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());
    }

    private String getRequestURLFor(String registerEntryId) {
        return "http://localhost:8080/register-entries/" + registerEntryId + "/ban";
    }
}