package de.adesso.fischereiregister.registerservice.register_entry_view;

import de.adesso.fischereiregister.RegisterServiceApplication;
import de.adesso.fischereiregister.registerservice.common.security.mocking.WithMockSecurityContext;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import static org.hamcrest.Matchers.containsString;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.MOCK, classes = RegisterServiceApplication.class)
@AutoConfigureMockMvc(addFilters = false)
@WithMockSecurityContext()

class GetRegisterEntryIntegrationTest {

    @Autowired
    private MockMvc mvc;

    @Test
    @DisplayName("""
				POST /api/register-entries/{registerEntryId}
				Verify that the register-entries returns a registerEntry for the given registerEntryId and check if the data (firstname) matches.
			""")
    void getRegisterEntryTest() throws Exception {

        ResultActions result =  mvc.perform(MockMvcRequestBuilders.get("http://localhost:8080/register-entries/e4b6af13-1feb-4a4d-9c46-76298a0611cf")
                .accept(MediaType.APPLICATION_JSON));

        result.andExpect(status().isOk());
        result.andExpect(jsonPath("$.person.firstname", containsString("Max")));
        result.andExpect(jsonPath("$.person.birthdate", containsString("08.08.2024")));
    }

    @Test
    @DisplayName("""
				POST /api/register-entries/{registerEntryId}
				Verify that the register-entries returns a not found exception if no registerEntryId is found.
			""")
    void getRegisterEntryFailsTest() throws Exception {

        ResultActions result =  mvc.perform(MockMvcRequestBuilders.get("http://localhost:8080/register-entries/e123f13-1feb-4a4d-9c46-76298a0611cf")
                .accept(MediaType.APPLICATION_JSON));

        result.andExpect(status().isNotFound());
    }
}