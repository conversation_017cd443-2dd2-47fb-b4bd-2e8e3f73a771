package de.adesso.fischereiregister.registerservice.mail;

import de.adesso.fischereiregister.registerservice.fishing_license_export.model.RenderedContent;
import de.adesso.fischereiregister.registerservice.fishing_license_export.model.RenderedContentType;
import jakarta.mail.Session;
import jakarta.mail.internet.MimeMessage;
import jakarta.mail.internet.MimeMultipart;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessagePreparator;

import java.util.List;
import java.util.Properties;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
public class JavaMailServiceTest {

    @Mock
    private JavaMailSender javaMailSender;

    @InjectMocks
    private JavaMailService javaMailService;

    @Captor
    private ArgumentCaptor<MimeMessagePreparator> mimeMessagePreparatorCaptor;


    private String to;
    private String from;
    private String subject;
    private String text;
    private String filename;
    private byte[] attachment;
    private RenderedContent renderedContent;

    @BeforeEach
    public void setUp() {
        to = "<EMAIL>";
        from = "<EMAIL>";
        subject = "Test Subject";
        text = "Test Email Body";
        filename = "testfile";
        attachment = "Test Attachment Content".getBytes();
        renderedContent = new RenderedContent(filename, RenderedContentType.PDF, attachment);
    }

    @Test
    @DisplayName("Test send mail")
    public void testSendMail() {
        // Arrange
        doNothing().when(javaMailSender).send(any(MimeMessagePreparator.class));

        // Act
        javaMailService.sendMail(to, from, subject, text, List.of(renderedContent));

        // Assert
        verify(javaMailSender).send(any(MimeMessagePreparator.class));
    }

    @Test
    @DisplayName("Test for the attachment.")
    public void testSendWithAttachment() throws Exception {
        // Act
        javaMailService.sendMail(to, from, subject, text, List.of(renderedContent));

        // Assert
        verify(javaMailSender).send(mimeMessagePreparatorCaptor.capture());
        MimeMessagePreparator preparator = mimeMessagePreparatorCaptor.getValue();

        // Create a MimeMessage for validation
        Properties props = new Properties();
        Session session = Session.getDefaultInstance(props);
        MimeMessage mimeMessage = new MimeMessage(session);

        preparator.prepare(mimeMessage);

        // Validate the MimeMessage content
        assertEquals(from, mimeMessage.getFrom()[0].toString());
        assertEquals(to, mimeMessage.getAllRecipients()[0].toString());
        assertEquals(subject, mimeMessage.getSubject());
        assertEquals(((MimeMultipart) mimeMessage.getContent()).getBodyPart(1).getFileName(), renderedContent.getFullFilename());

    }
}
