package de.adesso.fischereiregister.registerservice.certificate_numbers_view;

import de.adesso.fischereiregister.core.model.QualificationsProof;
import de.adesso.fischereiregister.core.model.type.QualificationsProofType;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import de.adesso.fischereiregister.registerservice.apapters.inmemory.InMemoryCertificateNumbersViewRepository;
import de.adesso.fischereiregister.registerservice.common.TestDataUtil;
import de.adesso.fischereiregister.registerservice.utils.NumberGenerationUtils;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.AdditionalAnswers;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class CertificateNumbersViewServiceImplTest {

    @Mock
    private CertificateNumbersViewRepository repository;


    @InjectMocks
    private CertificateNumbersViewServiceImpl service;


    @Test
    @DisplayName("Tests that the create certificate numbers view does not run in an exception")
    public void testCreateExaminerView() {
        //GIVEN
        final QualificationsProof proof = new QualificationsProof();
        proof.setFishingCertificateId(TestDataUtil.fishingCertificateId);
        proof.setType(QualificationsProofType.CERTIFICATE);

        //WHEN
        service.createCertificateNumbersView(DomainTestData.registerId, proof.getFishingCertificateId(), QualificationsProofType.CERTIFICATE);

        //THEN
        final ArgumentCaptor<CertificateNumbersView> captor = ArgumentCaptor.forClass(CertificateNumbersView.class);
        verify(repository, times(1)).save(captor.capture());
        final CertificateNumbersView captorValue = captor.getValue();


        assertEquals(DomainTestData.registerId, captorValue.getRegisterEntryId());

    }

    @Test
    @DisplayName("CertificateNumbersViewService.createNewAvailableFishingCertificateNumber just provides a new number, when it doesn't match the existing ones")
    public void testCreateNewAvailableFishingCertificateNumber() {
        //GIVEN
        final String randomFishingCertificateNumber = "ZF00-0000-0000-0000";
        when(repository.findAllNumbers()).thenReturn(List.of("ZF01-0000-0000-0000", "ZF02-0000-0000-0000"));
        try (MockedStatic<NumberGenerationUtils> mocked = mockStatic(NumberGenerationUtils.class)) {
            mocked.when(NumberGenerationUtils::generateCertificateId).thenReturn(randomFishingCertificateNumber);

            //WHEN
            final String fishingCertificateNumber = service.createNewAvailableFishingCertificateNumber();

            //THEN
            assertEquals(randomFishingCertificateNumber, fishingCertificateNumber);
        }
    }

    @Test
    @DisplayName("CertificateNumbersViewService.createNewAvailableFishingCertificateNumber rejects two existing fishing license numbers and only uses new ones")
    public void testCreateNewAvailableFishingCertificateNumberDoesntMatchExistingOnes() {
        //GIVEN
        final String randomFishingCertificateNumber = "ZF00-0000-0000-0000";
        final List<String> existingFishingCertificateNumbers = List.of("ZF01-0000-0000-0000", "ZF02-0000-0000-0000");
        final List<String> sequenceForRandomGenerationMock = new ArrayList<>(existingFishingCertificateNumbers);
        sequenceForRandomGenerationMock.add(randomFishingCertificateNumber);

        when(repository.findAllNumbers()).thenReturn(existingFishingCertificateNumbers);


        try (MockedStatic<NumberGenerationUtils> mocked = mockStatic(NumberGenerationUtils.class)) {
            mocked.when(NumberGenerationUtils::generateCertificateId).thenAnswer(AdditionalAnswers.returnsElementsOf(sequenceForRandomGenerationMock));
            //WHEN
            final String fishingCertificateNumber = service.createNewAvailableFishingCertificateNumber();
            //THEN
            assertEquals(randomFishingCertificateNumber, fishingCertificateNumber);
            mocked.verify(NumberGenerationUtils::generateCertificateId, times(3));

        }
    }

    @Test
    @DisplayName("Test save with null values")
    public void testSaveWithNullValues() {
        // given
        UUID registerEntryId = DomainTestData.registerId;
        QualificationsProof proof = TestDataUtil.createQualificationsProof();

        InMemoryCertificateNumbersViewRepository repository = new InMemoryCertificateNumbersViewRepository();
        CertificateNumbersViewServiceImpl service = new CertificateNumbersViewServiceImpl(repository);


        proof.setFishingCertificateId(null);
        // when
        service.createCertificateNumbersView(registerEntryId, proof.getFishingCertificateId(), QualificationsProofType.CERTIFICATE);

        // then
        assertNull(repository.findByRegisterEntryId(registerEntryId));

    }

    @Test
    @DisplayName("Test save with empty values")
    public void testSaveWithEmptyValues() {
        // given
        UUID registerEntryId = DomainTestData.registerId;
        QualificationsProof proof = TestDataUtil.createQualificationsProof();

        InMemoryCertificateNumbersViewRepository repository = new InMemoryCertificateNumbersViewRepository();
        CertificateNumbersViewServiceImpl service = new CertificateNumbersViewServiceImpl(repository);


        proof.setFishingCertificateId("");
        // when
        service.createCertificateNumbersView(registerEntryId, proof.getFishingCertificateId(), QualificationsProofType.CERTIFICATE);

        // then
        assertNull(repository.findByRegisterEntryId(registerEntryId));

    }

    @Test
    @DisplayName("Test save with empty values")
    public void testSaveWithWrongType() {
        // given
        UUID registerEntryId = DomainTestData.registerId;
        QualificationsProof proof = TestDataUtil.createQualificationsProof();

        InMemoryCertificateNumbersViewRepository repository = new InMemoryCertificateNumbersViewRepository();
        CertificateNumbersViewServiceImpl service = new CertificateNumbersViewServiceImpl(repository);


        // when
        service.createCertificateNumbersView(registerEntryId, proof.getFishingCertificateId(), QualificationsProofType.OTHER);

        // then
        assertNull(repository.findByRegisterEntryId(registerEntryId));

    }


}
