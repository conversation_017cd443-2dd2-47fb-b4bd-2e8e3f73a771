package de.adesso.fischereiregister.registerservice.apapters.inmemory;

import de.adesso.fischereiregister.registerservice.certificate_numbers_view.CertificateNumbersView;
import de.adesso.fischereiregister.registerservice.certificate_numbers_view.CertificateNumbersViewRepository;

import java.util.List;
import java.util.Map;
import java.util.UUID;

public class InMemoryCertificateNumbersViewRepository extends InMemoryCrudRepository<CertificateNumbersView, String> implements CertificateNumbersViewRepository {

    @Override
    protected String getID(CertificateNumbersView entity) {
        return entity.getCertificateNumber();
    }

    @Override
    public List<String> findAllNumbers() {
        return List.of();
    }

    @Override
    public CertificateNumbersView findByRegisterEntryId(UUID registerEntryId) {
        return save.entrySet().stream()
                .filter(entry -> entry.getValue().getRegisterEntryId().toString().equalsIgnoreCase(registerEntryId.toString()))
                .map(Map.Entry::getValue)
                .findFirst()
                .orElse(null);
    }
}
