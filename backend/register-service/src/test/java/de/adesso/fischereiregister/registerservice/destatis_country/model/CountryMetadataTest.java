package de.adesso.fischereiregister.registerservice.destatis_country.model;

import de.adesso.fischereiregister.registerservice.destatis_country.data.CountryMetadata;
import de.adesso.fischereiregister.registerservice.destatis_country.data.CountryNameValue;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

class CountryMetadataTest {

    private CountryMetadata countryMetadata;

    @BeforeEach
    void setUp() {
        countryMetadata = new CountryMetadata();
    }

    @Test
    void testSetAndGetKennung() {
        String kennung = "DE12345";
        countryMetadata.setKennung(kennung);

        assertEquals(kennung, countryMetadata.getKennung());
    }

    @Test
    void testSetAndGetKennungInhalt() {
        String kennungInhalt = "Example Inhalt";
        countryMetadata.setKennungInhalt(kennungInhalt);

        assertEquals(kennungInhalt, countryMetadata.getKennungInhalt());
    }

    @Test
    void testSetAndGetNameKurz() {
        CountryNameValue nameValue = new CountryNameValue();
        List<CountryNameValue> nameKurz = List.of(nameValue);
        countryMetadata.setNameKurz(nameKurz);

        assertNotNull(countryMetadata.getNameKurz());
        assertEquals(1, countryMetadata.getNameKurz().size());
        assertEquals(nameValue, countryMetadata.getNameKurz().get(0));
    }

    @Test
    void testSetAndGetXoevHandbuch() {
        boolean xoevHandbuch = true;
        countryMetadata.setXoevHandbuch(xoevHandbuch);

        assertTrue(countryMetadata.isXoevHandbuch());
    }

    @Test
    void testSetAndGetGueltigAb() {
        String gueltigAb = "2024-01-01";
        countryMetadata.setGueltigAb(gueltigAb);

        assertEquals(gueltigAb, countryMetadata.getGueltigAb());
    }

    @Test
    void testSetAndGetBezugsorte() {
        List<String> bezugsorte = List.of("Berlin", "Munich", "Hamburg");
        countryMetadata.setBezugsorte(bezugsorte);

        assertNotNull(countryMetadata.getBezugsorte());
        assertEquals(3, countryMetadata.getBezugsorte().size());
        assertEquals("Berlin", countryMetadata.getBezugsorte().get(0));
    }

    @Test
    void testAllOtherFields() {
        CountryNameValue exampleValue = new CountryNameValue();

        List<CountryNameValue> exampleList = List.of(exampleValue);
        countryMetadata.setNameLang(exampleList);
        countryMetadata.setHerausgebernameLang(exampleList);
        countryMetadata.setHerausgebernameKurz(exampleList);
        countryMetadata.setBeschreibung(exampleList);
        countryMetadata.setVersionBeschreibung(exampleList);
        countryMetadata.setAenderungZurVorversion(exampleList);

        assertEquals(exampleList, countryMetadata.getNameLang());
        assertEquals(exampleList, countryMetadata.getHerausgebernameLang());
        assertEquals(exampleList, countryMetadata.getHerausgebernameKurz());
        assertEquals(exampleList, countryMetadata.getBeschreibung());
        assertEquals(exampleList, countryMetadata.getVersionBeschreibung());
        assertEquals(exampleList, countryMetadata.getAenderungZurVorversion());
    }
}

