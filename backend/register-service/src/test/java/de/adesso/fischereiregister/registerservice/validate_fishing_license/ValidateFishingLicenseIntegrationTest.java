package de.adesso.fischereiregister.registerservice.validate_fishing_license;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import de.adesso.fischereiregister.RegisterServiceApplication;
import de.adesso.fischereiregister.registerservice.common.security.mocking.WithMockSecurityContext;
import de.adesso.fischereiregister.registerservice.validation.ValidationResponseDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.openapitools.model.LicenseType;
import org.openapitools.model.LicenseValidationStatus;
import org.openapitools.model.ValidationResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.web.util.UriComponentsBuilder;

import java.time.LocalDate;
import java.util.UUID;

import static de.adesso.fischereiregister.utils.DateUtils.GERMAN_DATE_TIME_FORMATTER;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = RegisterServiceApplication.class)
@AutoConfigureMockMvc(addFilters = false)
@DisplayName("Validate Fishing License Integration")
@WithMockSecurityContext()
public class ValidateFishingLicenseIntegrationTest {

    private static final String BASE_URL = "http://localhost:8080";
    private static final String VALIDATE_FISHING_LICENSE_PATH = "/register-entries/fishing-licenses:validate";
    private static final String LICENSE_NUMBER_PARAM = "licenseNumber";
    private static final String IDENTIFICATION_DOCUMENT_ID_PARAM = "identificationDocumentId";
    private static final String HASH_PARAM = "hash";

    private static final String LICENSE_NOTE = "";
    private static final String TAX_NOTE = "";

    private static final String MISSING_HASH_ERROR = "Missing required field: hash";
    private static final String MISSING_IDENTIFICATION_DOCUMENT_ID_ERROR = "Missing required field: identificationDocumentId";

    @Autowired
    private MockMvc mvc;

    private ObjectMapper mapper;

    // request data
    private String licenseNumber;
    private UUID identificationDocumentId;
    private String hash;

    // response data
    private String title;
    private String firstname;
    private String lastname;
    private LocalDate birthdate;
    private LicenseType fishingLicenseType;

    @BeforeEach
    void setUp() {
        mapper = new ObjectMapper();
        mapper.registerModule(new JavaTimeModule()); // Register JavaTimeModule for LocalDate

        // mock request data
        identificationDocumentId = UUID.fromString("2d073b7d-a961-45e3-89e6-85cd19123743");
        licenseNumber = "SH12111122223331";
        hash = "0c48d0d28f"; // precalculated

        // mock response data
        fishingLicenseType = LicenseType.REGULAR;
        title = "Dr.";
        firstname = "Max";
        lastname = "Mustermann";
        birthdate = LocalDate.of(2000, 1, 1);
    }

    @Test
    @DisplayName("""
                GET /api/validate-fishing-license.
                Verify successful validation of a fishing license.
            """)
    void validateFishingLicenseSuccess() throws Exception {
        String url = UriComponentsBuilder.fromUriString(BASE_URL + VALIDATE_FISHING_LICENSE_PATH)
                .queryParam(IDENTIFICATION_DOCUMENT_ID_PARAM, identificationDocumentId)
                .queryParam(HASH_PARAM, hash)
                .queryParam(LICENSE_NUMBER_PARAM, licenseNumber)
                .toUriString();

        ResultActions result = mvc.perform(MockMvcRequestBuilders.get(url)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON));

        result.andExpect(status().isOk());

        var responseDto = mapper.readValue(result.andReturn().getResponse().getContentAsString(),
                ValidationResponse.class);

        assertEquals(licenseNumber, responseDto.getLicenseNumber());
        assertEquals(fishingLicenseType, responseDto.getLicenseType());
        assertEquals(LicenseValidationStatus.VALID, responseDto.getLicenseStatus());
        assertEquals(LICENSE_NOTE, responseDto.getLicenseNote());
        assertFalse(responseDto.getTaxValid()); // sollte false sein
        assertEquals(TAX_NOTE, responseDto.getTaxNote());
        assertEquals(title, responseDto.getTitle());
        assertEquals(firstname, responseDto.getGivenNames());
        assertEquals(lastname, responseDto.getSurname());
        assertEquals(birthdate.format(GERMAN_DATE_TIME_FORMATTER), responseDto.getBirthdate());
    }

    @Test
    @DisplayName("""
                GET /api/validate-fishing-license.
                Verify request validation fails when request parameters are missing.
            """)
    void validateFishingLicenseInvalidRequest() throws Exception {

        String url = UriComponentsBuilder.fromUriString(BASE_URL + VALIDATE_FISHING_LICENSE_PATH)
                .queryParam(IDENTIFICATION_DOCUMENT_ID_PARAM, "")
                .queryParam(HASH_PARAM, "")
                .queryParam(LICENSE_NUMBER_PARAM, "")
                .toUriString();

        ResultActions result = mvc.perform(MockMvcRequestBuilders.get(url)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON));

        result.andExpect(status().isBadRequest());

        var responseDto = mapper.readValue(result.andReturn().getResponse().getContentAsString(),
                ValidationResponseDto.class);

        assertNotNull(responseDto);
        assertFalse(responseDto.getErrorNotes().isEmpty());
        assertTrue(responseDto.getErrorNotes().contains(MISSING_IDENTIFICATION_DOCUMENT_ID_ERROR));
        assertTrue(responseDto.getErrorNotes().contains(MISSING_HASH_ERROR));
    }

    @Test
    @DisplayName("""
                GET /api/validate-fishing-license.
                Verify request validation fails when hash is wrong.
            """)
    void validateFishingLicenseWrongHash() throws Exception {

        String url = UriComponentsBuilder.fromUriString(BASE_URL + VALIDATE_FISHING_LICENSE_PATH)
                .queryParam(LICENSE_NUMBER_PARAM, licenseNumber)
                .queryParam(IDENTIFICATION_DOCUMENT_ID_PARAM, identificationDocumentId)
                .queryParam(HASH_PARAM, "WRONG_HASH")
                .toUriString();

        ResultActions result = mvc.perform(MockMvcRequestBuilders.get(url)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON));

        result.andExpect(status().isNotFound());
    }

    @Test
    @DisplayName("""
                GET /api/validate-fishing-license.
                Verify request validation fails when identification document is not found.
            """)
    void validateFishingLicenseNotFound() throws Exception {
        String url = UriComponentsBuilder.fromUriString(BASE_URL + VALIDATE_FISHING_LICENSE_PATH)
                .queryParam(LICENSE_NUMBER_PARAM, "")
                .queryParam(IDENTIFICATION_DOCUMENT_ID_PARAM, "WRONG_DOCUMENT_IDENTIFICATION_NUMBER")
                .queryParam(HASH_PARAM, hash)
                .toUriString();

        ResultActions result = mvc.perform(MockMvcRequestBuilders.get(url)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON));

        result.andExpect(status().isNotFound());
    }
}
