package de.adesso.fischereiregister.registerservice.statistics;

import de.adesso.fischereiregister.RegisterServiceApplication;
import de.adesso.fischereiregister.core.events.FishingTaxPayedEvent;
import de.adesso.fischereiregister.core.events.QualificationsProofCreatedEvent;
import de.adesso.fischereiregister.core.events.RegularLicenseCreatedEvent;
import de.adesso.fischereiregister.core.events.VacationLicenseCreatedEvent;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.Jurisdiction;
import de.adesso.fischereiregister.core.model.PaymentInfo;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.QualificationsProof;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.core.model.type.PaymentType;
import de.adesso.fischereiregister.core.model.type.QualificationsProofType;
import de.adesso.fischereiregister.core.model.type.SubmissionType;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import de.adesso.fischereiregister.registerservice.common.security.mocking.WithMockSecurityContext;
import de.adesso.fischereiregister.view.certifications_statistics.eventhandling.CertificationsStatisticsViewEventHandler;
import de.adesso.fischereiregister.view.licenses_statistics.eventhandling.LicensesStatisticsViewEventHandler;
import de.adesso.fischereiregister.view.taxes_statistics.eventhandling.TaxesStatisticsViewEventHandler;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.UUID;

import static org.hamcrest.Matchers.greaterThanOrEqualTo;
import static org.hamcrest.Matchers.hasSize;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.MOCK, classes = RegisterServiceApplication.class)
@AutoConfigureMockMvc(addFilters = false)
@WithMockSecurityContext
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class StatisticsMetadataReadIntegrationTest {

    // greaterThanOrEqualTo is used for the assertions as the data is still contaminated by the file test-data.xml, please correct when the file gets deleted

    public static final String PARAM_YEAR = "year";
    public static final String PARAM_FEDERAL_STATE = "federalState";
    private static final String TEST_FEDERAL_STATE_SH = "SH";
    private static final String TEST_FEDERAL_STATE_HH = "HH";
    private static final String TEST_OFFICE_SH = "Test Office SH";
    private static final String TEST_OFFICE_HH = "Test Office HH";
    private static final String TEST_CERTIFICATION_ISSUER_SH = "Certification Issuer SH";
    private static final String TEST_CERTIFICATION_ISSUER_HH = "Certification Issuer HH";
    private static final double TAX_AMOUNT = 100.0;
    private static final int CURRENT_YEAR = LocalDate.now().getYear();
    private static final int PREVIOUS_YEAR = CURRENT_YEAR - 1;

    @Autowired
    private CertificationsStatisticsViewEventHandler certificationsEventHandler;

    @Autowired
    private TaxesStatisticsViewEventHandler taxesEventHandler;

    @Autowired
    private LicensesStatisticsViewEventHandler licensesEventHandler;

    @Autowired
    private MockMvc mvc;

    @BeforeAll
    void setUp() {
        // Create events and call handlers directly
        Instant currentYearInstant = LocalDateTime.of(CURRENT_YEAR, 1, 1, 0, 0, 0).toInstant(ZoneOffset.UTC);
        Instant previousYearInstant = LocalDateTime.of(PREVIOUS_YEAR, 1, 1, 0, 0, 0).toInstant(ZoneOffset.UTC);

        // Create certification events
        QualificationsProofCreatedEvent certificationEventSH1 = createCertificationEvent(TEST_FEDERAL_STATE_SH, TEST_CERTIFICATION_ISSUER_SH, CURRENT_YEAR);
        QualificationsProofCreatedEvent certificationEventSH2 = createCertificationEvent(TEST_FEDERAL_STATE_SH, TEST_CERTIFICATION_ISSUER_SH, CURRENT_YEAR);
        QualificationsProofCreatedEvent certificationEventHH = createCertificationEvent(TEST_FEDERAL_STATE_HH, TEST_CERTIFICATION_ISSUER_HH, CURRENT_YEAR);
        QualificationsProofCreatedEvent certificationEventPreviousYear = createCertificationEvent(TEST_FEDERAL_STATE_SH, TEST_CERTIFICATION_ISSUER_SH, PREVIOUS_YEAR);

        // Create tax events
        FishingTaxPayedEvent taxPayedEventSH = createFishingTaxPayedEvent(SubmissionType.ONLINE, TEST_FEDERAL_STATE_SH, TEST_OFFICE_SH, TAX_AMOUNT);
        FishingTaxPayedEvent taxPayedEventHH = createFishingTaxPayedEvent(SubmissionType.ANALOG, TEST_FEDERAL_STATE_HH, TEST_OFFICE_HH, TAX_AMOUNT);
        FishingTaxPayedEvent taxPayedEventPreviousYear = createFishingTaxPayedEvent(SubmissionType.ANALOG, TEST_FEDERAL_STATE_SH, TEST_OFFICE_SH, TAX_AMOUNT);

        // Create license events
        RegularLicenseCreatedEvent regularLicenseEventSH = createRegularLicenseEvent(SubmissionType.ONLINE, TEST_FEDERAL_STATE_SH, TEST_OFFICE_SH);
        VacationLicenseCreatedEvent vacationLicenseEventHH = createVacationLicenseEvent(SubmissionType.ANALOG, TEST_FEDERAL_STATE_HH, TEST_OFFICE_HH);

        // Process events
        certificationsEventHandler.on(certificationEventSH1);
        certificationsEventHandler.on(certificationEventSH2);
        certificationsEventHandler.on(certificationEventHH);
        certificationsEventHandler.on(certificationEventPreviousYear);

        taxesEventHandler.on(taxPayedEventSH, currentYearInstant);
        taxesEventHandler.on(taxPayedEventHH, currentYearInstant);
        taxesEventHandler.on(taxPayedEventPreviousYear, previousYearInstant);

        licensesEventHandler.on(regularLicenseEventSH, currentYearInstant);
        licensesEventHandler.on(vacationLicenseEventHH, currentYearInstant);
    }

    private QualificationsProofCreatedEvent createCertificationEvent(String federalState, String issuer, int year) {
        // Create a qualification proof with specific year
        QualificationsProof qualificationsProof = new QualificationsProof();
        qualificationsProof.setFederalState(federalState);
        qualificationsProof.setIssuedBy(issuer);
        qualificationsProof.setPassedOn(LocalDate.of(year, 1, 15)); // Set the passed date in the specified year
        qualificationsProof.setType(QualificationsProofType.CERTIFICATE);
        qualificationsProof.setFishingCertificateId("CERT-" + UUID.randomUUID());

        // Create QualificationsProofCreatedEvent with the created proof
        return new QualificationsProofCreatedEvent(
                UUID.randomUUID(), // registerEntryId
                qualificationsProof,
                new Person() // person
        );
    }

    private FishingTaxPayedEvent createFishingTaxPayedEvent(SubmissionType submissionType, String federalState, String office, double amount) {
        // Create a tax
        Tax tax = createTax(federalState, amount);

        // Create FishingTaxPayedEvent
        return new FishingTaxPayedEvent(
                UUID.randomUUID(),
                null, // consentInfo
                null, // person
                List.of(tax),
                "salt",
                List.of(), // identificationDocuments
                office, // issuedByOffice
                null, // inboxReference
                null,
                null,
                submissionType
        );
    }

    private RegularLicenseCreatedEvent createRegularLicenseEvent(SubmissionType submissionType, String federalState, String office) {
        // Create a fishing license
        FishingLicense license = DomainTestData.createLicense();

        // Create a person
        Person person = DomainTestData.createPerson();

        // Create jurisdiction
        Jurisdiction jurisdiction = new Jurisdiction();
        jurisdiction.setFederalState(federalState);

        // Create RegularLicenseCreatedEvent
        return new RegularLicenseCreatedEvent(
                UUID.randomUUID(),
                "salt",
                null, // consentInfo
                person, // person
                List.of(), // fees
                List.of(), // taxes
                license,
                List.of(), // identificationDocuments
                jurisdiction,
                office, // issuedByOffice
                "Test Address", // issuedByAddress
                null, // inboxReference
                null, // serviceAccountId
                null, // transactionId
                submissionType
        );
    }

    private VacationLicenseCreatedEvent createVacationLicenseEvent(SubmissionType submissionType, String federalState, String office) {
        // Create a fishing license
        FishingLicense license = DomainTestData.createLicense();
        license.setType(LicenseType.VACATION);
        license.setIssuingFederalState(FederalState.valueOf(federalState));

        // Create VacationLicenseCreatedEvent
        return new VacationLicenseCreatedEvent(
                UUID.randomUUID(),
                null, // person
                "salt",
                null, // consentInfo
                List.of(), // fees
                List.of(), // taxes
                List.of(), // identificationDocuments
                license,
                office, // issuedByOffice
                null, // inboxReference
                null, // serviceAccountId
                null, // transactionId
                submissionType
        );
    }

    private Tax createTax(String federalState, double amount) {
        Tax tax = new Tax();
        tax.setTaxId(UUID.randomUUID().toString());
        tax.setFederalState(federalState);
        tax.setValidFrom(LocalDate.of(LocalDate.now().getYear(), 1, 1));
        tax.setValidTo(LocalDate.of(LocalDate.now().getYear(), 12, 31));

        PaymentInfo paymentInfo = new PaymentInfo();
        paymentInfo.setAmount(amount);
        paymentInfo.setType(PaymentType.ONLINE);

        tax.setPaymentInfo(paymentInfo);

        return tax;
    }

    @Test
    @DisplayName("""
            GET /api/statistics/metadata/certification-issuers
            Verify that the certification issuers metadata endpoint can be reached and delivers the proper information.
            """)
    void callGetCertificationIssuersMetadataSuccessful() throws Exception {
        // given
        // Test data is set up in @BeforeAll

        // when & then
        mvc.perform(MockMvcRequestBuilders.get("/statistics/metadata/certification-issuers")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(jsonPath("$", hasSize(greaterThanOrEqualTo(2)))); // At least 2 issuers from test data
    }

    @Test
    @DisplayName("""
            GET /api/statistics/metadata/certification-issuers with year parameter
            Verify that the certification issuers metadata endpoint correctly filters by year.
            """)
    void callGetCertificationIssuersMetadataWithYearFilterSuccessful() throws Exception {
        // given
        // Test data is set up in @BeforeAll

        // when & then
        mvc.perform(MockMvcRequestBuilders.get("/statistics/metadata/certification-issuers")
                        .param(PARAM_YEAR, String.valueOf(CURRENT_YEAR))
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(jsonPath("$", hasSize(greaterThanOrEqualTo(2)))); // At least 2 issuers for current year
    }

    @Test
    @DisplayName("""
            GET /api/statistics/metadata/certification-issuers with federal state parameter
            Verify that the certification issuers metadata endpoint correctly filters by federal state.
            """)
    void callGetCertificationIssuersMetadataWithFederalStateFilterSuccessful() throws Exception {
        // given
        // Test data is set up in @BeforeAll

        // when & then
        mvc.perform(MockMvcRequestBuilders.get("/statistics/metadata/certification-issuers")
                        .param(PARAM_FEDERAL_STATE, TEST_FEDERAL_STATE_SH)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(jsonPath("$", hasSize(greaterThanOrEqualTo(1)))); // At least 1 issuer for SH
    }

    @Test
    @DisplayName("""
            GET /api/statistics/metadata/certification-issuers with year and federal state parameters
            Verify that the certification issuers metadata endpoint correctly filters by both year and federal state.
            """)
    void callGetCertificationIssuersMetadataWithBothFiltersSuccessful() throws Exception {
        // given
        // Test data is set up in @BeforeAll

        // when & then
        mvc.perform(MockMvcRequestBuilders.get("/statistics/metadata/certification-issuers")
                        .param(PARAM_YEAR, String.valueOf(CURRENT_YEAR))
                        .param(PARAM_FEDERAL_STATE, TEST_FEDERAL_STATE_SH)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(jsonPath("$", hasSize(greaterThanOrEqualTo(1)))); // At least 1 issuer for current year SH
    }

    @Test
    @DisplayName("""
            GET /api/statistics/metadata/offices
            Verify that the offices metadata endpoint can be reached and delivers the proper information.
            """)
    void callGetOfficesMetadataSuccessful() throws Exception {
        // given
        // Test data is set up in @BeforeAll

        // when & then
        mvc.perform(MockMvcRequestBuilders.get("/statistics/metadata/offices")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(jsonPath("$", hasSize(greaterThanOrEqualTo(2)))); // At least 2 offices from test data (taxes + licenses)
    }

    @Test
    @DisplayName("""
            GET /api/statistics/metadata/offices with year parameter
            Verify that the offices metadata endpoint correctly filters by year.
            """)
    void callGetOfficesMetadataWithYearFilterSuccessful() throws Exception {
        // given
        // Test data is set up in @BeforeAll

        // when & then
        mvc.perform(MockMvcRequestBuilders.get("/statistics/metadata/offices")
                        .param(PARAM_YEAR, String.valueOf(CURRENT_YEAR))
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(jsonPath("$", hasSize(greaterThanOrEqualTo(2)))); // At least 2 offices for current year
    }

    @Test
    @DisplayName("""
            GET /api/statistics/metadata/offices with federal state parameter
            Verify that the offices metadata endpoint correctly filters by federal state.
            """)
    void callGetOfficesMetadataWithFederalStateFilterSuccessful() throws Exception {
        // given
        // Test data is set up in @BeforeAll

        // when & then
        mvc.perform(MockMvcRequestBuilders.get("/statistics/metadata/offices")
                        .param(PARAM_FEDERAL_STATE, TEST_FEDERAL_STATE_SH)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(jsonPath("$", hasSize(greaterThanOrEqualTo(1)))); // At least 1 office for SH
    }

    @Test
    @DisplayName("""
            GET /api/statistics/metadata/offices with year and federal state parameters
            Verify that the offices metadata endpoint correctly filters by both year and federal state.
            """)
    void callGetOfficesMetadataWithBothFiltersSuccessful() throws Exception {
        // given
        // Test data is set up in @BeforeAll

        // when & then
        mvc.perform(MockMvcRequestBuilders.get("/statistics/metadata/offices")
                        .param(PARAM_YEAR, String.valueOf(CURRENT_YEAR))
                        .param(PARAM_FEDERAL_STATE, TEST_FEDERAL_STATE_SH)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(jsonPath("$", hasSize(greaterThanOrEqualTo(1)))); // At least 1 office for current year SH
    }

    @Test
    @DisplayName("""
            GET /api/statistics/metadata/certification-issuers without parameters
            Verify that the certification issuers metadata endpoint returns all available data when no parameters are provided.
            """)
    void callGetCertificationIssuersMetadataWithoutParametersSuccessful() throws Exception {
        // given
        // Test data is set up in @BeforeAll

        // when & then
        mvc.perform(MockMvcRequestBuilders.get("/statistics/metadata/certification-issuers")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(jsonPath("$", hasSize(greaterThanOrEqualTo(2)))); // All issuers from all years and federal states
    }

    @Test
    @DisplayName("""
            GET /api/statistics/metadata/offices without parameters
            Verify that the offices metadata endpoint returns all available data when no parameters are provided.
            """)
    void callGetOfficesMetadataWithoutParametersSuccessful() throws Exception {
        // given
        // Test data is set up in @BeforeAll

        // when & then
        mvc.perform(MockMvcRequestBuilders.get("/statistics/metadata/offices")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(jsonPath("$", hasSize(greaterThanOrEqualTo(2)))); // All offices from all years and federal states
    }

    @Test
    @DisplayName("""
            GET /api/statistics/metadata/years without parameters
            Verify that the years metadata endpoint returns all available data when no parameters are provided.
            """)
    void callGetYearsMetadataWithoutParametersSuccessful() throws Exception {
        // when & then
        mvc.perform(MockMvcRequestBuilders.get("/statistics/metadata/years")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(jsonPath("$", hasSize(greaterThanOrEqualTo(2)))) // At least two years given the mocked events
                .andExpect(jsonPath("$[0]").value(CURRENT_YEAR )) // First result year (current year)
                .andExpect(jsonPath("$[1]").value(PREVIOUS_YEAR)); // Second result year (previous year)

    }
}
