package de.adesso.fischereiregister.registerservice.common.security.mocking;

import de.adesso.fischereiregister.registerservice.security.UserDetailsService;
import org.junit.jupiter.api.extension.BeforeEachCallback;
import org.junit.jupiter.api.extension.ExtensionContext;
import org.mockito.Mockito;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;

import java.util.Map;
import java.util.Optional;

import static org.mockito.Mockito.when;

public class SecurityContextExtension implements BeforeEachCallback {

    @Override
    public void beforeEach(ExtensionContext context) {
        // this part is with values which can be anotated as this in our integration tests eg: @WithMockSecurityContext(federalState="BR")
        WithMockSecurityContext annotation = context.getRequiredTestClass().getAnnotation(WithMockSecurityContext.class);
        String federalState = annotation.federalState();
        String userId = annotation.userId();
        // ends here

        MockedServices result = getMockedServices();
        when(result.securityContextMock().getAuthentication()).thenReturn(result.authenticationMock());
        when(result.authenticationMock().getPrincipal()).thenReturn(result.jwtMock());

        addValuesOfKeyCloakToBeMocked(federalState, userId, result, SecurityContextConstants.EXAMINATION_CLAIMS);

        SecurityContextHolder.setContext(result.securityContextMock());
    }

    private void addValuesOfKeyCloakToBeMocked(String federalState, String userId, MockedServices result, Map<String, Object> examinationClaims) {
        addMockOfStringClaimFromKeyCloakClaims(result.jwtMock(), SecurityContextConstants.FEDERAL_STATE_CLAIM_IDENTIFIER, federalState);
        addMockOfStringClaimFromKeyCloakClaims(result.jwtMock(), SecurityContextConstants.USER_ID_CLAIM_IDENTIFIER, userId);
        addMockOfMapClaimFromKeyCloakClaims(result.jwtMock().getClaimAsMap(Mockito.eq(SecurityContextConstants.EXAMINATION_CLAIM_IDENTIFIER)), examinationClaims);
        addMockOfMapClaimFromKeyCloakClaims(result.jwtMock().getClaimAsMap(Mockito.eq(SecurityContextConstants.OFFICE_ADDRESS_CLAIM_IDENTIFIER)), SecurityContextConstants.OFFICE_ADDRESS_CLAIM_VALUES);
        when(result.userDetailsService().getOffice()).thenReturn(Optional.of(SecurityContextConstants.OFFICE_AS_STRING)); // das sind testdaten die kommen eigentlich aus: OFFICE_ADDRESS_CLAIM_VALUES
        when(result.userDetailsService().getParsedOfficeAddress()).thenReturn(Optional.of(SecurityContextConstants.MOCK_OFFICE_ADDRESS_AS_STRING)); // diese testdaten kommen eigentlich aus OFFICE_ADDRESS_CLAIM_VALUES im echten code
    }

    private static MockedServices getMockedServices() {
        SecurityContext securityContextMock = Mockito.mock(SecurityContext.class);
        Authentication  authenticationMock  = Mockito.mock(JwtAuthenticationToken.class);
        UserDetailsService userDetailsService = Mockito.mock(UserDetailsService.class);
        Jwt jwtMock = Mockito.mock(Jwt.class);
        MockedServices result = new MockedServices(securityContextMock, authenticationMock, userDetailsService, jwtMock);
        return result;
    }

    private record MockedServices(SecurityContext securityContextMock, Authentication authenticationMock, UserDetailsService userDetailsService, Jwt jwtMock) {
    }

    private static void addMockOfMapClaimFromKeyCloakClaims(Map<String, Object> jwtMock, Map<String, Object> examinationClaims) {
        when(jwtMock).thenReturn(examinationClaims);
    }

    private static void addMockOfStringClaimFromKeyCloakClaims(Jwt jwtMock, String claimIdentifier, String claim) {
        when(jwtMock.getClaimAsString(Mockito.eq(claimIdentifier))).thenReturn(claim);
    }
}