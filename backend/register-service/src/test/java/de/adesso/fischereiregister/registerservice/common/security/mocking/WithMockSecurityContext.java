package de.adesso.fischereiregister.registerservice.common.security.mocking;

import org.junit.jupiter.api.extension.ExtendWith;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;


/**
 * Add to our Integration Tests to mock the security context
 * which we do not want to use a running service and also it is to much overhead.
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@ExtendWith(SecurityContextExtension.class)
public @interface WithMockSecurityContext {

    //*use as follows:
    // @WithMockSecurityContext(federalState="BR")
    String federalState() default SecurityContextConstants.MOCK_FEDERAL_STATE;
    String userId()       default SecurityContextConstants.MOCK_USER_ID;
}