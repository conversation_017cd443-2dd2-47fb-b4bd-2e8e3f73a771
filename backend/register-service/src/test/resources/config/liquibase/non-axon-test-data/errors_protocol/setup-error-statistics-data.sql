/* the last two inserts represent an edge case where the sum has to be calculated correctly if the federal_state is not part of the select */
INSERT INTO errors_protocol (error_type, federal_state, year, count, office) VALUES
('SYSTEM_ERROR', 'SH', 2026, 8, null),
('ONLINE_SERVICE_ERROR', 'SH', 2023, 5, null),
('SYSTEM_ERROR', 'NW', 2023, 3, null),
('CARD_ORDER_ERROR', 'BE', 2022, 7, 'Kiel'),
('ONLINE_SERVICE_ERROR', 'SH', 2022, 2, null),
('SYSTEM_ERROR', 'BE', 2021, 4, null),
('CARD_ORDER_ERROR', 'NW', 2021, 6, 'Kiel2'),
('SYSTEM_ERROR', 'BE', 2025, 1, null),
('SYSTEM_ERROR', 'SH', 2025, 1, null);