package de.adesso.fischereiregister.testutils.inmemory.jpa;

import org.assertj.core.util.Streams;
import org.springframework.data.repository.ListCrudRepository;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

public abstract class InMemoryCrudRepository<T, ID> implements ListCrudRepository<T, ID> {

    protected final Map<ID, T> save = new HashMap<>();

    protected abstract ID getID(T entity);

    @Override
    public <S extends T> S save(S entity) {
        save.put(getID(entity), entity);
        return entity;
    }

    @Override
    public Optional<T> findById(ID id) {
        return Optional.ofNullable(save.get(id));
    }

    @Override
    public boolean existsById(ID id) {
        return save.containsKey(id);
    }

    @Override
    public void deleteById(ID id) {
        save.remove(id);
    }

    @Override
    public void deleteAllById(Iterable<? extends ID> ids) {
        ids.forEach(this::deleteById);
    }

    @Override
    public void delete(T entity) {
        // Collect keys to be removed
        List<ID> keysToRemove = save.entrySet().stream()
                .filter(entry -> entry.getKey().equals(getID(entity)))
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());

        // Remove entries by collected keys
        keysToRemove.forEach(this::deleteById);
    }

    @Override
    public void deleteAll(Iterable<? extends T> entities) {
        entities.forEach(entry -> save.remove(getID(entry)));
    }

    @Override
    public List<T> findAllById(Iterable<ID> ids) {
        return Streams.stream(ids).map(save::get).toList();
    }

    @Override
    public <S extends T> List<S> saveAll(Iterable<S> entities) {
        return Streams.stream(entities).map(entity -> {
            save(entity);
            return entity;
        }).toList();
    }

    @Override
    public List<T> findAll() {
        return new ArrayList<>(save.values());
    }

    @Override
    public long count() {
        return save.size();
    }

    @Override
    public void deleteAll() {
        save.clear();
    }

    protected Map<ID, T> save() {
        return save;
    }
}
