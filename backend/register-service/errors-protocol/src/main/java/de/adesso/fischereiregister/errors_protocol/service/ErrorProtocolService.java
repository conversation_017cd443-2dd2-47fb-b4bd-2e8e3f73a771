package de.adesso.fischereiregister.errors_protocol.service;

import de.adesso.fischereiregister.core.ports.ErrorsProtocolCardOrdersPort;
import de.adesso.fischereiregister.core.ports.ErrorsProtocolOnlineServicesPort;
import de.adesso.fischereiregister.core.ports.ErrorsProtocolStatisticsPort;
import de.adesso.fischereiregister.core.ports.ErrorsProtocolSystemPort;

interface ErrorProtocolService extends
        ErrorsProtocolSystemPort,
        ErrorsProtocolOnlineServicesPort,
        ErrorsProtocolStatisticsPort,
        ErrorsProtocolCardOrdersPort {

}
