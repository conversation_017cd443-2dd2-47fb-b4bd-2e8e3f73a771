package de.adesso.fischereiregister.core.exceptions;

import java.io.Serial;

/**
 * Exception thrown when the tenant rules had an invalid format or could not be processed.
 */
public class RulesProcessingException extends Exception {

    @Serial
    private static final long serialVersionUID = 7732815121575832750L;

    public RulesProcessingException(String message) {
        super(message);
    }

    public RulesProcessingException() {
        super("Es gab ein Problem beim Laden oder Verarbeiten der mandantenspezifischen Regeln");
    }
}
