package de.adesso.fischereiregister.core.commands;

import de.adesso.fischereiregister.core.model.Fee;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.consent.ConsentInfo;
import de.adesso.fischereiregister.core.model.user.UserDetails;
import org.axonframework.modelling.command.TargetAggregateIdentifier;

import java.util.List;
import java.util.UUID;

public record OrderReplacementCardCommand(
        @TargetAggregateIdentifier UUID registerId,
        String fishingLicenseNumber,
        Person person,
        String salt,
        List<Fee> fees,
        List<Tax> taxes,
        ConsentInfo consentInfo,
        UserDetails userDetails
) {

    public OrderReplacementCardCommand {
        assert fees != null : "The list of fees should not be null";
        assert taxes != null : "The list of taxes should not be null";
    }
}
