package de.adesso.fischereiregister.core.events;

import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.consent.TaxConsentInfo;
import de.adesso.fischereiregister.core.model.type.SubmissionType;
import org.axonframework.modelling.command.TargetAggregateIdentifier;
import org.axonframework.serialization.Revision;

import java.util.List;
import java.util.UUID;

@Revision("3.0")
public record PersonCreatedEvent(
        @TargetAggregateIdentifier UUID registerId,
        Person person,
        List<Tax> taxes,
        List<Tax> payedTaxes,
        String salt,
        List<IdentificationDocument> identificationDocuments,
        TaxConsentInfo consentInfo,
        String issuedByOffice,
        String inboxReference, // if null no message will be send to the online service portal (OS)
        String serviceAccountId, // id of the service account in the online service portal (OS)
        String transactionId, // transaction id of the online service portal (OS)
        String federalState, // Only for OSCreatePersonCommand
        SubmissionType submissionType

) implements AxonEvent {

    public PersonCreatedEvent {
        assert taxes != null : "The list of taxes should not be null";
        assert payedTaxes != null : "The list of payedTaxes should not be null";
        assert identificationDocuments != null : "The list of identificationDocuments should not be null";
    }
}


