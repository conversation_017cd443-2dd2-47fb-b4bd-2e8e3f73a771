package de.adesso.fischereiregister.core.commands;

import de.adesso.fischereiregister.core.model.user.UserDetails;
import org.axonframework.modelling.command.TargetAggregateIdentifier;

import java.time.LocalDate;
import java.util.UUID;

public record BanTemporarilyCommand(
        @TargetAggregateIdentifier UUID registerId,
        UUID banId,
        String fileNumber,
        String reportedBy,
        LocalDate from,
        LocalDate to,
        UserDetails userDetails
) {
}
