package de.adesso.fischereiregister.core.commands;

import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.user.UserDetails;
import org.axonframework.modelling.command.TargetAggregateIdentifier;

import java.time.LocalDate;
import java.util.UUID;

public record CreateFishingCertificateCommand(
        @TargetAggregateIdentifier UUID registerEntryId,
        LocalDate passedOn,
        Person person,
        UserDetails userDetails) {
}
