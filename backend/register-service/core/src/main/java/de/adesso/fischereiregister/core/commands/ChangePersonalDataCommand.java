package de.adesso.fischereiregister.core.commands;

import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.consent.ConsentInfo;
import de.adesso.fischereiregister.core.model.user.UserDetails;
import org.axonframework.modelling.command.TargetAggregateIdentifier;

import java.util.List;
import java.util.UUID;

public record ChangePersonalDataCommand(
        @TargetAggregateIdentifier
        UUID registerId,
        Person person,
        List<Tax> taxes,
        ConsentInfo consentInfo,
        String salt,
        UserDetails userDetails
        ) {
}
