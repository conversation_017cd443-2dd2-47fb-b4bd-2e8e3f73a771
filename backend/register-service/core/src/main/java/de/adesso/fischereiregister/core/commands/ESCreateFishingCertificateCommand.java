package de.adesso.fischereiregister.core.commands;

import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.user.UserDetails;
import org.axonframework.modelling.command.TargetAggregateIdentifier;

import java.time.LocalDate;
import java.util.UUID;

/**
 * Command to create a fishing certificate from the ES (Examination Software)
 * Similar to CreateFishingCertificateCommand but with direct federal state
 * because it is not included in the UserDetails in this case.
 */
public record ESCreateFishingCertificateCommand(
        @TargetAggregateIdentifier UUID registerEntryId,
        LocalDate passedOn,
        Person person,
        String federalState,
        UserDetails userDetails) {
}
