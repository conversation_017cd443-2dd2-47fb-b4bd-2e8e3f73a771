package de.adesso.fischereiregister.core.validation.helper;

import de.adesso.fischereiregister.core.exceptions.LicenseNotExtendableException;
import de.adesso.fischereiregister.core.exceptions.LicenseNotFoundException;
import de.adesso.fischereiregister.core.exceptions.RulesProcessingException;
import de.adesso.fischereiregister.core.model.Ban;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.ValidityPeriod;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.core.model.type.PaymentType;
import de.adesso.fischereiregister.core.ports.TenantInformationPort;
import de.adesso.fischereiregister.core.ports.contracts.LicenseInformation;
import de.adesso.fischereiregister.core.validation.ValidationResult;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

@Component
@AllArgsConstructor
public class LicenseValidatorHelper {
    private static final String DATE_PATTERN = "dd.MM.yyyy";

    private final TenantInformationPort tenantInformationPort;

    /**
     * @param registerEntry     The register entry to fetch the license from
     * @param licenseNumber     The number of the license that should be extended
     * @param newValidityPeriod The new validity period of the license
     * @param userFederalState  The federal state of the User / request
     * @param paymentType       The payment type of the corresponding request to the register (A license extension has to be paid)
     * @param validationResult  The validation result to store any validation errors
     * @throws RulesProcessingException When the Tenant rules on license extendability could not be read
     */
    public void validateLicenseIsExtendable(
            RegisterEntry registerEntry,
            String licenseNumber,
            ValidityPeriod newValidityPeriod,
            FederalState userFederalState,
            PaymentType paymentType,
            ValidationResult validationResult) throws RulesProcessingException {
        final FishingLicense fishingLicense = registerEntry.getFishingLicenses().stream()
                .filter(fl -> fl.getNumber().equals(licenseNumber))
                .findAny()
                .orElseThrow(LicenseNotFoundException::new);

        if (!fishingLicense.getIssuingFederalState().equals(userFederalState)) {
            validationResult.addErrorNote("Users can only extend licenses within their own jurisdiction. User jurisdiction: " + userFederalState.toString());
        }

        validateTenantInfo(fishingLicense, paymentType);

        validateNewPeriod(fishingLicense, newValidityPeriod, validationResult);

        validateNewPeriodAgainstBan(newValidityPeriod, registerEntry.getBan(), validationResult);
    }

    /**
     * Checks if a license type is available in the given tenant.
     *
     * @param federalState The federal state of the tenant
     * @param licenseType  The type of the license to check
     * @return true if the license is available, false otherwise
     * @throws RulesProcessingException if an error occurs while retrieving the license information
     */
    public boolean isLicenseAvailable(FederalState federalState, LicenseType licenseType) throws RulesProcessingException {
        final LicenseInformation licenseInfo = tenantInformationPort.getLicenseInformation(
                federalState,
                licenseType,
                PaymentType.CASH);

        return licenseInfo.isAvailable();
    }

    private void validateTenantInfo(FishingLicense fishingLicense, PaymentType paymentType) throws RulesProcessingException {
        final LicenseInformation licenseInfo = tenantInformationPort.getLicenseInformation(
                fishingLicense.getIssuingFederalState(),
                fishingLicense.getType(),
                paymentType);

        if (!licenseInfo.isExtendable()) {
            throw new LicenseNotExtendableException(fishingLicense.getNumber());
        }
    }

    private void validateNewPeriod(FishingLicense fishingLicense, ValidityPeriod newValidityPeriod, ValidationResult validationResult) {
        if (fishingLicense.getValidityPeriods().size() > 1) {
            validationResult.addErrorNote("A Vacation/Foreigner License can only be extended once");
        }

        final ValidityPeriod currentValidityPeriod = fishingLicense.getValidityPeriods().getFirst();
        if (currentValidityPeriod.getValidTo().isAfter(newValidityPeriod.getValidFrom())) {
            validationResult.addErrorNote("Der neue Gültigkeitszeitraum darf den bisherigen nicht überschneiden.");
        }
        if (currentValidityPeriod.getValidFrom().getYear() != newValidityPeriod.getValidFrom().getYear()) {
            validationResult.addErrorNote("Der neue Gültigkeitszeitraum muss in dem Selben Jahr beginnen wir der bisherige.");
        }
    }

    private void validateNewPeriodAgainstBan(ValidityPeriod newValidityPeriod, Ban ban,
                                             ValidationResult validationResult) {
        if (ban == null) {
            return;
        }

        LocalDate validFrom = newValidityPeriod.getValidFrom();
        LocalDate validTo = newValidityPeriod.getValidTo();
        LocalDate banFrom = ban.getFrom();

        if (ban.getTo() == null) { // Permanent ban, only has a "from" date
            if (validFrom.isAfter(banFrom) || validTo.isAfter(banFrom)) {
                String startDate = banFrom.format(DateTimeFormatter.ofPattern(DATE_PATTERN));
                validationResult.addErrorNote(
                        "Der Gültigkeitszeitraum darf nicht im Zeitraum der Sperre liegen: " + startDate + " — lebenslang");
            }
        } else { // Ban limited by "from" and "to" dates
            LocalDate banTo = ban.getTo();

            if (doTimeFramesOverlap(validFrom, validTo, banFrom, banTo)) {
                String startDate = banFrom.format(DateTimeFormatter.ofPattern(DATE_PATTERN));
                String endDate = banTo.format(DateTimeFormatter.ofPattern(DATE_PATTERN));
                validationResult.addErrorNote(
                        "Der Gültigkeitszeitraum darf nicht im Zeitraum der Sperre liegen: " + startDate + " — " + endDate);
            }
        }
    }

    /**
     * Checks if two time frames overlap in any way, including partial overlaps and inclusions.
     *
     * @param start1 the start date of the first time frame (inclusive)
     * @param end1   the end date of the first time frame (inclusive)
     * @param start2 the start date of the second time frame (inclusive)
     * @param end2   the end date of the second time frame (inclusive)
     * @return {@code true} if the time frames overlap, {@code false} otherwise
     * @throws IllegalArgumentException if the start date is after the end date for either time frame
     */
    private boolean doTimeFramesOverlap(LocalDate start1, LocalDate end1, LocalDate start2, LocalDate end2) {
        if (start1.isAfter(end1) || start2.isAfter(end2)) {
            throw new IllegalArgumentException("Start date must be before or equal to end date for both time frames.");
        }
        // has to use negated checks, so overlaps on same day are detected.
        return !end1.isBefore(start2) && !end2.isBefore(start1);
    }
}
