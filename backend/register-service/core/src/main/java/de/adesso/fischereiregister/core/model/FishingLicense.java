package de.adesso.fischereiregister.core.model;

import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.LicenseType;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;


/**
 * Represents a fishing license (Fischereischein) issued to a Person.
 * Some licenses may have a specific validity time range,
 * indicating the period during which the license is valid.
 */
@Getter
@Setter
public class FishingLicense {

    /**
     * The license number.
     */
    private String number;

    /**
     * Old license number. used only for auditing purposes. can be null
     */
    private String legacyNumber;

    /**
     * The federal State that issued the fishing license.
     */
    private FederalState issuingFederalState;

    /**
     * The type of fishing license [Normal, Vacation or Special]
     */
    private LicenseType type;

    /**
     * ValidityPeriods: including federal State information
     * <p>
     * REGULAR license: only the validFrom is set. The validity period of this license type is not limited so validTo is null,
     * and its validity periods are not determined by fees or taxes.
     * Even though the license is valid the owner has to pay taxes (and or fees) to ensure that he is allowed to fish.
     * If he is allowed to fish is determined at the moment in ValidateFishingLicenseService.validateRegister
     * <p>
     * VACATION license: this type of fishing license is only valid in the year it was issued.
     * If the Person of the Register Entry wants to fish next year in the same federal state a new fishing license will
     * be issued with a new license number and new validity periods.
     * <p>
     * LIMITED license: here the validity period can be different depending on the federal state.
     * e.g. in NRW it will be valid either for 1 year or for 5 years.
     * in SH it will be valid indefinitely starting with the valid from date, and the validTo date will be null.
     * the owner of the fishing license is only allowed to fish in company of a person which has a valid fishing license
     * themselves (this person can be any other (grown up) with a fishing license).
     */
    private List<ValidityPeriod> validityPeriods = new ArrayList<>();

    /**
     * If the fishing license is of LicenseType.LIMITED, this field may be set.
     */
    private LimitedLicenseApproval limitedLicenseApproval;

}
