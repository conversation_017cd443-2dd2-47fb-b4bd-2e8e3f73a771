package de.adesso.fischereiregister.core.commands;

import de.adesso.fischereiregister.core.model.Fee;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.consent.ConsentInfo;
import de.adesso.fischereiregister.core.model.type.FederalState;
import org.axonframework.modelling.command.TargetAggregateIdentifier;

import java.util.UUID;

public record OSCreateLimitedLicenseApplicationCommand(
        @TargetAggregateIdentifier UUID registerEntryId,
        FederalState federalState,
        Person person,
        Fee fee,
        ConsentInfo consentInfo,
        String disabilityCertificateFileURL,
        String inboxReference,
        String serviceAccountId,
        String transactionId
) {
}
