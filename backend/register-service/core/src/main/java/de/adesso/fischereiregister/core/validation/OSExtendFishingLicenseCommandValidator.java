package de.adesso.fischereiregister.core.validation;

import de.adesso.fischereiregister.core.commands.OSExtendFishingLicenseCommand;
import de.adesso.fischereiregister.core.exceptions.AggregateValidationException;
import de.adesso.fischereiregister.core.exceptions.ClientInputValidationException;
import de.adesso.fischereiregister.core.exceptions.RulesProcessingException;
import de.adesso.fischereiregister.core.exceptions.SystemConfigValidationException;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.type.PaymentType;
import de.adesso.fischereiregister.core.ports.CountryService;
import de.adesso.fischereiregister.core.ports.TenantRulesValidationPort;
import de.adesso.fischereiregister.core.validation.helper.FederalStateValidatorHelper;
import de.adesso.fischereiregister.core.validation.helper.LicenseValidatorHelper;
import de.adesso.fischereiregister.core.validation.helper.OSValidationHelper;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import static de.adesso.fischereiregister.core.validation.utils.ValidationUtils.validateFieldRequired;

@Component
@Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class OSExtendFishingLicenseCommandValidator extends AbstractValidator implements CommandValidator<OSExtendFishingLicenseCommand> {

    private static final String FEE = "fee";

    private final LicenseValidatorHelper licenseValidatorHelper;

    public OSExtendFishingLicenseCommandValidator(CountryService countryService, TenantRulesValidationPort tenantRulesValidationPort, LicenseValidatorHelper tenantInformationPort) {
        super(countryService, tenantRulesValidationPort);

        this.licenseValidatorHelper = tenantInformationPort;
    }

    @Override
    public void validateOrThrow(OSExtendFishingLicenseCommand command, RegisterEntry registerEntry) throws AggregateValidationException, RulesProcessingException {
        if (registerEntry == null) {
            validateOrThrow(command);
            return;
        }

        validateFieldRequired(command.salt(), SALT, validationResult);

        if (validationResult.hasErrors()) {
            // If the salt value is invalid, it is due to a system misconfiguration, so we return an exception accordingly.
            throw new SystemConfigValidationException(validationResult);
        }

        validateLicenseIsExtendable(command, registerEntry);
        validateFields(command);

        if (validationResult.hasErrors()) {
            throw new ClientInputValidationException(validationResult);
        }
    }

    private void validateLicenseIsExtendable(OSExtendFishingLicenseCommand command, RegisterEntry registerEntry) throws RulesProcessingException {
        licenseValidatorHelper.validateLicenseIsExtendable(
                registerEntry,
                command.licenseNumber(),
                command.validityPeriod(),
                command.federalState(),
                PaymentType.ONLINE,
                validationResult);
    }

    private void validateFields(OSExtendFishingLicenseCommand command) {

        validateFieldRequired(command.fee(), FEE, validationResult);
        validateFieldRequired(command.licenseNumber(), LICENSE_NUMBER, validationResult);
        OSValidationHelper.validateOSRequiredFields(command.serviceAccountId(), command.transactionId(), command.inboxReference(), validationResult);

        validateConsentInfo(command.consentInfo());
        FederalStateValidatorHelper.validateFederalState(command.federalState().toString(), validationResult);

        validateValidityPeriod(command.validityPeriod());

        validateFieldRequired(command.salt(), SALT, validationResult);
    }
}
