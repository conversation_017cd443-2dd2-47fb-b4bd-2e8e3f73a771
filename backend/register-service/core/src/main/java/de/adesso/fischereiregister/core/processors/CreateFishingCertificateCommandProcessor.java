package de.adesso.fischereiregister.core.processors;

import de.adesso.fischereiregister.core.commands.CreateFishingCertificateCommand;
import de.adesso.fischereiregister.core.events.AxonEvent;
import de.adesso.fischereiregister.core.events.QualificationsProofCreatedEvent;
import de.adesso.fischereiregister.core.model.QualificationsProof;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.type.QualificationsProofType;
import de.adesso.fischereiregister.core.ports.FishingCertificateNumberService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AllArgsConstructor
public class CreateFishingCertificateCommandProcessor implements CommandProcessor<CreateFishingCertificateCommand> {

    private FishingCertificateNumberService fishingCertificateNumberService;

    @Override
    public List<AxonEvent> process(CreateFishingCertificateCommand command, RegisterEntry registerEntry) {
        final QualificationsProofCreatedEvent event = buildEvent(command);

        return List.of(event);
    }

    private QualificationsProofCreatedEvent buildEvent(CreateFishingCertificateCommand command) {
        QualificationsProof qualificationsProof = new QualificationsProof(
                command.userDetails().getUserId(),
                fishingCertificateNumberService.createNewAvailableFishingCertificateNumber(command.registerEntryId()),
                command.userDetails().getCertificationIssuer(),
                QualificationsProofType.CERTIFICATE,
                command.userDetails().getFederalState(),
                command.passedOn()
        );

        return new QualificationsProofCreatedEvent(
                command.registerEntryId(),
                qualificationsProof,
                command.person()
        );
    }

}
