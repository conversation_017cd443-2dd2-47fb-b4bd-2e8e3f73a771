package de.adesso.fischereiregister.core.validation;

import de.adesso.fischereiregister.core.commands.PayFishingTaxCommand;
import de.adesso.fischereiregister.core.exceptions.AggregateValidationException;
import de.adesso.fischereiregister.core.exceptions.ClientInputValidationException;
import de.adesso.fischereiregister.core.exceptions.RulesProcessingException;
import de.adesso.fischereiregister.core.exceptions.SystemConfigValidationException;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.core.ports.CountryService;
import de.adesso.fischereiregister.core.ports.TenantRulesValidationPort;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import static de.adesso.fischereiregister.core.validation.utils.ValidationUtils.validateFieldRequired;

@Component
@Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class PayFishingTaxCommandValidator extends AbstractValidator implements CommandValidator<PayFishingTaxCommand> {

    public PayFishingTaxCommandValidator(CountryService countryService, TenantRulesValidationPort tenantRulesValidationPort) {
        super(countryService, tenantRulesValidationPort);
    }

    @Override
    public void validateOrThrow(PayFishingTaxCommand command, RegisterEntry registerEntry) throws AggregateValidationException, RulesProcessingException {
        validateUserDetails(command.userDetails());

        if (command.userDetails() != null) {
            validateFieldRequired(command.userDetails().getOffice(), OFFICE, validationResult);
        }

        validateFieldRequired(command.salt(), SALT, validationResult);

        if (validationResult.hasErrors()) {
            // if the user details, or the salt values are invalid is because of a System misconfiguration, so we return an exception accordingly
            throw new SystemConfigValidationException(validationResult);
        }

        validateTaxes(command.taxes());
        validateConsentInfo(command.consentInfo());

        validateFieldRequired(command.taxes(), TAXES, validationResult);

        // The command does not contain person data (DEPRECATED) so we check if a person is allowed to pay taxes by checking the aggregate state
        if (command.userDetails() != null) {
            validateTenantRules(FederalState.valueOf(command.userDetails().getFederalState()), registerEntry.getPerson(), command.taxes(), null, LicenseType.REGULAR);
        }

        if (validationResult.hasErrors()) {
            throw new ClientInputValidationException(validationResult);
        }

    }
}
