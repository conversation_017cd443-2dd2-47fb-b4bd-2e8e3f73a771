package de.adesso.fischereiregister.core.validation;


import de.adesso.fischereiregister.core.commands.CreateVacationLicenseCommand;
import de.adesso.fischereiregister.core.exceptions.AggregateValidationException;
import de.adesso.fischereiregister.core.exceptions.ClientInputValidationException;
import de.adesso.fischereiregister.core.exceptions.RulesProcessingException;
import de.adesso.fischereiregister.core.exceptions.SystemConfigValidationException;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.core.ports.CountryService;
import de.adesso.fischereiregister.core.ports.TenantRulesValidationPort;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import static de.adesso.fischereiregister.core.validation.utils.ValidationUtils.validateFieldRequired;

@Component
@Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class CreateVacationLicenseCommandValidator extends AbstractValidator implements CommandValidator<CreateVacationLicenseCommand> {

    public CreateVacationLicenseCommandValidator(CountryService countryService, TenantRulesValidationPort tenantRulesValidationPort) {
        super(countryService, tenantRulesValidationPort);
    }

    @Override
    public void validateOrThrow(CreateVacationLicenseCommand command, RegisterEntry registerEntry) throws AggregateValidationException, RulesProcessingException {
        validateUserDetails(command.userDetails());

        if (command.userDetails() != null) {
            validateFieldRequired(command.userDetails().getOffice(), OFFICE, validationResult);
        }

        validateFieldRequired(command.salt(), SALT, validationResult);

        if (validationResult.hasErrors()) {
            // If the user details or the salt values are invalid, it is due to a system misconfiguration, so we return an exception accordingly.
            throw new SystemConfigValidationException(validationResult);
        }

        if (licenseAlreadyExistsForYearAndState(command, registerEntry)) {
            validationResult.addErrorNote("A Vacation License for the same year and federal state already exists");
        }

        validatePerson(command.person(), true);
        validateFees(command.fees());
        validateTaxes(command.taxes());
        validateConsentInfo(command.consentInfo());
        validateValidityPeriod(command.validityPeriod());

        validateFieldRequired(command.fees(), FEES, validationResult);

        if (command.userDetails() != null) {
            validateTenantRules(FederalState.valueOf(command.userDetails().getFederalState()), command.person(), command.taxes(), command.fees(), LicenseType.VACATION);
        }

        if (validationResult.hasErrors()) {
            throw new ClientInputValidationException(validationResult);
        }

    }

    private boolean licenseAlreadyExistsForYearAndState(
            CreateVacationLicenseCommand command,
            RegisterEntry registerEntry
    ) {
        return registerEntry.getFishingLicenses().stream()
                .anyMatch(license ->
                        license.getType().equals(LicenseType.VACATION) &&
                                license.getIssuingFederalState().equals(FederalState.valueOf(command.userDetails().getFederalState())) &&
                                license.getValidityPeriods().stream().anyMatch(validityPeriod -> validityPeriod.getValidFrom().getYear() == command.validityPeriod().getValidFrom().getYear())
                );
    }

    @Override
    public void validateOrThrow(CreateVacationLicenseCommand command) throws AggregateValidationException {
        throw new UnsupportedOperationException("Validation without RegisterEntry for the CreateVacationLicenseCommandValidator is not supported.");
    }
}
