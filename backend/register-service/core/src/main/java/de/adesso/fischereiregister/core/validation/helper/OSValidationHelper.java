package de.adesso.fischereiregister.core.validation.helper;

import de.adesso.fischereiregister.core.validation.ValidationResult;
import de.adesso.fischereiregister.core.validation.utils.ValidationUtils;

public class OSValidationHelper {
    private static final String SERVICE_ACCOUNT_ID = "serviceAccountId";
    private static final String INBOX_REFERENCE = "inboxReference";
    private static final String TRANSACTION_ID = "transactionId";

    private OSValidationHelper() {
        throw new IllegalStateException("Utility class");
    }

    public static void validateOSRequiredFields(String serviceAccountId, String osPaymentTransactionId, String inboxReference, ValidationResult result) {

        ValidationUtils.validateFieldRequired(serviceAccountId, SERVICE_ACCOUNT_ID, result);
        ValidationUtils.validateFieldRequired(osPaymentTransactionId, TRANSACTION_ID, result);
        ValidationUtils.validateFieldRequired(inboxReference, INBOX_REFERENCE, result);
    }
}
