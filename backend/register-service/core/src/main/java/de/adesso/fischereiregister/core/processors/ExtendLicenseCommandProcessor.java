package de.adesso.fischereiregister.core.processors;

import de.adesso.fischereiregister.core.commands.ExtendLicenseCommand;
import de.adesso.fischereiregister.core.events.AxonEvent;
import de.adesso.fischereiregister.core.events.LicenseExtendedEvent;
import de.adesso.fischereiregister.core.exceptions.LicenseNotFoundException;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.core.model.type.SubmissionType;
import de.adesso.fischereiregister.core.model.utils.IdentificationDocumentFactory;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;


@Component
@AllArgsConstructor
public class ExtendLicenseCommandProcessor implements CommandProcessor<ExtendLicenseCommand> {

    private final IdentificationDocumentFactory identificationDocumentFactory;

    @Override
    public List<AxonEvent> process(ExtendLicenseCommand command, RegisterEntry registerEntry) {

        final FishingLicense fishingLicense = registerEntry.getFishingLicenses().stream()
                .filter(fl -> fl.getNumber().equals(command.licenseNumber()) && fl.getType().equals(LicenseType.VACATION))
                .findAny()
                .orElseThrow(LicenseNotFoundException::new);

        final IdentificationDocument pdfDocumentForValidityPeriod = identificationDocumentFactory.createPDFDocumentForValidityPeriod(fishingLicense, command.validityPeriod(), registerEntry.getRegisterId());

        final List<IdentificationDocument> identificationDocuments = new ArrayList<>();
        command.taxes().forEach(t -> identificationDocuments.add(identificationDocumentFactory.createIdentificationDocumentForTax(t, command.registerId())));
        identificationDocuments.add(pdfDocumentForValidityPeriod);

        LicenseExtendedEvent event = new LicenseExtendedEvent(
                command.registerId(),
                command.person(),
                command.salt(),
                command.consentInfo(),
                command.fees(),
                command.taxes(),
                command.licenseNumber(),
                command.validityPeriod(),
                identificationDocuments,
                null,
                null,
                null,
                command.userDetails().getOffice(),
                SubmissionType.ANALOG
        );

        return List.of(event);
    }

}
