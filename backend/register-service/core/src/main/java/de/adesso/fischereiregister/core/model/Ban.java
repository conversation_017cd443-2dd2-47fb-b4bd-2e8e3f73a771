package de.adesso.fischereiregister.core.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.util.UUID;

/**
 * Represents a ban (Sperre) issued to a person from fishing.
 */
@Getter
@Setter
public class Ban {

    /**
     * Unique identifier used to differentiate different Bans.
     */
    private UUID banId;

    /**
     * The file number associated with the ban (Aktenzeichen).
     */
    private String fileNumber;

    /**
     * The name of the person or entity who reported the ban (Zuständigkeit).
     */
    private String reportedBy;

    /**
     * The date the ban was reported.
     */
    private LocalDate at;

    /**
     * The start date of the ban.
     */
    private LocalDate from;

    /**
     * The end date of the ban. If null, the ban is permanent.
     */
    private LocalDate to;

    @JsonIgnore
    public boolean isCurrent(){
        // from date should normally never be null
        return (LocalDate.now().isAfter(from) || LocalDate.now().isEqual(from))
                && (to == null || (LocalDate.now().isBefore(to) || LocalDate.now().isEqual(to)));
    }
}