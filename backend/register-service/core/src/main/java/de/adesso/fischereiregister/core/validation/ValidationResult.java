package de.adesso.fischereiregister.core.validation;

import lombok.Getter;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Getter
public class ValidationResult implements Serializable {

    private UUID validationResultId = UUID.randomUUID();

    private List<String> errorNotes = new ArrayList<>();

    public void addErrorNote(String error) {
        errorNotes.add(error);
    }

    public boolean hasErrors() {
        return !errorNotes.isEmpty();
    }
}
