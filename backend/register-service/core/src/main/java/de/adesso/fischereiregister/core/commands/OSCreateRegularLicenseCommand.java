package de.adesso.fischereiregister.core.commands;

import de.adesso.fischereiregister.core.model.Fee;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.consent.ConsentInfo;
import org.axonframework.modelling.command.TargetAggregateIdentifier;

import java.util.List;
import java.util.UUID;

public record OSCreateRegularLicenseCommand(@TargetAggregateIdentifier UUID registerId,
                                            String salt,
                                            String fishingCertificateCode,
                                            Person person,
                                            ConsentInfo consentInfo,
                                            List<Tax> taxes,
                                            List<Fee> fees,
                                            String serviceAccountId,
                                            String federalState,
                                            String transactionId,
                                            String inboxReference) {

    public OSCreateRegularLicenseCommand {
        assert fees != null : "The list of fees should not be null";
        assert taxes != null : "The list of taxes should not be null";

    }
}
