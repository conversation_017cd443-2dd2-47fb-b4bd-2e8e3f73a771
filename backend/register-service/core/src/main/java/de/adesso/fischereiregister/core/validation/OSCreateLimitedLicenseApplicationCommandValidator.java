package de.adesso.fischereiregister.core.validation;

import de.adesso.fischereiregister.core.commands.OSCreateLimitedLicenseApplicationCommand;
import de.adesso.fischereiregister.core.exceptions.AggregateValidationException;
import de.adesso.fischereiregister.core.exceptions.ClientInputValidationException;
import de.adesso.fischereiregister.core.exceptions.LicenseTypeNotSupportedException;
import de.adesso.fischereiregister.core.exceptions.RulesProcessingException;
import de.adesso.fischereiregister.core.model.LimitedLicenseApplication;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.core.ports.CountryService;
import de.adesso.fischereiregister.core.ports.TenantRulesValidationPort;
import de.adesso.fischereiregister.core.validation.helper.ConsentInfoValidatorHelper;
import de.adesso.fischereiregister.core.validation.helper.FederalStateValidatorHelper;
import de.adesso.fischereiregister.core.validation.helper.FeeValidatorHelper;
import de.adesso.fischereiregister.core.validation.helper.LicenseValidatorHelper;
import de.adesso.fischereiregister.core.validation.helper.OSValidationHelper;
import de.adesso.fischereiregister.core.validation.helper.PersonValidatorHelper;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import static de.adesso.fischereiregister.core.validation.utils.ValidationUtils.validateFieldRequired;

@Component
@Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class OSCreateLimitedLicenseApplicationCommandValidator extends AbstractValidator implements CommandValidator<OSCreateLimitedLicenseApplicationCommand> {

    private static final String DISABILITY_CERTIFICATE_FILE_URL = LimitedLicenseApplication.Fields.disabilityCertificateFileURL;

    private final LicenseValidatorHelper licenseValidatorHelper;

    public OSCreateLimitedLicenseApplicationCommandValidator(
            CountryService countryService,
            TenantRulesValidationPort tenantRulesValidationPort,
            LicenseValidatorHelper licenseValidatorHelper) {
        super(countryService, tenantRulesValidationPort);

        this.licenseValidatorHelper = licenseValidatorHelper;
    }

    @Override
    public void validateOrThrow(OSCreateLimitedLicenseApplicationCommand command, RegisterEntry registerEntry) throws AggregateValidationException, RulesProcessingException {
        FederalStateValidatorHelper.validateFederalState(command.federalState(), validationResult);
        if (command.federalState() != null && !licenseValidatorHelper.isLicenseAvailable(command.federalState(), LicenseType.LIMITED)) {
            throw new LicenseTypeNotSupportedException(LicenseType.LIMITED, command.federalState());
        }


        PersonValidatorHelper.validate(command.person(), countryService, validationResult, true);
        if (command.person() != null) {
            PersonValidatorHelper.validateAddress(command.person().getAddress(), validationResult);
        }

        validateFieldRequired(command.fee(), FEES, validationResult);
        if (command.fee() != null) {
            FeeValidatorHelper.validateFee(command.fee(), validationResult);
        }

        ConsentInfoValidatorHelper.validate(command.consentInfo(), validationResult);

        validateFieldRequired(command.disabilityCertificateFileURL(), DISABILITY_CERTIFICATE_FILE_URL, validationResult);

        OSValidationHelper.validateOSRequiredFields(command.serviceAccountId(), command.transactionId(), command.inboxReference(), validationResult);


        if (validationResult.hasErrors()) {
            throw new ClientInputValidationException(validationResult);
        }
    }
}

