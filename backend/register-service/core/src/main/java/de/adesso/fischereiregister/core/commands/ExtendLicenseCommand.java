package de.adesso.fischereiregister.core.commands;

import de.adesso.fischereiregister.core.model.Fee;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.ValidityPeriod;
import de.adesso.fischereiregister.core.model.consent.ConsentInfo;
import de.adesso.fischereiregister.core.model.user.UserDetails;
import org.axonframework.modelling.command.TargetAggregateIdentifier;

import java.util.List;
import java.util.UUID;

/*
* Only Vacation Licenses can be extended
* */
public record ExtendLicenseCommand(
        @TargetAggregateIdentifier UUID registerId,
        String licenseNumber,
        String salt,
        Person person,
        List<Fee> fees,
        List<Tax> taxes,
        ValidityPeriod validityPeriod,
        ConsentInfo consentInfo,
        UserDetails userDetails
) {
}
