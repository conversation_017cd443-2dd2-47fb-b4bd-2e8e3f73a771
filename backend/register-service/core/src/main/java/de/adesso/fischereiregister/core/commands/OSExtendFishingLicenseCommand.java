package de.adesso.fischereiregister.core.commands;

import de.adesso.fischereiregister.core.model.Fee;
import de.adesso.fischereiregister.core.model.ValidityPeriod;
import de.adesso.fischereiregister.core.model.consent.ConsentInfo;
import de.adesso.fischereiregister.core.model.type.FederalState;
import org.axonframework.modelling.command.TargetAggregateIdentifier;

import java.util.UUID;

public record OSExtendFishingLicenseCommand(@TargetAggregateIdentifier
                                            UUID registerId,
                                            String salt,
                                            String licenseNumber,
                                            ConsentInfo consentInfo,
                                            Fee fee,
                                            FederalState federalState,
                                            ValidityPeriod validityPeriod,
                                            String serviceAccountId,
                                            String transactionId,
                                            String inboxReference) {
}
