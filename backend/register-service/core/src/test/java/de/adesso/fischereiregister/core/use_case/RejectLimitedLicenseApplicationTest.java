package de.adesso.fischereiregister.core.use_case;

import de.adesso.fischereiregister.core.commands.RejectLimitedLicenseApplicationCommand;
import de.adesso.fischereiregister.core.events.JurisdictionMovedEvent;
import de.adesso.fischereiregister.core.events.LimitedLicenseApplicationCreatedEvent;
import de.adesso.fischereiregister.core.events.LimitedLicenseApplicationRejectedEvent;
import de.adesso.fischereiregister.core.model.Jurisdiction;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.type.LimitedLicenseApplicationStatus;
import de.adesso.fischereiregister.core.model.type.SubmissionType;
import de.adesso.fischereiregister.core.model.user.UserRole;
import de.adesso.fischereiregister.core.testutils.AxonFixture;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import org.axonframework.test.aggregate.AggregateTestFixture;
import org.axonframework.test.matchers.Matchers;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

public class RejectLimitedLicenseApplicationTest {

    @Test
    @DisplayName("RejectLimitedLicenseApplication sets application status correctly")
    @ExtendWith(AxonFixture.class)
    public void testRejectLimitedLicenseApplication(AggregateTestFixture<RegisterEntry> fixture) {
        // GIVEN
        final LimitedLicenseApplicationCreatedEvent createdEvent = new LimitedLicenseApplicationCreatedEvent(
                DomainTestData.registerId,
                DomainTestData.createLimitedLicenseApplication(),
                DomainTestData.createPersonWithAddress(),
                List.of(),
                DomainTestData.createConsentInfo(),
                "inboxReference",
                "serviceAccountId"
        );

        final JurisdictionMovedEvent jurisdictionMovedEvent = new JurisdictionMovedEvent(
                DomainTestData.registerId,
                new Jurisdiction(),
                DomainTestData.createJurisdiction(),
                DomainTestData.createJurisdictionConsentInfo(),
                null,
                List.of(),
                "salt",
                "office",
                List.of(),
                SubmissionType.ANALOG
        );

        final RejectLimitedLicenseApplicationCommand command = new RejectLimitedLicenseApplicationCommand(
                DomainTestData.registerId,
                DomainTestData.createUserDetails(UserRole.LIMITED_LICENSE_CREATOR)
        );

        // WHEN
        fixture.given(createdEvent, jurisdictionMovedEvent)
                .when(command)
                .expectSuccessfulHandlerExecution()
                .expectEventsMatching(Matchers.exactSequenceOf(
                        Matchers.messageWithPayload(Matchers.matches(payload ->
                                payload.getClass().isAssignableFrom(LimitedLicenseApplicationRejectedEvent.class)
                        ))
                ))
                .expectState(registerEntry -> {
                    // Assert that the application status is set to REJECTED
                    assertNotNull(registerEntry.getLimitedLicenseApplication());
                    assertEquals(LimitedLicenseApplicationStatus.REJECTED, registerEntry.getLimitedLicenseApplication().getStatus());
                });
    }
}
