package de.adesso.fischereiregister.core.model.utils;

import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.ValidityPeriod;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.core.ports.FishingLicenseNumberService;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class FishingLicenseFactoryTest {

    private static final String MOCK_LICENSE_NUMBER = "SH001111222233334444";

    private FishingLicenseNumberService mockFishingLicenseNumberService;
    private FishingLicenseFactory fishingLicenseFactory;

    @BeforeEach
    void setUp() {
        mockFishingLicenseNumberService = mock(FishingLicenseNumberService.class);
        when(mockFishingLicenseNumberService.createNewAvailableFishingLicenseNumber(any(UUID.class), any(FederalState.class)))
                .thenReturn(MOCK_LICENSE_NUMBER);

        fishingLicenseFactory = new FishingLicenseFactory(mockFishingLicenseNumberService);
    }

    @Test
    @DisplayName("Test createRegularFishingLicense is successful")
    public void testFishingLicenseIsCreatedSuccessfully() {
        // GIVEN
        UUID registerEntryId = UUID.randomUUID();
        FederalState issuingFederalState = FederalState.SH;

        // WHEN
        FishingLicense license = fishingLicenseFactory.createRegularFishingLicense(registerEntryId, issuingFederalState);

        // THEN
        verify(mockFishingLicenseNumberService, times(1)).createNewAvailableFishingLicenseNumber(registerEntryId, issuingFederalState);

        // Basic properties
        assertEquals(MOCK_LICENSE_NUMBER, license.getNumber());
        assertEquals(LicenseType.REGULAR, license.getType());
        assertEquals(issuingFederalState, license.getIssuingFederalState());

        // Validity Periods
        assertEquals(1, license.getValidityPeriods().size());
        assertEquals(LocalDate.now(), license.getValidityPeriods().getFirst().getValidFrom());
        assertNull(license.getValidityPeriods().getFirst().getValidTo());
    }

    @Test
    @DisplayName("Test createLimitedFishingLicense is successful")
    public void testLimitedFishingLicenseIsCreatedSuccessfully() {
        // GIVEN
        UUID registerEntryId = UUID.randomUUID();
        FederalState issuingFederalState = FederalState.SH;
        ValidityPeriod validityPeriod = new ValidityPeriod();
        validityPeriod.setValidFrom(LocalDate.now());
        // WHEN
        FishingLicense license = fishingLicenseFactory.createLimitedFishingLicense(registerEntryId, validityPeriod, issuingFederalState, DomainTestData.createLimitedLicenseApproval());

        // THEN
        verify(mockFishingLicenseNumberService, times(1)).createNewAvailableFishingLicenseNumber(registerEntryId, issuingFederalState);

        // Basic properties
        assertEquals(MOCK_LICENSE_NUMBER, license.getNumber());
        assertEquals(LicenseType.LIMITED, license.getType());
        assertEquals(issuingFederalState, license.getIssuingFederalState());

        // Validity Periods
        assertEquals(1, license.getValidityPeriods().size());
        assertEquals(LocalDate.now(), license.getValidityPeriods().getFirst().getValidFrom());
        assertNull(license.getValidityPeriods().getFirst().getValidTo());
        assertNotNull(license.getLimitedLicenseApproval());
    }
}
