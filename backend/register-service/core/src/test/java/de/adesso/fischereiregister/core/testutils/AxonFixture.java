package de.adesso.fischereiregister.core.testutils;

import de.adesso.fischereiregister.core.interceptables.InterceptableCountryService;
import de.adesso.fischereiregister.core.interceptables.InterceptableDocumentNumberService;
import de.adesso.fischereiregister.core.interceptables.InterceptableFishingCertificateNumberService;
import de.adesso.fischereiregister.core.interceptables.InterceptableFishingLicenseNumberService;
import de.adesso.fischereiregister.core.interceptables.InterceptableTenantInformationPort;
import de.adesso.fischereiregister.core.interceptables.InterceptableTenantRulesValidationPort;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.utils.FishingLicenseFactory;
import de.adesso.fischereiregister.core.model.utils.IdentificationDocumentFactory;
import de.adesso.fischereiregister.core.model.utils.IdentificationDocumentFactoryImpl;
import de.adesso.fischereiregister.core.ports.TenantInformationPort;
import de.adesso.fischereiregister.core.ports.TenantRulesValidationPort;
import de.adesso.fischereiregister.core.processors.BanPermanentlyCommandProcessor;
import de.adesso.fischereiregister.core.processors.BanTemporarilyCommandProcessor;
import de.adesso.fischereiregister.core.processors.ChangePersonalDataCommandProcessor;
import de.adesso.fischereiregister.core.processors.CreateFishingCertificateCommandProcessor;
import de.adesso.fischereiregister.core.processors.CreateLimitedLicenseCommandProcessor;
import de.adesso.fischereiregister.core.processors.CreatePersonCommandProcessor;
import de.adesso.fischereiregister.core.processors.CreateRegularLicenseCommandProcessor;
import de.adesso.fischereiregister.core.processors.CreateVacationLicenseCommandProcessor;
import de.adesso.fischereiregister.core.processors.DigitizeRegularLicenseCommandProcessor;
import de.adesso.fischereiregister.core.processors.ExtendLicenseCommandProcessor;
import de.adesso.fischereiregister.core.processors.MarkRegisterEntryForDeletionCommandProcessor;
import de.adesso.fischereiregister.core.processors.MoveJurisdictionCommandProcessor;
import de.adesso.fischereiregister.core.processors.OSCreateLimitedLicenseApplicationCommandProcessor;
import de.adesso.fischereiregister.core.processors.OSCreatePersonCommandProcessor;
import de.adesso.fischereiregister.core.processors.OSCreateRegularLicenseCommandProcessor;
import de.adesso.fischereiregister.core.processors.OSCreateVacationLicenseCommandProcessor;
import de.adesso.fischereiregister.core.processors.OSExtendFishingLicenseCommandProcessor;
import de.adesso.fischereiregister.core.processors.OSPayTaxCommandProcessor;
import de.adesso.fischereiregister.core.processors.OSReplacementCardOrderedCommandProcessor;
import de.adesso.fischereiregister.core.processors.OrderReplacementCardCommandProcessor;
import de.adesso.fischereiregister.core.processors.PayFishingTaxCommandProcessor;
import de.adesso.fischereiregister.core.processors.RejectLimitedLicenseApplicationCommandProcessor;
import de.adesso.fischereiregister.core.processors.UnbanCommandProcessor;
import de.adesso.fischereiregister.core.service.VacationFeeSeparationService;
import de.adesso.fischereiregister.core.validation.BanPermanentlyCommandValidator;
import de.adesso.fischereiregister.core.validation.BanTemporarilyCommandValidator;
import de.adesso.fischereiregister.core.validation.CreateFishingCertificateCommandValidator;
import de.adesso.fischereiregister.core.validation.CreateLimitedLicenseCommandValidator;
import de.adesso.fischereiregister.core.validation.CreatePersonCommandValidator;
import de.adesso.fischereiregister.core.validation.CreateRegularLicenseCommandValidator;
import de.adesso.fischereiregister.core.validation.CreateVacationLicenseCommandValidator;
import de.adesso.fischereiregister.core.validation.DigitizeRegularLicenseCommandValidator;
import de.adesso.fischereiregister.core.validation.ExtendLicenseCommandValidator;
import de.adesso.fischereiregister.core.validation.MarkRegisterEntryForDeletionCommandValidator;
import de.adesso.fischereiregister.core.validation.MoveJurisdictionCommandValidator;
import de.adesso.fischereiregister.core.validation.OSCreateLimitedLicenseApplicationCommandValidator;
import de.adesso.fischereiregister.core.validation.OSCreateRegularLicenseCommandValidator;
import de.adesso.fischereiregister.core.validation.OSCreateVacationLicenseCommandValidator;
import de.adesso.fischereiregister.core.validation.OSExtendFishingLicenseCommandValidator;
import de.adesso.fischereiregister.core.validation.OSReplaceCardCommandValidator;
import de.adesso.fischereiregister.core.validation.OSTaxCommandValidator;
import de.adesso.fischereiregister.core.validation.OrderReplacementCardCommandValidator;
import de.adesso.fischereiregister.core.validation.PayFishingTaxCommandValidator;
import de.adesso.fischereiregister.core.validation.RejectLimitedLicenseApplicationCommandValidator;
import de.adesso.fischereiregister.core.validation.helper.LicenseValidatorHelper;
import org.axonframework.test.aggregate.AggregateTestFixture;
import org.junit.jupiter.api.extension.BeforeEachCallback;
import org.junit.jupiter.api.extension.ExtensionContext;
import org.junit.jupiter.api.extension.ParameterContext;
import org.junit.jupiter.api.extension.ParameterResolutionException;
import org.junit.jupiter.api.extension.ParameterResolver;

import java.lang.reflect.Parameter;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.lang.reflect.WildcardType;
import java.util.Arrays;
import java.util.List;

public class AxonFixture implements BeforeEachCallback, ParameterResolver {

    private static final ExtensionContext.Namespace NAMESPACE = ExtensionContext.Namespace.create("axon_fixture");

    private static final Object KEY_FIXTURE = new Object();
    private static final Object KEY_COUNTRY = new Object();
    private static final Object KEY_TENANT_VALIDATION_PORT = new Object();
    private static final Object KEY_FISHING_LICENSE_NUMBER = new Object();

    //__________________________________________________________________________________________________________________
    //::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::

    @Override
    public void beforeEach(ExtensionContext context) {
        final AggregateTestFixture<RegisterEntry> fixture = new AggregateTestFixture<>(RegisterEntry.class);

        InterceptableCountryService countryService = new InterceptableCountryService();
        fixture.registerInjectableResource(countryService);

        InterceptableFishingLicenseNumberService licenseNumberService = new InterceptableFishingLicenseNumberService();
        fixture.registerInjectableResource(licenseNumberService);

        IdentificationDocumentFactory identificationDocumentFactory = new IdentificationDocumentFactoryImpl(new InterceptableDocumentNumberService());
        fixture.registerInjectableResource(identificationDocumentFactory);

        InterceptableFishingCertificateNumberService certificateNumberService = new InterceptableFishingCertificateNumberService();
        fixture.registerInjectableResource(certificateNumberService);

        FishingLicenseFactory fishingLicenseFactory = new FishingLicenseFactory(licenseNumberService);
        fixture.registerInjectableResource(fishingLicenseFactory);

        TenantInformationPort tenantInformationService = new InterceptableTenantInformationPort();
        fixture.registerInjectableResource(tenantInformationService);

        TenantRulesValidationPort tenantRulesValidationPort = new InterceptableTenantRulesValidationPort();
        fixture.registerInjectableResource(tenantRulesValidationPort);

        VacationFeeSeparationService vacationFeeSeparationService = new VacationFeeSeparationService(tenantInformationService);
        fixture.registerInjectableResource(vacationFeeSeparationService);

        final LicenseValidatorHelper licenseValidatorHelper = new LicenseValidatorHelper(tenantInformationService);
        fixture.registerInjectableResource(licenseValidatorHelper);

        // Register all processors
        fixture.registerInjectableResources(
                new CreateRegularLicenseCommandProcessor(identificationDocumentFactory, fishingLicenseFactory),
                new OrderReplacementCardCommandProcessor(identificationDocumentFactory),
                new OSCreateRegularLicenseCommandProcessor(identificationDocumentFactory, fishingLicenseFactory),
                new OSPayTaxCommandProcessor(identificationDocumentFactory),
                new OSCreatePersonCommandProcessor(identificationDocumentFactory),
                new OSReplacementCardOrderedCommandProcessor(identificationDocumentFactory),
                new CreateFishingCertificateCommandProcessor(certificateNumberService),
                new MoveJurisdictionCommandProcessor(identificationDocumentFactory),
                new DigitizeRegularLicenseCommandProcessor(identificationDocumentFactory, fishingLicenseFactory),
                new ChangePersonalDataCommandProcessor(identificationDocumentFactory),
                new CreatePersonCommandProcessor(identificationDocumentFactory),
                new PayFishingTaxCommandProcessor(identificationDocumentFactory),
                new UnbanCommandProcessor(),
                new BanPermanentlyCommandProcessor(),
                new BanTemporarilyCommandProcessor(),
                new OSCreateVacationLicenseCommandProcessor(fishingLicenseFactory, identificationDocumentFactory, vacationFeeSeparationService),
                new CreateVacationLicenseCommandProcessor(fishingLicenseFactory, identificationDocumentFactory),
                new ExtendLicenseCommandProcessor(identificationDocumentFactory),
                new OSCreateVacationLicenseCommandProcessor(fishingLicenseFactory, identificationDocumentFactory, vacationFeeSeparationService),
                new OSExtendFishingLicenseCommandProcessor(vacationFeeSeparationService, identificationDocumentFactory),
                new CreateLimitedLicenseCommandProcessor(identificationDocumentFactory, fishingLicenseFactory),
                new OSCreateLimitedLicenseApplicationCommandProcessor(),
                new RejectLimitedLicenseApplicationCommandProcessor(),
                new MarkRegisterEntryForDeletionCommandProcessor());

        // Register all validators
        fixture.registerInjectableResources(
                new DigitizeRegularLicenseCommandValidator(countryService, tenantRulesValidationPort),
                new CreateRegularLicenseCommandValidator(countryService, tenantRulesValidationPort),
                new CreatePersonCommandValidator(countryService, tenantRulesValidationPort),
                new OrderReplacementCardCommandValidator(countryService, tenantRulesValidationPort),
                new OSCreateRegularLicenseCommandValidator(countryService, tenantRulesValidationPort),
                new OSTaxCommandValidator(countryService, tenantRulesValidationPort),
                new OSReplaceCardCommandValidator(countryService, tenantRulesValidationPort),
                new CreateFishingCertificateCommandValidator(countryService, tenantRulesValidationPort),
                new BanPermanentlyCommandValidator(countryService, tenantRulesValidationPort),
                new BanTemporarilyCommandValidator(countryService, tenantRulesValidationPort),
                new PayFishingTaxCommandValidator(countryService, tenantRulesValidationPort),
                new MoveJurisdictionCommandValidator(countryService, tenantRulesValidationPort),
                new OrderReplacementCardCommandValidator(countryService, tenantRulesValidationPort),
                new OSCreateRegularLicenseCommandValidator(countryService, tenantRulesValidationPort),
                new OSReplaceCardCommandValidator(countryService, tenantRulesValidationPort),
                new OSCreateVacationLicenseCommandValidator(countryService, tenantRulesValidationPort),
                new CreateVacationLicenseCommandValidator(countryService, tenantRulesValidationPort),
                new ExtendLicenseCommandValidator(countryService, tenantRulesValidationPort, licenseValidatorHelper),
                new OSExtendFishingLicenseCommandValidator(countryService, tenantRulesValidationPort, licenseValidatorHelper),
                new CreateLimitedLicenseCommandValidator(countryService, tenantRulesValidationPort, licenseValidatorHelper),
                new OSCreateLimitedLicenseApplicationCommandValidator(countryService, tenantRulesValidationPort, licenseValidatorHelper),
                new RejectLimitedLicenseApplicationCommandValidator(countryService, tenantRulesValidationPort, licenseValidatorHelper),
                new MarkRegisterEntryForDeletionCommandValidator(countryService, tenantRulesValidationPort)
        );

        context.getStore(NAMESPACE).put(KEY_FIXTURE, fixture);
        context.getStore(NAMESPACE).put(KEY_COUNTRY, countryService);
        context.getStore(NAMESPACE).put(KEY_TENANT_VALIDATION_PORT, tenantRulesValidationPort);
        context.getStore(NAMESPACE).put(KEY_FISHING_LICENSE_NUMBER, licenseNumberService);
    }

    //__________________________________________________________________________________________________________________
    //::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::
    private record ParamDef<C>(Object key, Class<C> clazz, Class<?>... types) {
    }

    private static final List<ParamDef<?>> paramDefs = Arrays.asList(
            new ParamDef<>(KEY_FIXTURE, AggregateTestFixture.class, RegisterEntry.class),
            new ParamDef<>(KEY_COUNTRY, InterceptableCountryService.class),
            new ParamDef<>(KEY_FISHING_LICENSE_NUMBER, InterceptableFishingLicenseNumberService.class)
    );

    private static boolean isParameterMatching(ParamDef<?> paramDef, Parameter parameter) {
        if (!parameter.getType().isAssignableFrom(paramDef.clazz)) return false;

        if (paramDef.types.length == 0) return true;

        final Type type = parameter.getParameterizedType();
        if (type instanceof ParameterizedType) {
            final Type[] actualTypeArguments = ((ParameterizedType) type).getActualTypeArguments();

            if (actualTypeArguments.length != paramDef.types.length) return false;

            for (int i = 0; i < actualTypeArguments.length; i++) {
                if (actualTypeArguments[i] instanceof WildcardType) continue;
                if (!paramDef.types[i].getName().equals(actualTypeArguments[i].getTypeName())) return false;
            }
        }

        return true;
    }

    @Override
    public boolean supportsParameter(ParameterContext parameterContext, ExtensionContext extensionContext) throws ParameterResolutionException {
        return paramDefs.stream().anyMatch(paramDef -> {
            Parameter parameter = parameterContext.getParameter();
            return isParameterMatching(paramDef, parameter);
        });
    }

    @Override
    public Object resolveParameter(ParameterContext parameterContext, ExtensionContext extensionContext) throws ParameterResolutionException {
        final Parameter parameter = parameterContext.getParameter();
        final Object key = paramDefs.stream()
                .filter(paramDef -> isParameterMatching(paramDef, parameter))
                .findFirst()
                .orElseThrow(() -> new ParameterResolutionException("No resolver for parameter type %1$s found.".formatted(parameter.getType().getName())))
                .key();
        return extensionContext.getStore(NAMESPACE).get(key);
    }

}
