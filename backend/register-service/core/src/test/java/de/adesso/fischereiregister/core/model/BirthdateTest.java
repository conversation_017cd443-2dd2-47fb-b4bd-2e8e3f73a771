package de.adesso.fischereiregister.core.model;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class BirthdateTest {

    @Test
    @DisplayName("Birthdate.getAge() returns correct value if person turned 10 years today")
    public void testGetAgeReturnsCorrectValueOnExactly10Years() {
        final LocalDate today = LocalDate.now();

        final Birthdate birthdate = new Birthdate(today.getYear() - 10, today.getMonthValue(), today.getDayOfMonth());

        final Integer age = birthdate.getAge();

        assertEquals(10, age);
    }

    @Test
    @DisplayName("Birthdate.getAge() returns correct value if person turns 10 years tomorrow")
    public void testGetAgeReturnsCorrectValueOnJustBelow10Years() {
        final LocalDate tomorrow = LocalDate.now().plusDays(1);

        final Birthdate birthdate = new Birthdate(tomorrow.getYear() - 10, tomorrow.getMonthValue(), tomorrow.getDayOfMonth());

        final Integer age = birthdate.getAge();

        assertEquals(9, age);
    }

    @Test
    @DisplayName("Birthdate.getAge() returns correct value if person turned 10 years yesterday")
    public void testGetAgeReturnsCorrectValueOnJustAbove10Years() {
        final LocalDate tomorrow = LocalDate.now().minusDays(1);

        final Birthdate birthdate = new Birthdate(tomorrow.getYear() - 10, tomorrow.getMonthValue(), tomorrow.getDayOfMonth());

        final Integer age = birthdate.getAge();

        assertEquals(10, age);
    }


    @Test
    @DisplayName("Birthdate.getAge() returns correct value for partial date when month is known")
    public void testGetAgeReturnsCorrectValueOnPartialDateMonthKnown() {
        final LocalDate today = LocalDate.now();

        final Birthdate birthdate = new Birthdate(today.getYear() - 10, today.getMonthValue(), 0);

        final Integer age = birthdate.getAge();

        assertEquals(10, age);
    }

    @Test
    @DisplayName("Birthdate.getAge() returns correct value for partial date when only year is known")
    public void testGetAgeReturnsCorrectValueOnPartialDateYearOnly() {
        final LocalDate today = LocalDate.now();

        final Birthdate birthdate = new Birthdate(today.getYear() - 10, 0, 0);

        final Integer age = birthdate.getAge();

        assertEquals(10, age);
    }
}
