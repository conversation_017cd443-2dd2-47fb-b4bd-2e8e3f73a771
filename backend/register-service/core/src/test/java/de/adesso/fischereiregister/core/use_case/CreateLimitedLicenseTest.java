package de.adesso.fischereiregister.core.use_case;

import de.adesso.fischereiregister.core.commands.CreateLimitedLicenseCommand;
import de.adesso.fischereiregister.core.events.JurisdictionMovedEvent;
import de.adesso.fischereiregister.core.events.LimitedLicenseCreatedEvent;
import de.adesso.fischereiregister.core.events.PersonCreatedEvent;
import de.adesso.fischereiregister.core.model.Fee;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.ValidityPeriod;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.core.model.type.PaymentType;
import de.adesso.fischereiregister.core.model.type.SubmissionType;
import de.adesso.fischereiregister.core.model.user.UserRole;
import de.adesso.fischereiregister.core.testutils.AxonFixture;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import org.axonframework.test.aggregate.AggregateTestFixture;
import org.axonframework.test.aggregate.TestExecutor;
import org.axonframework.test.matchers.Matchers;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

public class CreateLimitedLicenseTest {
    @ExtendWith(AxonFixture.class)
    @Test
    @DisplayName("Limited license command is handled successfully when a register entry already exists.")
    void testLimitedLicenseAddedToExistingRegisterEntry(AggregateTestFixture<RegisterEntry> fixture) {
        testLimitedLicenseCreation(false, fixture);

    }

    @ExtendWith(AxonFixture.class)
    @Test
    @DisplayName("Limited license command is handled successfully when no register entry exists, i.e. a new entry is created.")
    void testLimitedLicenseAddedWhenNoRegisterEntryExists(AggregateTestFixture<RegisterEntry> fixture) {
        testLimitedLicenseCreation(true, fixture);

    }

    private void testLimitedLicenseCreation(boolean createNew, AggregateTestFixture<RegisterEntry> fixture) {
        final UUID registerEntryId = UUID.randomUUID();

        final LocalDate validFromForFee = LocalDate.of(2021, 4, 1);
        final LocalDate validToForFee = LocalDate.of(2021, 4, 6);

        Fee fee = new Fee();
        fee.setValidFrom(validFromForFee);
        fee.setValidTo(validToForFee);
        fee.setFederalState(FederalState.SH.toString());
        fee.setPaymentInfo(DomainTestData.createPaymentInfo(27.0, PaymentType.ONLINE));

        ValidityPeriod validityPeriod = new ValidityPeriod();
        validityPeriod.setValidFrom(LocalDate.now());

        TestExecutor<RegisterEntry> executor = fixture;
        if (!createNew) {

            executor = fixture.given(
                    new PersonCreatedEvent(
                            registerEntryId,
                            DomainTestData.createPerson(),
                            List.of(),
                            List.of(),
                            "salt",
                            List.of(),
                            DomainTestData.createTaxConsentInfo(),
                            "inboxReference",
                            null,
                            null,
                            null,
                            "SH",
                            SubmissionType.ANALOG
                    ),
                    new JurisdictionMovedEvent(
                            registerEntryId,
                            DomainTestData.createJurisdiction(),
                            DomainTestData.createJurisdiction(),
                            DomainTestData.createJurisdictionConsentInfo(),
                            null,
                            List.of(),
                            "salt",
                            "",
                            List.of(),
                            SubmissionType.ANALOG
                    )
            );
        }

        executor.when(
                        new CreateLimitedLicenseCommand(
                                registerEntryId,
                                "salt",
                                DomainTestData.createLimitedLicenseConsentInfo(),
                                DomainTestData.createPersonWithAddress(),
                                List.of(fee),
                                DomainTestData.createAnalogTaxesWithOneTax(),
                                validityPeriod,
                                DomainTestData.createLimitedLicenseApproval(),
                                DomainTestData.createUserDetails(UserRole.LIMITED_LICENSE_CREATOR)
                        )
                )
                .expectSuccessfulHandlerExecution().expectEventsMatching(Matchers.exactSequenceOf(Matchers.messageWithPayload(Matchers.matches(payload ->
                        payload.getClass().isAssignableFrom(LimitedLicenseCreatedEvent.class)
                ))))
                .expectState(registerEntry -> {
                    // Check fee
                    assertEquals(1, registerEntry.getFees().size());

                    // Check taxes
                    assertEquals(1, registerEntry.getTaxes().size());

                    // Check license
                    assertEquals(1, registerEntry.getFishingLicenses().size());
                    FishingLicense license = registerEntry.getFishingLicenses().get(0);
                    // we expect only one validity period because the vaction license has one validity period after it's creation
                    // after an extension there however will be two validity periods (or maybe even more than two validity periods in future versions of the application)
                    ValidityPeriod validityPeriodFromLicense = license.getValidityPeriods().get(0);
                    assertEquals(FederalState.SH, license.getIssuingFederalState());
                    assertEquals(LocalDate.now(), validityPeriodFromLicense.getValidFrom());
                    assertEquals(LicenseType.LIMITED, license.getType());

                    // Created 4 documents
                    assertEquals(4, registerEntry.getIdentificationDocuments().size());
                    IdentificationDocument licenseDocument = registerEntry.getIdentificationDocuments().stream()
                            .filter(document -> document.getFishingLicense() != null)
                            .findAny()
                            .orElse(null);
                    assertNotNull(licenseDocument);
                    assertEquals(license.getNumber(), licenseDocument.getFishingLicense().getNumber());

                    IdentificationDocument taxDocument = registerEntry.getIdentificationDocuments().stream()
                            .filter(document -> document.getTax() != null)
                            .findAny()
                            .orElse(null);
                    assertNotNull(taxDocument);


                    IdentificationDocument approvalDocument = registerEntry.getIdentificationDocuments().stream()
                            .filter(document -> document.getLimitedLicenseApproval() != null)
                            .findAny()
                            .orElse(null);
                    assertNotNull(approvalDocument);

                    assertNotNull(registerEntry.getJurisdiction());
                });
    }
}
