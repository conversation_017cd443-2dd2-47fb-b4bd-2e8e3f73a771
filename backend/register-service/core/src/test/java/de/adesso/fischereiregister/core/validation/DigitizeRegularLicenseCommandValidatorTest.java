package de.adesso.fischereiregister.core.validation;

import de.adesso.fischereiregister.core.commands.DigitizeRegularLicenseCommand;
import de.adesso.fischereiregister.core.exceptions.ClientInputValidationException;
import de.adesso.fischereiregister.core.exceptions.SystemConfigValidationException;
import de.adesso.fischereiregister.core.interceptables.InterceptableCountryService;
import de.adesso.fischereiregister.core.interceptables.InterceptableTenantRulesValidationPort;
import de.adesso.fischereiregister.core.model.user.UserDetails;
import de.adesso.fischereiregister.core.model.user.UserRole;
import de.adesso.fischereiregister.core.ports.CountryService;
import de.adesso.fischereiregister.core.ports.TenantRulesValidationPort;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

class DigitizeRegularLicenseCommandValidatorTest {

    private DigitizeRegularLicenseCommandValidator commandValidator;

    @BeforeEach
    void setUp() {
        final CountryService countryService = new InterceptableCountryService();
        final TenantRulesValidationPort tenantRulesValidationPort = new InterceptableTenantRulesValidationPort();
        commandValidator = new DigitizeRegularLicenseCommandValidator(countryService, tenantRulesValidationPort);
    }

    @Test
    @DisplayName("DigitizeFishingLicenseCommandValidator.validateOrThrow should not throw any error for a valid command")
    public void testValidateOrThrow_validCommand() {
        // GIVEN
        DigitizeRegularLicenseCommand validCommand = new DigitizeRegularLicenseCommand(
                UUID.randomUUID(),
                UUID.randomUUID().toString(),
                DomainTestData.createPersonWithAddress(),
                List.of(DomainTestData.createAnalogFee()),
                List.of(DomainTestData.createAnalogTax()),
                List.of(DomainTestData.createPreviouslyPayedTax()),
                List.of(DomainTestData.createQualificationsProof()),
                DomainTestData.createConsentInfo(),
                DomainTestData.createUserDetails(UserRole.OFFICIAL)
        );

        // WHEN
        // THEN
        assertDoesNotThrow(() -> commandValidator.validateOrThrow(validCommand));
    }

    @Test
    @DisplayName("DigitizeFishingLicenseCommandValidator.validateOrThrow should throw a ClientInputValidationException with the respective error messages for an invalid command")
    public void testValidateOrThrow_invalidClientInputCommand() {
        // GIVEN
        DigitizeRegularLicenseCommand invalidCommand = new DigitizeRegularLicenseCommand(
                UUID.randomUUID(),
                UUID.randomUUID().toString(),
                null,
                Collections.emptyList(),
                Collections.emptyList(),
                List.of(DomainTestData.createAnalogTax()),
                Collections.emptyList(),
                null,
                DomainTestData.createUserDetails(UserRole.OFFICIAL) // Valid: User Details are provided
        );

        // WHEN
        // THEN
        ClientInputValidationException exception = assertThrows(ClientInputValidationException.class, () -> commandValidator.validateOrThrow(invalidCommand));
        ValidationResult result = exception.getValidationResult();

        assertTrue(result.hasErrors());
        assertEquals(5, result.getErrorNotes().size());
        assertTrue(result.getErrorNotes().contains("Person Details is required"));
        assertTrue(result.getErrorNotes().contains("address is required"));
        assertTrue(result.getErrorNotes().contains("Fees are required"));
        assertTrue(result.getErrorNotes().contains("Previously Paid Taxes Payment amount should not be different than 0"));
        assertTrue(result.getErrorNotes().contains("consentInfo is required"));
    }

    @Test
    @DisplayName("DigitizeFishingLicenseCommandValidator.validateOrThrow should throw a SystemConfigValidationException when user details are invalid")
    public void testValidateOrThrow_invalidSystemConfigCommand() {
        // GIVEN
        DigitizeRegularLicenseCommand invalidCommand = new DigitizeRegularLicenseCommand(
                UUID.randomUUID(),
                null,
                DomainTestData.createPersonWithAddress(),
                List.of(DomainTestData.createAnalogFee()),
                List.of(DomainTestData.createAnalogTax()),
                List.of(DomainTestData.createPreviouslyPayedTax()),
                List.of(DomainTestData.createQualificationsProof()),
                DomainTestData.createConsentInfo(),
                null
        );

        // WHEN
        // THEN
        SystemConfigValidationException exception = assertThrows(SystemConfigValidationException.class, () -> commandValidator.validateOrThrow(invalidCommand));
        ValidationResult result = exception.getValidationResult();

        assertTrue(result.hasErrors());
        assertEquals(2, result.getErrorNotes().size());
        assertTrue(result.getErrorNotes().contains("User Details is required"));
        assertTrue(result.getErrorNotes().contains("Salt is required"));
    }

    @Test
    @DisplayName("DigitizeFishingLicenseCommandValidator.validateOrThrow should throw a SystemConfigValidationException when user details are present but invalid")
    public void testValidateOrThrow_missingOffice() {
        // GIVEN
        // Create user details with null office
        UserDetails userDetailsWithoutOffice = new UserDetails(null,null,null,null,null, Set.of(UserRole.OFFICIAL));

        DigitizeRegularLicenseCommand invalidCommand = new DigitizeRegularLicenseCommand(
                UUID.randomUUID(),
                UUID.randomUUID().toString(),
                DomainTestData.createPersonWithAddress(),
                List.of(DomainTestData.createAnalogFee()),
                List.of(DomainTestData.createAnalogTax()),
                List.of(DomainTestData.createPreviouslyPayedTax()),
                List.of(DomainTestData.createQualificationsProof()),
                DomainTestData.createConsentInfo(),
                userDetailsWithoutOffice
        );

        // WHEN
        // THEN
        SystemConfigValidationException exception = assertThrows(SystemConfigValidationException.class, () -> commandValidator.validateOrThrow(invalidCommand));
        ValidationResult result = exception.getValidationResult();

        assertTrue(result.hasErrors());
        assertTrue(result.getErrorNotes().contains("User Office is required"));
    }
}