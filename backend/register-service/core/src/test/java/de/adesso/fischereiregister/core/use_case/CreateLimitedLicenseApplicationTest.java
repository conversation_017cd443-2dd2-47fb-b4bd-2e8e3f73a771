package de.adesso.fischereiregister.core.use_case;

import de.adesso.fischereiregister.core.commands.CreateLimitedLicenseCommand;
import de.adesso.fischereiregister.core.commands.OSCreateLimitedLicenseApplicationCommand;
import de.adesso.fischereiregister.core.events.JurisdictionMovedEvent;
import de.adesso.fischereiregister.core.events.LimitedLicenseApplicationCreatedEvent;
import de.adesso.fischereiregister.core.events.LimitedLicenseCreatedEvent;
import de.adesso.fischereiregister.core.model.Fee;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.Jurisdiction;
import de.adesso.fischereiregister.core.model.LimitedLicenseApplication;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.core.model.type.LimitedLicenseApplicationStatus;
import de.adesso.fischereiregister.core.model.type.SubmissionType;
import de.adesso.fischereiregister.core.model.user.UserRole;
import de.adesso.fischereiregister.core.testutils.AxonFixture;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import org.axonframework.test.aggregate.AggregateTestFixture;
import org.axonframework.test.matchers.Matchers;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

import java.time.LocalDate;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

public class CreateLimitedLicenseApplicationTest {

    @Test
    @DisplayName("Test whether aggregate handlers CreateLimitedLicenseApplicationCommand are successful.")
    @ExtendWith(AxonFixture.class)
    public void testCreateLimitedLicenseApplication(AggregateTestFixture<RegisterEntry> fixture) {
        // GIVEN
        // Create a command to create a limited license application
        final OSCreateLimitedLicenseApplicationCommand command = new OSCreateLimitedLicenseApplicationCommand(
                DomainTestData.registerId,
                FederalState.SH,
                DomainTestData.createPersonWithAddress(),
                DomainTestData.createDigitalFee(),
                DomainTestData.createConsentInfo(),
                "disabilityCertificateFileURL",
                "inboxReference",
                "serviceAccountId",
                "transactionId"
        );

        // WHEN
        // Send the command to the fixture
        fixture.when(command)
                .expectSuccessfulHandlerExecution()
                .expectEventsMatching(Matchers.exactSequenceOf(
                        Matchers.messageWithPayload(Matchers.matches(payload ->
                                payload.getClass().isAssignableFrom(LimitedLicenseApplicationCreatedEvent.class)
                        )),
                        Matchers.messageWithPayload(Matchers.matches(payload ->
                                payload.getClass().isAssignableFrom(JurisdictionMovedEvent.class)
                        ))
                ))
                .expectState(registerEntry -> {
                    // Assert that the register entry has been created with the expected properties
                    assertEquals(DomainTestData.registerId, registerEntry.getRegisterId());
                    assertNotNull(registerEntry.getPerson());

                    // Assert that limited license application is created with the expected properties
                    assertNotNull(registerEntry.getLimitedLicenseApplication());
                    LimitedLicenseApplication limitedLicenseApplication = registerEntry.getLimitedLicenseApplication();
                    assertNotNull(limitedLicenseApplication.getId());
                    assertEquals(FederalState.SH, limitedLicenseApplication.getFederalState());
                    assertEquals("disabilityCertificateFileURL", limitedLicenseApplication.getDisabilityCertificateFileURL());
                    assertEquals(LocalDate.now(), limitedLicenseApplication.getCreatedAt());
                    assertEquals(LimitedLicenseApplicationStatus.PENDING, limitedLicenseApplication.getStatus());

                    // Tests fees and transaction id is correctly set
                    assertEquals(1, registerEntry.getFees().size());
                    Fee fee = registerEntry.getFees().getFirst();
                    assertEquals(FederalState.SH.toString(), fee.getFederalState());
                    assertEquals("transactionId", fee.getPaymentInfo().getTransactionId());

                    // Test no documents or licenses were created
                    assertEquals(0, registerEntry.getIdentificationDocuments().size());
                    assertEquals(0, registerEntry.getFishingLicenses().size());

                    // Test OS information is correctly set
                    assertEquals("inboxReference", registerEntry.getInboxReference());
                    assertEquals("serviceAccountId", registerEntry.getServiceAccountId());

                    assertNotNull(registerEntry.getJurisdiction());
                    assertEquals(FederalState.SH.toString(), registerEntry.getJurisdiction().getFederalState());
                });

        // THEN
        // Verify that the command was handled correctly and the expected events were published
    }

    @Test
    @DisplayName("Test whether aggregate handlers of CreateLimitedLicense delete the current limited license application when a new one is created.")
    @ExtendWith(AxonFixture.class)
    public void testCreateLimitedLicenseApplicationDeletesCurrentApplication(AggregateTestFixture<RegisterEntry> fixture) {
        // GIVEN
        // Create a command to create a limited license application
        final CreateLimitedLicenseCommand command = new CreateLimitedLicenseCommand(
                DomainTestData.registerId,
                "salt",
                DomainTestData.createLimitedLicenseConsentInfo(),
                DomainTestData.createPersonWithAddress(),
                List.of(),
                List.of(),
                DomainTestData.createValidityPeriod(LocalDate.now(), LocalDate.now().plusYears(1)),
                DomainTestData.createLimitedLicenseApproval(),
                DomainTestData.createUserDetails(UserRole.LIMITED_LICENSE_CREATOR)
        );

        // WHEN
        // Send the command to the fixture
        fixture.given(
                        new LimitedLicenseApplicationCreatedEvent(
                                DomainTestData.registerId,
                                DomainTestData.createLimitedLicenseApplication(),
                                DomainTestData.createPerson(),
                                List.of(DomainTestData.createDigitalFee()),
                                DomainTestData.createConsentInfo(),
                                "inboxReference",
                                "serviceAccountId"
                        ),
                        new JurisdictionMovedEvent(
                                DomainTestData.registerId,
                                new Jurisdiction(),
                                DomainTestData.createJurisdiction(),
                                DomainTestData.createJurisdictionConsentInfo(),
                                null,
                                List.of(),
                                "salt",
                                "office",
                                List.of(),
                                SubmissionType.ANALOG
                        )
                )
                .when(command)
                .expectSuccessfulHandlerExecution()
                .expectEventsMatching(Matchers.exactSequenceOf(
                        Matchers.messageWithPayload(Matchers.matches(payload ->
                                {
                                    final boolean isCorrectEventType = payload.getClass().isAssignableFrom(LimitedLicenseCreatedEvent.class);
                                    if (!isCorrectEventType) {
                                        return false;
                                    }

                                    // Check whether the event sends the correct inbox reference, when it already existed via the application
                                    final LimitedLicenseCreatedEvent event = (LimitedLicenseCreatedEvent) payload;
                                    return event.inboxReference().equals("inboxReference");
                                }
                        ))
                ))
                .expectState(registerEntry -> {
                    assertEquals(1, registerEntry.getFishingLicenses().size());
                    final FishingLicense license = registerEntry.getFishingLicenses().getFirst();
                    assertEquals(LicenseType.LIMITED, license.getType());

                    assertNull(registerEntry.getLimitedLicenseApplication());
                });
    }
}
