package de.adesso.fischereiregister.core.validation;

import de.adesso.fischereiregister.core.commands.OrderReplacementCardCommand;
import de.adesso.fischereiregister.core.exceptions.ClientInputValidationException;
import de.adesso.fischereiregister.core.exceptions.SystemConfigValidationException;
import de.adesso.fischereiregister.core.interceptables.InterceptableCountryService;
import de.adesso.fischereiregister.core.interceptables.InterceptableTenantRulesValidationPort;
import de.adesso.fischereiregister.core.model.user.UserRole;
import de.adesso.fischereiregister.core.ports.CountryService;
import de.adesso.fischereiregister.core.ports.TenantRulesValidationPort;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.Collections;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

class OrderReplacementCardCommandValidatorTest {

    private OrderReplacementCardCommandValidator commandValidator;

    @BeforeEach
    void setUp() {
        final CountryService countryService = new InterceptableCountryService();
        final TenantRulesValidationPort tenantRulesValidationPort = new InterceptableTenantRulesValidationPort();
        commandValidator = new OrderReplacementCardCommandValidator(countryService, tenantRulesValidationPort);
    }

    @Test
    @DisplayName("OrderReplacementCardCommandValidator.validateOrThrow should not throw any error for a valid command")
    public void testValidateOrThrow_validCommand() {
        // GIVEN
        OrderReplacementCardCommand validCommand = new OrderReplacementCardCommand(
                UUID.randomUUID(),
                "licenseNumber",
                DomainTestData.createPersonWithAddress(),
                UUID.randomUUID().toString(),
                List.of(DomainTestData.createAnalogFee()),
                List.of(DomainTestData.createAnalogTax()),
                DomainTestData.createConsentInfo(),
                DomainTestData.createUserDetails(UserRole.OFFICIAL)
        );

        // WHEN
        // THEN
        assertDoesNotThrow(() -> commandValidator.validateOrThrow(validCommand));
    }

    @Test
    @DisplayName("OrderReplacementCardCommandValidator.validateOrThrow should throw a ClientInputValidationException with the respective error messages for an invalid command")
    public void testValidateOrThrow_invalidClientInputCommand() {
        // GIVEN
        OrderReplacementCardCommand invalidCommand = new OrderReplacementCardCommand(
                UUID.randomUUID(),
                null,
                null,
                UUID.randomUUID().toString(),
                Collections.emptyList(),
                Collections.emptyList(),
                null,
                DomainTestData.createUserDetails(UserRole.OFFICIAL)
        );

        // WHEN
        // THEN
        ClientInputValidationException exception = assertThrows(ClientInputValidationException.class, () -> commandValidator.validateOrThrow(invalidCommand));
        ValidationResult result = exception.getValidationResult();

        assertTrue(result.hasErrors());
        assertEquals(4, result.getErrorNotes().size());
        assertTrue(result.getErrorNotes().contains("Person Details is required"));
        assertTrue(result.getErrorNotes().contains("address is required"));
        assertTrue(result.getErrorNotes().contains("Fees are required"));
        assertTrue(result.getErrorNotes().contains("consentInfo is required"));
    }

    @Test
    @DisplayName("OrderReplacementCardCommandValidator.validateOrThrow should throw a SystemConfigValidationException when user details are invalid")
    public void testValidateOrThrow_invalidSystemConfigCommand() {
        // GIVEN
        OrderReplacementCardCommand invalidCommand = new OrderReplacementCardCommand(
                UUID.randomUUID(),
                "licenseNumber",
                DomainTestData.createPersonWithAddress(),
                null,
                List.of(DomainTestData.createAnalogFee()),
                List.of(DomainTestData.createAnalogTax()),
                DomainTestData.createConsentInfo(),
                null
        );

        // WHEN
        // THEN
        SystemConfigValidationException exception = assertThrows(SystemConfigValidationException.class, () -> commandValidator.validateOrThrow(invalidCommand));
        ValidationResult result = exception.getValidationResult();

        assertTrue(result.hasErrors());
        assertEquals(2, result.getErrorNotes().size());
        assertTrue(result.getErrorNotes().contains("User Details is required"));
        assertTrue(result.getErrorNotes().contains("Salt is required"));
    }
}