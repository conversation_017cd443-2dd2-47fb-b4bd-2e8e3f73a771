package de.adesso.fischereiregister.core.validation;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.Collections;
import java.util.List;
import java.util.regex.Pattern;

import static de.adesso.fischereiregister.core.validation.utils.ValidationUtils.validateBooleanFieldIsTrue;
import static de.adesso.fischereiregister.core.validation.utils.ValidationUtils.validateFieldRequired;
import static de.adesso.fischereiregister.core.validation.utils.ValidationUtils.validateStringField;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

class ValidationUtilsTest {

    @Test
    @DisplayName("ValidationUtils.validateBooleanFieldIsTrue should add error when field is false or null")
    void validateBooleanFieldIsTrue_falseOrNull() {
        // Given
        ValidationResult validationResult = new ValidationResult();

        // When
        validateBooleanFieldIsTrue(false, "testField", validationResult);
        validateBooleanFieldIsTrue(null, "testField", validationResult);

        // Then
        assertEquals(2, validationResult.getErrorNotes().size());
        assertTrue(validationResult.getErrorNotes().contains("testField must be set to true"));
    }

    @Test
    @DisplayName("ValidationUtils.validateBooleanFieldIsTrue should not add error when field is true")
    void validateBooleanFieldIsTrue_true() {
        // Given
        ValidationResult validationResult = new ValidationResult();

        // When
        validateBooleanFieldIsTrue(true, "testField", validationResult);

        // Then
        assertEquals(0, validationResult.getErrorNotes().size());
    }

    @Test
    @DisplayName("ValidationUtils.validateFieldRequired should add error when string is null or blank")
    void validateFieldRequired_stringNullOrBlank() {
        // Given
        ValidationResult validationResult = new ValidationResult();

        // When
        validateFieldRequired("", "testField", validationResult);
        validateFieldRequired("   ", "testField", validationResult);

        // Then
        assertEquals(2, validationResult.getErrorNotes().size());
        assertTrue(validationResult.getErrorNotes().contains("testField is required"));
    }

    @Test
    @DisplayName("ValidationUtils.validateFieldRequired should not add error when string is not blank")
    void validateFieldRequired_validString() {
        // Given
        ValidationResult validationResult = new ValidationResult();

        // When
        validateFieldRequired("valid", "testField", validationResult);

        // Then
        assertEquals(0, validationResult.getErrorNotes().size());
    }

    @Test
    @DisplayName("ValidationUtils.validateStringField should add error when string contains invalid characters")
    void validateStringField_invalidCharacters() {
        // Given
        ValidationResult validationResult = new ValidationResult();
        Pattern pattern = Pattern.compile("^[a-zA-Z]+$"); // Only letters allowed

        // When
        validateStringField("invalid123", "testField", 50, pattern, validationResult);

        // Then
        assertEquals(1, validationResult.getErrorNotes().size());
        assertTrue(validationResult.getErrorNotes().contains("testField contains invalid characters."));
    }

    @Test
    @DisplayName("ValidationUtils.validateFieldRequired should add error when list is null or empty")
    void validateFieldRequired_listNullOrEmpty() {
        // Given
        ValidationResult validationResult = new ValidationResult();

        // When
        validateFieldRequired((List<String>) null, "testList", validationResult);
        validateFieldRequired(Collections.emptyList(), "testList", validationResult);

        // Then
        assertEquals(2, validationResult.getErrorNotes().size());
        assertTrue(validationResult.getErrorNotes().contains("testList are required"));
    }

    @Test
    @DisplayName("ValidationUtils.validateFieldRequired should not add error when list is not empty")
    void validateFieldRequired_validList() {
        // Given
        ValidationResult validationResult = new ValidationResult();

        // When
        validateFieldRequired(List.of("item"), "testList", validationResult);

        // Then
        assertEquals(0, validationResult.getErrorNotes().size());
    }
}