package de.adesso.fischereiregister.card_orders.services;

import de.adesso.fischereiregister.card_orders.persistence.CardOrder;
import de.adesso.fischereiregister.card_orders.persistence.CardOrderRepository;
import de.adesso.fischereiregister.card_orders.persistence.OrderStatus;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.ports.ErrorsProtocolCardOrdersPort;
import jakarta.persistence.EntityNotFoundException;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Component
@AllArgsConstructor
public class CardOrderService {

    private final HttpOrderService httpOrderService;
    private final CardOrderRepository cardOrderRepository;
    private final ErrorsProtocolCardOrdersPort errorsProtocolCardOrdersPort;

    /**
     * Will create a new card order in the database with the initial status NOT_TRANSMITTED and submit the order to the
     * card printing software via REST HTTP.
     * <p>
     * Once the order is submitted, the status of the order will be updated to TRANSMITTED.
     */
    public void issueCardOrder(UUID registerEntryId, Person person, FishingLicense license, IdentificationDocument document, String salt, String issuedByAddress, String issuedByOffice) {
        CardOrder cardOrder = new CardOrder();
        cardOrder.setOrderId(UUID.randomUUID());
        cardOrder.setStatus(OrderStatus.NOT_TRANSMITTED);
        cardOrder.setStatusNote("");
        cardOrder.setLicenseNumber(license.getNumber());
        cardOrder.setIdentificationDocumentId(document.getDocumentId());
        cardOrder.setUpdatedAt(LocalDateTime.now());
        cardOrderRepository.save(cardOrder);

        final RegisterOrderInformation registerInformation = new RegisterOrderInformation(license, person, registerEntryId, document);

        try {

            httpOrderService.registerOrder(
                    cardOrder.getOrderId(),
                    LocalDate.now(),
                    registerInformation,
                    salt,
                    issuedByAddress);
        } catch (Exception e) {
            errorsProtocolCardOrdersPort.reportCardOrdersError(issuedByOffice, license.getIssuingFederalState().toString(), LocalDate.now().getYear());
            return;
        }

        cardOrder.setUpdatedAt(LocalDateTime.now());
        cardOrder.setStatus(OrderStatus.TRANSMITTED);
        cardOrderRepository.save(cardOrder);
    }

    public void updateCardOrderStatus(UUID orderId, OrderStatus orderStatus, String statusNote) throws EntityNotFoundException {
        CardOrder cardOrder = getCardOrder(orderId);

        cardOrder.setStatus(orderStatus);
        cardOrder.setStatusNote(statusNote);
        cardOrder.setUpdatedAt(LocalDateTime.now());

        cardOrderRepository.save(cardOrder);
    }

    public CardOrder getCardOrder(UUID orderId) throws EntityNotFoundException {
        return cardOrderRepository
                .findById(orderId)
                .orElseThrow(() -> new EntityNotFoundException("Card order not found"));
    }

    /**
     * Will return all orders registered for the given license number.
     * <p>
     * Will return an empty list, if now orders could be found.
     */
    public List<CardOrder> findOrdersFor(String licenseNumber) {
        return cardOrderRepository.findByLicenseNumber(licenseNumber);
    }
}
