package de.adesso.fischereiregister.card_orders.persistence;

import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.UUID;

@Entity
@Table
@Getter
@Setter
public class CardOrder {
    @Id
    private UUID orderId;

    private String licenseNumber;

    private String identificationDocumentId;

    @Enumerated(EnumType.STRING)
    private OrderStatus status;

    private String statusNote;

    @Temporal(TemporalType.TIMESTAMP)
    private LocalDateTime updatedAt;
}
