package de.adesso.fischereiregister.card_orders.services;

import de.adesso.fischereiregister.core.model.type.LicenseType;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "default")
public interface OrderRequestLicenseTypeMapper {
    OrderRequestLicenseTypeMapper INSTANCE = Mappers.getMapper(OrderRequestLicenseTypeMapper.class);

    default OrderRequestLicenseType toOrderRequestLicenseType(LicenseType licenseType) {
        return switch (licenseType) {
            case LIMITED -> OrderRequestLicenseType.S;
            case VACATION -> OrderRequestLicenseType.U;
            case REGULAR -> OrderRequestLicenseType.A;
            default ->
                    throw new IllegalArgumentException("LicenseType " + licenseType + " could not be parsed to Order Request");
        };
    }
}
