package de.adesso.fischereiregister.migrations.migrate_0_17_to_1_0_0;

import com.fasterxml.jackson.databind.node.NullNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.databind.node.TextNode;
import de.adesso.fischereiregister.core.events.JurisdictionMovedEvent;
import org.axonframework.serialization.SimpleSerializedType;
import org.axonframework.serialization.upcasting.event.IntermediateEventRepresentation;
import org.axonframework.serialization.upcasting.event.SingleEventUpcaster;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
@Order(2)
public class JurisdictionMovedEvent2_to_3_Upcaster extends SingleEventUpcaster {
    private static final String PAYLOAD_TYPE = JurisdictionMovedEvent.class.getTypeName();
    private static final String EXPECTED_REVISION = "2.0";
    private static final String UPCASTED_REVISION = "3.0";

    private static final String JURISDICTION = "jurisdiction";
    private static final String BAN = "ban";

    private static final String NEW_JURISDICTION = "newJurisdiction";
    private static final String PREVIOUS_JURISDICTION = "previousJurisdiction";
    private static final String SUBMISSION_TYPE = "submissionType";

    @Override
    protected boolean canUpcast(IntermediateEventRepresentation intermediateRepresentation) {
        return intermediateRepresentation.getType().getName().equals(PAYLOAD_TYPE) &&
                Objects.equals(intermediateRepresentation.getType().getRevision(), EXPECTED_REVISION);
    }

    @Override
    protected IntermediateEventRepresentation doUpcast(IntermediateEventRepresentation intermediateRepresentation) {
        return intermediateRepresentation.upcastPayload(
                new SimpleSerializedType(PAYLOAD_TYPE, UPCASTED_REVISION),
                ObjectNode.class,
                event -> {
                    if (event.has(JURISDICTION)) {
                        ObjectNode currentJurisdiction = (ObjectNode) event.get(JURISDICTION);

                        event.set(NEW_JURISDICTION, currentJurisdiction);
                        event.set(PREVIOUS_JURISDICTION, NullNode.getInstance());
                        event.remove(JURISDICTION);
                    }

                    if (!event.has(BAN)) {
                        event.set(BAN, NullNode.getInstance());
                    }

                    if (!event.has(SUBMISSION_TYPE)) {
                        event.set(SUBMISSION_TYPE, TextNode.valueOf("ANALOG"));
                    }

                    return event;
                }
        );
    }
}
