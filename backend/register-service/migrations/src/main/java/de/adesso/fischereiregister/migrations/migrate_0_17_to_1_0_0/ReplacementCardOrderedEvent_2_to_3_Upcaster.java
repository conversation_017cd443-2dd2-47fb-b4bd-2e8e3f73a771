package de.adesso.fischereiregister.migrations.migrate_0_17_to_1_0_0;

import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.databind.node.TextNode;
import de.adesso.fischereiregister.core.events.ReplacementCardOrderedEvent;
import org.axonframework.serialization.SimpleSerializedType;
import org.axonframework.serialization.upcasting.event.IntermediateEventRepresentation;
import org.axonframework.serialization.upcasting.event.SingleEventUpcaster;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
@Order(2)
public class ReplacementCardOrderedEvent_2_to_3_Upcaster extends SingleEventUpcaster {
    private static final String PAYLOAD_TYPE = ReplacementCardOrderedEvent.class.getTypeName();
    private static final String EXPECTED_REVISION = "2.0";
    private static final String UPCASTED_REVISION = "3.0";

    private static final String PERSON = "person";
    private static final String SUBMISSION_TYPE = "submissionType";
    private static final String INBOX_REFERENCE = "inboxReference";

    @Override
    protected boolean canUpcast(IntermediateEventRepresentation intermediateRepresentation) {
        return intermediateRepresentation.getType().getName().equals(PAYLOAD_TYPE) &&
                Objects.equals(intermediateRepresentation.getType().getRevision(), EXPECTED_REVISION);
    }

    @Override
    protected IntermediateEventRepresentation doUpcast(IntermediateEventRepresentation intermediateRepresentation) {
        return intermediateRepresentation.upcastPayload(
                new SimpleSerializedType(PAYLOAD_TYPE, UPCASTED_REVISION),
                ObjectNode.class,
                (event) -> {
                    event.set(PERSON, DomainObject2_to_3Upcaster.upcastPerson((ObjectNode) event.get(PERSON)));

                    if (!event.has(SUBMISSION_TYPE)) {
                        if (event.has(INBOX_REFERENCE) || event.hasNonNull(INBOX_REFERENCE)) {
                            event.set(SUBMISSION_TYPE, TextNode.valueOf("ONLINE"));
                        } else {
                            event.set(SUBMISSION_TYPE, TextNode.valueOf("ANALOG"));

                        }
                    }

                    if (!event.has("issuedByOffice")) {
                        event.set("issuedByOffice", TextNode.valueOf("Konnte nicht ermittelt werden"));
                    }

                    return event;
                }
        );
    }
}
