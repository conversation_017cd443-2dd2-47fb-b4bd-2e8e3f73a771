package de.adesso.fischereiregister.migrations.migrate_0_16_9_to_0_17_5;

import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import de.adesso.fischereiregister.core.events.PersonCreatedEvent;
import org.axonframework.serialization.SimpleSerializedType;
import org.axonframework.serialization.upcasting.event.IntermediateEventRepresentation;
import org.axonframework.serialization.upcasting.event.SingleEventUpcaster;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
@Order(0)
public class PersonCreatedEvent0_to_2Upcaster extends SingleEventUpcaster {
    private static final String PAYLOAD_TYPE = PersonCreatedEvent.class.getTypeName();
    private static final String EXPECTED_REVISION = null;
    private static final String UPCASTED_REVISION = "2.0";

    @Override
    protected boolean canUpcast(IntermediateEventRepresentation intermediateRepresentation) {
        return intermediateRepresentation.getType().getName().equals(PAYLOAD_TYPE) &&
                Objects.equals(intermediateRepresentation.getType().getRevision(), EXPECTED_REVISION);
    }

    @Override
    protected IntermediateEventRepresentation doUpcast(IntermediateEventRepresentation intermediateRepresentation) {
        return intermediateRepresentation.upcastPayload(
                new SimpleSerializedType(PAYLOAD_TYPE, UPCASTED_REVISION),
                ObjectNode.class,
                event -> {
                    ArrayNode documents = (ArrayNode) event.get("identificationDocuments");
                    event.replace("identificationDocuments", DomainObject0_to_2Upcaster.upcastIdentificationDocuments(documents));
                    return event;
                }
        );
    }
}
