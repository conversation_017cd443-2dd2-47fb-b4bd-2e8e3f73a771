volumes:
  data: null
services:
  db:
    image: postgres:15
    environment:
      POSTGRES_USER: digifischdok
      POSTGRES_PASSWORD: digifischdok
      POSTGRES_DB: digifischdok
    volumes:
      - ./docker-volumes/postgres/:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    container_name: digifischdok-db

  mailhog:
    image: mailhog/mailhog
    logging:
      driver: none # disable saving logs
    ports:
      - 1025:1025 # smtp server
      - 8025:8025 # web ui

  s3:
    image: minio/minio
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:9000/minio/health/live" ]
      interval: 1s
      timeout: 20s
      retries: 20
    volumes:
      - ./docker-volumes/minio/data:/data
    command: server /data --console-address ":9001"
    container_name: digifischdok-s3

  s3_init:
    image: minio/mc
    container_name: s3_init
    depends_on:
      s3:
        condition: service_healthy
    restart: "no"
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin
      BUCKET_NAME: digifischdok
    entrypoint: >
      /bin/sh -c "
        set -e
        mc alias set s3 http://s3:9000 $$MINIO_ROOT_USER $$MINIO_ROOT_PASSWORD
        mc mb --ignore-existing s3/$$BUCKET_NAME
        mc cp -r /init-data/* s3/$$BUCKET_NAME/
        exit 0;
      "
    volumes:
      - ./shared/resources/s3-init-data:/init-data