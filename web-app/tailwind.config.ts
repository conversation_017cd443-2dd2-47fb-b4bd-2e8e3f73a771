import {Config} from 'tailwindcss';
import {fontFamily} from 'tailwindcss/defaultTheme';

export default {
  content: ['./src/**/*.{html,ts}'],
  theme: {
    screens: {
      md: '768px',
      lg: '1024px',
      xl: '1280px',
      '2xl': '1536px',
    },
    colors: {
      // SEMANTIC COLORS (generic colors are not defined)
      action: {
        primary: {
          DEFAULT: '#163BBF',
          glass: '#163BBFCC',
          hover: '#3354C9',
          pressed: '#0D2372',
          disabled: '#FFFFFF7A',
          text: '#FFFFFF',
          icon: '#FFFFFF',
        },
        secondary: {
          DEFAULT: '#FFFFFF7A',
          glass: '#FFFFFFB8',
          hover: '#F1F3FB',
          pressed: '#E3E8F8',
          disabled: '#FFFFFF7A',
        },
        text: {
          disabled: '#78889B',
        },
        icon: {
          disabled: '#78889B',
        },
        border: {
          DEFAULT: '#9FAFE6',
          disabled: '#A8B1BF',
          focus: '#1FBCFF',
          primary: '',
        },
      },
      border: {
        divider: {
          DEFAULT: '#CDD2DA',
          alternative: '#A8B1BF',
          grey: '#E6E8EC',
          white: '#FFFFFF',
        },
        outline: {
          glass: '#E3E8F839',
        },
      },
      feedback: {
        text: {
          success: '#163BBF',
          info: '#163BBF',
          warning: '#AD285F',
          error: '#AD285F',
          fail: '#CB3973',
        },
        background: {
          info: {
            1: '#FFFFFF3D',
            2: '#C8D1F0',
          },
          warning: {
            1: '#FDF0F4',
            2: '#FBE2E8',
          },
        },
        border: {
          warning: '#AD285F',
        },
      },
      icon: {
        primary: '#272D33',
        secondary: '#424C59',
      },
      background: {
        DEFAULT: '#FFFFFF',
        2: '#F2F4F6',
        3: '#E6E8EC',
        overlay: '#272D33CC',
        glass: {
          primary: '#FFFFFF3D',
          secondary: '#FFFFFFB8',
        },
      },
      layout: {
        gradient: '#E3E8F8',
      },
      font: {
        primary: '#272D33',
        secondary: '#526071',
      },
      checkbox: {
        border: {
          DEFAULT: '#424C59',
          checked: '#6A82D7',
        },
      },
      confirmbox: {
        border: '#9FAFE6',
      },
      noticearea: {
        fill: {
          DEFAULT: '#A8B1BF3D',
          checked: '#9FAFE63D',
        },
        border: {
          checked: '#9FAFE6',
        },
      },
      tabs: {
        text: '#CDD2DA',
        indicator: '#E6E8EC',
      },
      // Tints are used to simulate the shadow effect.
      // In FIGMA CSS box-shadows are used instead which are not feasible.
      // Instead we put the elements on this background and use inner shadows instead (see below shadow-section)
      tint: {
        blue: {
          DEFAULT: '#163bbf33',
          pressed: '#163bbf40',
        },
        white: {
          DEFAULT: '#cdd2da66',
          glass: '#EEF0F2',
        },
        red: {
          DEFAULT: '#ad285f33',
          pressed: '#F5C5D2',
        },
      },
      transparent: '#00000000',
    },
    fontSize: {
      xs: '12px',
      s: '16px',
      base: '24px',
      l: '32px',
      xl: '48px',
    },
    fontWeight: {
      thin: '300',
      normal: '400',
      bold: '700',
      'extra-bold': '800',
    },
    fontFamily: {
      sans: ['Noto Sans', ...fontFamily.sans],
      lexend: ['Lexend', ...fontFamily.sans],
    },
    transitionDuration: {
      '240': '240ms',
      '120': '120ms',
    },
    transitionDelay: {
      '240': '240ms',
      '120': '120ms',
    },
    extend: {
      height: {
        'page-content': 'calc(100vh - 118px)', // height of page without header
        'page-content-sm': 'calc(100vh - 220px)', // height of page content without header and without full profile bar
      },
      minHeight: {
        'page-content': 'calc(100vh - 118px)',
        'page-content-sm': 'calc(100vh - 220px)',
      },
      maxHeight: {
        'page-content': 'calc(100vh - 118px)',
        'page-content-sm': 'calc(100vh - 220px)',
      },
      boxShadow: {
        'glass-blue-tint-primary':
          '0px -2px 8px 0px rgba(200, 209, 240, 0.32) inset, 0px 2px 4px 0px rgba(22, 59, 191, 0.96) inset, 0px 33px 9px 0px rgba(22, 59, 191, 0.00), 0px 21px 9px 0px rgba(22, 59, 191, 0.04), 0px 12px 7px 0px rgba(22, 59, 191, 0.12), 0px 5px 5px 0px rgba(22, 59, 191, 0.20), 0px 1px 3px 0px rgba(22, 59, 191, 0.24);',
        'glass-blue-tint': `0 0 #0000 inset,
          0px 24px 10px 0px rgba(256, 256, 256, 0.18) inset,
          0px 14px 8px 0px rgba(256, 256, 256, 0.12) inset,
          0px 6px 6px 0px rgba(256, 256, 256, 0.07) inset,
          0px 2px 3px 0px rgba(256, 256, 256, 0.04) inset,
          0px 2px 4px 0px rgba(22, 59, 191, 0.24) inset,
          0px 38px 11px 0px rgba(22, 59, 191, 0.00),
          0px 24px 10px 0px rgba(22, 59, 191, 0.01),
          0px 14px 8px 0px rgba(22, 59, 191, 0.04),
          0px 6px 6px 0px rgba(22, 59, 191, 0.07),
          0px 2px 3px 0px rgba(22, 59, 191, 0.08);`,
        'glass-blue-tint-pressed': `0 0 #0000 inset,
          0px 10px 4px 0px rgba(256, 256, 256, 0.18) inset,
          0px 5px 3px 0px rgba(256, 256, 256, 0.11) inset,
          0px 2px 2px 0px rgba(256, 256, 256, 0.07) inset,
          0px 1px 1px 0px rgba(256, 256, 256, 0.04) inset,
          0px 2px 4px 0px rgba(22, 59, 191, 0.18) inset,
          0px 15px 4px 0px rgba(22, 59, 191, 0.00),
          0px 10px 4px 0px rgba(22, 59, 191, 0.01),
          0px 5px 3px 0px rgba(22, 59, 191, 0.05),
          0px 2px 2px 0px rgba(22, 59, 191, 0.09),
          0px 1px 1px 0px rgba(22, 59, 191, 0.10);`,
        'elevation-with-blue-action':
          '0 0 #0000 inset, 0 0 #0000 inset, 0px 0px 1px 0px rgba(23, 59, 191, 0.16), 0px 2px 2px 0px rgba(23, 59, 191, 0.14), 0px 4px 3px 0px rgba(23, 59, 191, 0.08), 0px 7px 3px 0px rgba(23, 59, 191, 0.02), 0px 12px 3px 0px rgba(23, 59, 191, 0.00);',
        'glass-white-tint': `0 0 #0000 inset,
          0px 25px 10px 0px rgba(256, 256, 256, 0.18) inset,
          0px 14px 8px 0px rgba(256, 256, 256, 0.11) inset,
          0px 6px 6px 0px rgba(256, 256, 256, 0.07) inset,
          0px 2px 3px 0px rgba(256, 256, 256, 0.06) inset,
          0px 2px 4px 0px rgba(82, 96, 113, 0.24) inset,
          0px 39px 11px 0px rgba(205, 210, 218, 0.00),
          0px 25px 10px 0px rgba(205, 210, 218, 0.02),
          0px 14px 8px 0px rgba(205, 210, 218, 0.08),
          0px 6px 6px 0px rgba(205, 210, 218, 0.14),
          0px 2px 3px 0px rgba(205, 210, 218, 0.16);`,
        'glass-red-tint': `0 0 #0000 inset,
          0px 24px 10px 0px rgba(256, 256, 256, 0.18) inset,
          0px 14px 8px 0px rgba(256, 256, 256, 0.12) inset,
          0px 6px 6px 0px rgba(256, 256, 256, 0.07) inset,
          0px 2px 3px 0px rgba(256, 256, 256, 0.04) inset,
          0px 2px 4px 0px rgba(136, 37, 76, 0.12) inset,
          0px 38px 11px 0px rgba(173, 40, 95, 0.00),
          0px 24px 10px 0px rgba(173, 40, 95, 0.01),
          0px 14px 8px 0px rgba(173, 40, 95, 0.04),
          0px 6px 6px 0px rgba(173, 40, 95, 0.07),
          0px 2px 3px 0px rgba(173, 40, 95, 0.08);`,
        'glass-red-tint-pressed': `0 0 #0000 inset,
          0px 1px 1px 0px rgba(136, 37, 76, 0.1),
          0px 2px 2px 0px rgba(136, 37, 76, 0.09),
          0px 5px 3px 0px rgba(136, 37, 76, 0.05),
          0px 10px 4px 0px rgba(214, 93, 135, 0.01),
          0px 15px 4px 0px rgba(136, 37, 76, 0),
          0px 2px 4px 0px rgba(136, 37, 76, 0.12) inset;`,
        'glass-darkened': `0 0 #0000 inset,
          0px 24px 10px 0px rgba(256, 256, 256, 0.18) inset,
          0px 14px 8px 0px rgba(256, 256, 256, 0.12) inset,
          0px 6px 6px 0px rgba(256, 256, 256, 0.07) inset,
          0px 2px 3px 0px rgba(256, 256, 256, 0.04) inset,
          0px 2px 4px 0px rgba(136, 37, 76, 0.12) inset,
          0px 55px 15px 0px rgba(120, 136, 155, 0.00),
          0px 35px 14px 0px rgba(120, 136, 155, 0.01),
          0px 20px 12px 0px rgba(120, 136, 155, 0.04),
          0px 9px 9px 0px rgba(120, 136, 155, 0.07),
          0px 2px 5px 0px rgba(120, 136, 155, 0.08);`,
        focus: '0 0 8px 0 #1FBCFF',
      },
      backgroundImage: {
        'dialog-pattern':
          "url('/assets/background-graphic/Dialog-background-pattern.png')",
      },
    },
  },
  plugins: [],
} satisfies Config;
