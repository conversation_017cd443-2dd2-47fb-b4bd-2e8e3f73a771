#!/bin/sh

# Function to parse YAML and extract values
parse_yaml() {
    local yaml_file="$1"
    local key="$2"

    if [ -f "$yaml_file" ]; then
        # Extract value for the given key from YAML file
        # This handles simple key: value pairs
        grep "^${key}:" "$yaml_file" | sed "s/^${key}:[[:space:]]*//" | tr -d '"'"'"
    fi
}

# Path to environment.yaml file
ENV_YAML="/usr/share/nginx/html/environment/environment.yaml"

# Read values from environment.yaml if file exists, otherwise use environment variables or defaults
if [ -f "$ENV_YAML" ]; then
    echo "Reading configuration from $ENV_YAML"

    # Extract values from YAML file
    YAML_KEYCLOAK_URL=$(parse_yaml "$ENV_YAML" "KEYCLOAK_URL")
    YAML_API_URL=$(parse_yaml "$ENV_YAML" "API_URL")

    # Use YAML values if they exist, otherwise fall back to environment variables or defaults
    export KEYCLOAK_URL=${YAML_KEYCLOAK_URL:-${KEYCLOAK_URL:-"https://digifischdok-iam.dsecurecloud.de:8443"}}
    export API_URL=${YAML_API_URL:-${API_URL:-"/api"}}

    echo "Using KEYCLOAK_URL: $KEYCLOAK_URL"
    echo "Using API_URL: $API_URL"
else
    echo "Environment YAML file not found at $ENV_YAML, using environment variables or defaults"

    # Set default values if environment variables are not provided
    export KEYCLOAK_URL=${KEYCLOAK_URL:-"https://digifischdok-iam.dsecurecloud.de:8443"}
    export API_URL=${API_URL:-"/api"}
fi

# Substitute environment variables in nginx config
envsubst '${KEYCLOAK_URL} ${API_URL}' < /etc/nginx/conf.d/default.conf.template > /etc/nginx/conf.d/default.conf

# Start nginx
exec "$@"
