// ***********************************************
// This example namespace declaration will help
// with Intellisense and code completion in your
// IDE or Text Editor.
// ***********************************************
// declare namespace Cypress {
//   interface Chainable<Subject = any> {
//     customCommand(param: any): typeof customCommand;
//   }
// }
//
// function customCommand(param: any): void {
//   console.warn(param);
// }
//
// NOTE: You can use it like so:
// Cypress.Commands.add('customCommand', customCommand);
//
// ***********************************************
// This example commands.js shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************
//
//
// -- This is a parent command --
// Cypress.Commands.add("login", (email, password) => { ... })
//
//
// -- This is a child command --
// Cypress.Commands.add("drag", { prevSubject: 'element'}, (subject, options) => { ... })
//
//
// -- This is a dual command --
// Cypress.Commands.add("dismiss", { prevSubject: 'optional'}, (subject, options) => { ... })
//
//
// -- This will overwrite an existing command --
// Cypress.Commands.overwrite("visit", (originalFn, url, options) => { ... })
import 'cypress-axe';
import 'allure-cypress';
import * as allure from "allure-js-commons";
import {ContentType} from "allure-js-commons";

Cypress.Commands.add("calculateEndDate", (startDate) => {
  const date = new Date(startDate);
  return cy.wrap(date.toISOString().split('T')[0]);
});

Cypress.Commands.add('dataTestid', (value: string) => cy.get(`[data-testid=${value}]`));

Cypress.Commands.add('getByLabel', (label: string) => {
  cy.log('**getByLabel**');
  cy.contains('label', label)
    .invoke('attr', 'for')
    .then((id) => {
      cy.get(`#${id}`);
    });
});

Cypress.Commands.add('changeDateFormat', (birthdate: string) => {
  const tempDate = birthdate.split('-');
  const formattedDate = `${tempDate[2]}.${tempDate[1]}.${tempDate[0]}`;
  return cy.wrap(formattedDate);
});

Cypress.Commands.add('permanentBanDate', (inputStr,textPlaceholder) => {
  const monthMap = {
    'Jan.': '01', 'Feb.': '02', 'Mär.': '03', 'Apr.': '04',
    'Mai': '05', 'Juni': '06', 'Jul.': '07', 'Aug.': '08',
    'Sep.': '09', 'Oct.': '10', 'Nov.': '11', 'Dec.': '12'
  };
  function formatDate(dateStr) {
    if (!dateStr) return null;
    const [day, monthRaw, year] = dateStr.split(' ');
    const monthKey = monthRaw.replace('.', '');
    const month = monthMap[monthRaw] || monthMap[monthKey];
    if (!month) 
      throw new Error(`Unknown month: ${monthRaw}`);
      return `${year}-${month}-${day}`;
  }
  const regex = new RegExp(`(\\d{2} \\w+\\.? \\d{4}) — (${textPlaceholder})(\\w+)\\s+(.*)`);

  return cy.wrap(null).then(() => {
    const matches = inputStr.match(regex);
    if (!matches) {
      throw new Error('Input string does not match the expected format.');
    }
    const startDate = formatDate(matches[1]);
    const endDate = matches[2];
    const type = matches[3];
    const name = (matches[4] || '').trim();
    return {
      startDate,
      endDate,
      type,
      name
    };
  });
});

Cypress.Commands.add('temporaryBanDate', (inputStr) => {
  const monthMap = {
    'Jan.': '01', 'Feb.': '02', 'Mär.': '03', 'Apr.': '04',
    'Mai': '05', 'Juni': '06', 'Jul.': '07', 'Aug.': '08',
    'Sep.': '09', 'Oct.': '10', 'Nov.': '11', 'Dec.': '12'
  };
  function formatDate(dateStr) {
    if (!dateStr) return null;
    const [day, monthRaw, year] = dateStr.split(' ');
    const monthKey = monthRaw.replace('.', '');
    const month = monthMap[monthRaw] || monthMap[monthKey];
    if (!month) 
      throw new Error(`Unknown month: ${monthRaw}`);
      return `${year}-${month}-${day}`;
  }
  const regex = /(\d{2} \w+\.? \d{4}) — (\d{2} \w+\.? \d{4})(\w+)\s+(.*)/;
  const matches = inputStr.match(regex);
  if (matches) {
    const startDate = formatDate(matches[1]);
    const endDateRaw = matches[2];
    const type = matches[3];
    const name = matches[4].trim();
    const endDate = formatDate(endDateRaw);
    return cy.wrap({
      startDate,
      endDate,
      type,
      name
    });
  } 
  else {
    throw new Error('Input string does not match the expected format.');
  }
});

Cypress.Commands.add('formatLicenseNumber', (liscenceNumber: string) => {
  const formattedNumber = liscenceNumber.replace(/(.{4})(?=.)/g, '$1-');
  return cy.wrap(formattedNumber);
});

Cypress.Commands.add('logAccessibilityViolations', (violations) => {
  let violationName;
  let violationDescription
  let impact;
  let helpUrl;
  let nodes;
  let message;
  let target;
  let html;
  violations.forEach((violation) => {
    violationName = violation.id;
    violationDescription = violation.description;
    impact = violation.impact;
    helpUrl = violation.helpUrl;
    nodes = violation.nodes.map((node) => {
      target = node.target.join(', ');
      html = node.html;
      message = node.failureSummary;
      return `- **Target**: ${target}\n  **HTML**: \`${html}\`\n  **Message**: ${message}`;
    }).join('\n');

    allure.step(`Accessibility issue detected : ${violationName}`, () => {
      allure.attachment(
        'Details of the violation',
        `**Description**: ${violationDescription}
        \n**Impact**: ${impact}
        \n**Affected Elements**:\n${nodes}
        \n**More information**: (${helpUrl})`,
        ContentType.JSON
      );
    });

    cy.task('table',
      {
        impact: impact,
        description: violationDescription,
        AffectedElement: target,
        helpUrl: helpUrl
      }
    );
  });
});


declare global {
  namespace Cypress {
    interface Chainable {

      /**
       * Custom command to get an element by its data-testid attribute.
       * @param {string} value - The value of the data-testid attribute.
       * @returns {Cypress.Chainable} - The element with the given data-testid.
       */
      dataTestid(value: string): Chainable;

      /**
       * Custom command to get an element by its label.
       * @param {string} label - The label text.
       */
      getByLabel(label: string): Chainable;

      /**
       * Change the birthdate format from 'AAAA-MM-JJ' to 'JJ.MM.AAAA'.
       * @param birthdate the birthdate in format 'AAAA-MM-JJ'.
       * @returns the birthdate in format 'JJ.MM.AAAA'.
       */
      changeDateFormat(birthdate: string): Chainable;
      
      /**
       * Split and convert a temporary ban's description found on Serviceoverview.
       * @param inputStr
       * @returns a set of individual values for all parts of the description.
       */
      temporaryBanDate(inputStr: string): Chainable;

      /**
       * Split and convert a permanent ban's description found on Serviceoverview.
       * @param inputStr
       * @param textPlaceholder
       * @returns a set of individual values for all parts of the description.
       */
      permanentBanDate(inputStr: string,textPlaceholder:string): Chainable;

      /**
       * Change the format of the licence number by adding a '-' after 4 characters
       * @param licenceNumber
       * @return the formated licence number '1111-2222-3333'
       */
      formatLicenseNumber(licenceNumber: string): Chainable;

      /**
       * Log accessibility violations to Allure report.
       * with the details of the broken rule and infos how to fix it
       * @param violations - Array of accessibility violations.
       */
      logAccessibilityViolations(violations: any): Chainable;

      /**
       * Clears all cookies, including HTTP-only cookies.
       * @see https://docs.cypress.io/api/commands/clearallcookies
       * @returns Chainable<null>
       */
      clearAllCookies(): Chainable;

      /**
       * Calculates the expected end date by adding days to a start date.         
       * @param startDate - The start date in "YYYY-MM-DD" format.
       * @param numberOfDays - Number of days to add.
       * @returns The expected end date in "YYYY-MM-DD" format.
      */
      calculateEndDate(startDate: string, numberOfDays: number): Chainable<string>;
    }
  }
}

export {};
