export class QualificationDataCo{
    //Selectors for qualification proof page
  private readonly QUALIFICATION_TAB_GROUP: string = '[data-testid="digitize-tab-qualification-proof"]';
  private readonly QUALIFICATION_VALID_FROM: string = '[data-testid="licence-form-valid-from"]';
  private readonly QUALIFICATION_LEGACY_NUMBER: string = '[data-testid="licence-form-legacy-number"]';
  private readonly QUALIFICATION_LICENCE_ISSUED_BY:string = '[data-testid="licence-form-issued-by"]';
  private readonly QUALIFICATION_FEDERAL_STATE:string = '[data-testid="licence-form-federal-state"]'
  private readonly QUALIFICATION_LINK_BOX:string = '[data-testid="qualification-proof-linkbox"]';
  private readonly QUALIFICATION_LINK_BOX_TEMPLATE:string = '[data-testid="qualification-proof-help-template"]';
  private readonly QUALIFICATION_LINK_BOX_ANTIFORGERY:string = '[data-testid="qualification-proof-help-antiforgery"]';
  private readonly QUALIFICATION_TOGGLE_BOX:string = '[data-testid="qualification-proof-toggle-box"]';
  private readonly QUALIFICATION_TOGGLE_BOX_LICENCE:string = '[data-testid="qualification-proof-toggle-box-licence-card"]';
  private readonly QUALIFICATION_TOGGLE_BOX_CERTIFICATE:string = '[data-testid="qualification-proof-toggle-box-certificate"]';
  private readonly QUALIFICATION_TOGGLE_BOX_OTHER:string = '[data-testid="qualification-proof-toggle-box-other-proof"]';
  private readonly INPUT_FIELD: string = '[data-testid="input"]';
  private readonly COMBOBOX_LIST_BOX: string = '[data-testid="combobox-listbox"]';
  private readonly COMBOBOX_LIST_OPTION: string = '[data-testid="combobox-option"]';
  private readonly CARD_COMPONENT: string = '[data-testid="card-component"]';
  private readonly CHECKBOX: string = '[data-testid="checkbox"]';
  private readonly RADIO_BUTTON: string = '[data-testid="radio"]';
  private readonly PERMANENT_BAN: string = '[data-testid="permanent-ban-period-radio-item"]';


  public getQualificationPage(): Cypress.Chainable{
    return cy.get(this.QUALIFICATION_TAB_GROUP)
  }

  public getQualificationValidFrom(): Cypress.Chainable{
    return this.getQualificationPage().find(this.QUALIFICATION_VALID_FROM)
  }

  public getQualificationLegacyNumber(): Cypress.Chainable{
    return this.getQualificationPage().find(this.QUALIFICATION_LEGACY_NUMBER)
  }

  public getQualificationLicenceIssuer(): Cypress.Chainable{
    return this.getQualificationPage().find(this.QUALIFICATION_LICENCE_ISSUED_BY)
  }

  public getQualificationFederalState(): Cypress.Chainable{
    return this.getQualificationPage().find(this.QUALIFICATION_FEDERAL_STATE)
  }

  public getQualificationFederalStateAllOptions(): Cypress.Chainable {
    return this.getQualificationFederalState()
      .find(this.INPUT_FIELD).click().get(this.COMBOBOX_LIST_BOX)
      .find(this.COMBOBOX_LIST_OPTION)
  }

  public getQualificationLinkBox():Cypress.Chainable{
    return this.getQualificationPage().find(this.QUALIFICATION_LINK_BOX)
  }

  public getQualificationTemplate():Cypress.Chainable{
    return this.getQualificationLinkBox().find(this.QUALIFICATION_LINK_BOX_TEMPLATE)
  }

  public getQualificationAntiForgery():Cypress.Chainable{
    return this.getQualificationLinkBox().find(this.QUALIFICATION_LINK_BOX_ANTIFORGERY)
  }

  public getQualificationToggleBox():Cypress.Chainable{
    return this.getQualificationPage().find(this.QUALIFICATION_TOGGLE_BOX)
  }

  public getQualificationToggleLicence():Cypress.Chainable{
    return this.getQualificationPage().find(this.QUALIFICATION_TOGGLE_BOX_LICENCE)
  }

  public getQualificationToggleCertificate():Cypress.Chainable{
    return this.getQualificationPage().find(this.QUALIFICATION_TOGGLE_BOX_CERTIFICATE)
  }

  public getQualificationToggleOther():Cypress.Chainable{
    return this.getQualificationPage().find(this.QUALIFICATION_TOGGLE_BOX_OTHER)
  }

  public getDisablementCertificationCard():Cypress.Chainable{
    return cy.get(this.CARD_COMPONENT)
  }

  public getDisablementCertificationCheckbox():Cypress.Chainable{
    return this.getDisablementCertificationCard().find(this.CHECKBOX)
  }

  public getDisablementCertificationCheckboxInput():Cypress.Chainable{
    return this.getDisablementCertificationCard().find(this.CHECKBOX).find('input[type="checkbox"]')
  }

  public getPermanentDisablement():Cypress.Chainable{
    return this.getDisablementCertificationCard().find(this.PERMANENT_BAN)
  }

  public getPermanentDisablementRadio():Cypress.Chainable{
    return this.getPermanentDisablement().find(this.RADIO_BUTTON)
  }
}