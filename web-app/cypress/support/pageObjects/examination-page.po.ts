
export class ExaminationPagePo {
  private readonly EXAM_PAGE: string = '[data-testid="create-passed-exam-page"]';
  private readonly EXAM_PAGE_ICON: string = '[data-testid="exam-page-certificate-icon"]';
  private readonly EXAM_PAGE_TITLE: string = '[data-testid="exam-page-title"]';
  private readonly EXAM_PAGE_DESCRIPTION: string = '[data-testid="exam-page-description"]';
  private readonly EXAM_PAGE_FORM:string = '[data-testid="exam-page-form"]';
  private readonly EXAM_PAGE_FORM_TITLE:string = '[data-testid="exam-page-field-title"]';
  private readonly EXAM_PAGE_FORM_LAST_NAME: string = '[data-testid="exam-page-field-lastname"]';
  private readonly EXAM_PAGE_FORM_FIRST_NAME: string = '[data-testid="exam-page-field-firstname"]';
  private readonly EXAM_PAGE_FORM_BIRTHNAME: string = '[data-testid="exam-page-field-birthname"]';
  private readonly EXAM_PAGE_FORM_BIRTHDATE: string = '[data-testid="exam-page-field-birthdate"]';
  private readonly EXAM_PAGE_FORM_BIRTHPLACE: string = '[data-testid="exam-page-field-birthplace"]';
  private readonly EXAM_PAGE_FORM_PASS_DATE: string = '[data-testid="exam-page-field-passedOn"]';
  private readonly EXAM_PAGE_CONTINUE_BUTTON: string = '[data-testid="exam-page-continue-button"]';
  private readonly EXAM_CONFIRMATION_PAGE: string = '[data-testid="create-passed-exam-page"]';
  private readonly EXAM_CONF_PAGE_TITLE: string = '[data-testid="exam-page-title"]';
  private readonly EXAM_CONFI_PAGE_ICON: string = '[data-testid="exam-page-certificate-icon"]';
  private readonly EXAM_CONF_PAGE_DESC: string = '[data-testid="exam-page-description"]';
  private readonly EXAM_CONF_PAGE_SUMMARY: string = '[data-testid="exam-page-summary"]'
  private readonly INPUT_FIELD: string = '[data-testid="input"]';
  private readonly EXAM_CONF_SEND_BUTTON: string = '[data-testid="exam-summary-send-button"]';
  private readonly EXAM_CONF_EDIT_BUTTON: string = '[data-testid="exam-summary-edit-button"]';
  private readonly EXAM_SUCCESS_PAGE: string = '[data-testid="exam-created"]';
  private readonly EXAM_SUCCESS_TABLE: string = '[data-testid="exam-created-exam-info"]';
  private readonly EXAM_SUCCESS_PRINT_BUTTON: string = '[data-test-id="exam-created-print-button"]';
  private readonly EXAM_SUCCESS_NEW_ENTRY_BUTTON: string = '[data-test-id="exam-created-create-button"]';

  public getExaminationPage(): Cypress.Chainable {
    return cy.get(this.EXAM_PAGE);
  }

  public getExamPageIcon(): Cypress.Chainable{
    return this.getExaminationPage().find(this.EXAM_PAGE_ICON)
  }
  
  public getExamPageTitle(): Cypress.Chainable{
    return this.getExaminationPage().find(this.EXAM_PAGE_TITLE)
  }

  public getExamPageDesc(): Cypress.Chainable{
    return this.getExaminationPage().find(this.EXAM_PAGE_DESCRIPTION)
  }

  public getExamPageForm(): Cypress.Chainable{
    return this.getExaminationPage().find(this.EXAM_PAGE_FORM)
  }

  public getExamPageFormTitle(): Cypress.Chainable{
    return this.getExamPageForm().find(this.EXAM_PAGE_FORM_TITLE)
  }

  public getExamPageFormFirstname(): Cypress.Chainable{
    return this.getExamPageForm().find(this.EXAM_PAGE_FORM_FIRST_NAME)
  }

  public getExamPageFormFirstnameInput(): Cypress.Chainable{
    return this.getExamPageFormFirstname().find(this.INPUT_FIELD)
  }

  public getExamPageFormLastname(): Cypress.Chainable{
    return this.getExamPageForm().find(this.EXAM_PAGE_FORM_LAST_NAME)
  }

  public getExamPageFormLastnameInput(): Cypress.Chainable{
    return this.getExamPageFormLastname().find(this.INPUT_FIELD)
  }

  public getExamPageFormBirthname(): Cypress.Chainable{
    return this.getExamPageForm().find(this.EXAM_PAGE_FORM_BIRTHNAME)
  }

  public getExamPageFormBirthnameInput(): Cypress.Chainable{
    return this.getExamPageFormBirthname().find(this.INPUT_FIELD)
  }

  public getExamPageFormBirthdate(): Cypress.Chainable{
    return this.getExamPageForm().find(this.EXAM_PAGE_FORM_BIRTHDATE)
  }

  public getExamPageFormBirthdateInput(): Cypress.Chainable{
    return this.getExamPageFormBirthdate().find(this.INPUT_FIELD)
  }

  public getExamPageFormBirthplace(): Cypress.Chainable{
    return this.getExamPageForm().find(this.EXAM_PAGE_FORM_BIRTHPLACE)
  }

  public getExamPageFormBirthplaceInput(): Cypress.Chainable{
    return this.getExamPageFormBirthplace().find(this.INPUT_FIELD)
  }

  public getExamPageFormPassDate(): Cypress.Chainable{
    return this.getExamPageForm().find(this.EXAM_PAGE_FORM_PASS_DATE)
  }

  public getExamPageFormPassDateInput(): Cypress.Chainable{
    return this.getExamPageFormPassDate().find(this.INPUT_FIELD)
  }

  public getExamPageContinueButton(): Cypress.Chainable{
    return cy.get(this.EXAM_PAGE_CONTINUE_BUTTON)
  }

  public getExamConfirmationPage(): Cypress.Chainable{
    return cy.get(this.EXAM_CONFIRMATION_PAGE,{timeout:10000})
  }

  public getExamConfirmationPageIcon(): Cypress.Chainable{
    return this.getExamConfirmationPage().find(this.EXAM_CONFI_PAGE_ICON,{timeout:10000})
  }

  public getExamConfirmationPageTitle(): Cypress.Chainable{
    return this.getExamConfirmationPage().find(this.EXAM_CONF_PAGE_TITLE,{timeout:10000})
  }

  public getExamConfirmationPageDesc(): Cypress.Chainable{
    return this.getExamConfirmationPage().find(this.EXAM_CONF_PAGE_DESC,{timeout:10000})
  }

  public getExamSummary(): Cypress.Chainable{
    return this.getExamConfirmationPage().find(this.EXAM_CONF_PAGE_SUMMARY,{timeout:10000})
  }

  public getExamEditButton(): Cypress.Chainable{
    return cy.get(this.EXAM_CONF_EDIT_BUTTON,{timeout:10000})
  }

  public getExamSubmitButton(): Cypress.Chainable{
    return cy.get(this.EXAM_CONF_SEND_BUTTON,{timeout:10000})
  }

  public getSuccessPage(): Cypress.Chainable{
    return cy.get(this.EXAM_SUCCESS_PAGE)
  }

  public getSuccessEntries(): Cypress.Chainable{
    return this.getSuccessPage().find(this.EXAM_SUCCESS_TABLE)
  }

  public getPrintButton(): Cypress.Chainable{
    return this.getSuccessPage().find(this.EXAM_SUCCESS_PRINT_BUTTON)
  }

  public getNewEntryButton(): Cypress.Chainable{
    return this.getSuccessPage().find(this.EXAM_SUCCESS_NEW_ENTRY_BUTTON)
  }

}
