export class EditFooterComponentCo {
  private readonly EDIT_FOOTER_COMPONENT: string = '[data-testid="edit-footer-component"]';
  private readonly FOOTER_CONFIRM_BOX: string = '[data-testid="edit-footer-confirm-box"]';
  private readonly FOOTER_CONTINUE_BUTTON: string = '[data-testid="edit-footer-continue-button"]';
  private readonly FOOTER_CONTINUE_ICON: string = '[data-testid="edit-footer-continue-icon"]';
  private readonly CHECKBOX: string = '[data-testid="checkbox"]';
  private readonly FOOTER_BACK_BUTTON: string = '[data-testid="edit-footer-back-button"]';
  private readonly FOOTER_BACK_ICON: string = '[data-testid="edit-footer-back-icon"]';
  private readonly FOOTER_FINISH_BUTTON: string = '[data-testid="edit-footer-finnish-button"]';
  private readonly FOOTER_FINISH_ICON: string = '[data-testid="edit-footer-finnish-icon"]';
  private readonly FOOTER_DELETE_USER_BUTTON: string = '[data-testid="delete-button"]';
  private readonly FOOTER_BAN_USER_BUTTON: string = '[data-testid="ban-person-button"]';

  public getEditFooterComponent(): Cypress.Chainable {
    return cy.get(this.EDIT_FOOTER_COMPONENT);
  }

  public getFooterConfirmBox(): Cypress.Chainable {
    return this.getEditFooterComponent().find(this.FOOTER_CONFIRM_BOX);
  }

  public getFooterContinueButton(): Cypress.Chainable {
    return this.getEditFooterComponent().find(this.FOOTER_CONTINUE_BUTTON);
  }

  public getFooterContinueIcon(): Cypress.Chainable {
    return this.getFooterContinueButton().find(this.FOOTER_CONTINUE_ICON);
  }

  public getFooterConfirmBoxCheckbox(): Cypress.Chainable {
    return this.getFooterConfirmBox().find(this.CHECKBOX);
  }

  public getFooterBackButton(): Cypress.Chainable {
    return this.getEditFooterComponent().find(this.FOOTER_BACK_BUTTON);
  }

  public getFooterBackIcon(): Cypress.Chainable {
    return this.getFooterBackButton().find(this.FOOTER_BACK_ICON);
  }

  public getFooterFinishButton(): Cypress.Chainable {
    return this.getEditFooterComponent().find(this.FOOTER_FINISH_BUTTON);
  }

  public getFooterFinishIcon(): Cypress.Chainable {
    return this.getFooterFinishButton().find(this.FOOTER_FINISH_ICON);
  }

  public getFooterDeleteButton(): Cypress.Chainable {
    return cy.get(this.FOOTER_DELETE_USER_BUTTON)
  }

  public getFooterBanButton(): Cypress.Chainable {
    return cy.get(this.FOOTER_BAN_USER_BUTTON)
  }
}
