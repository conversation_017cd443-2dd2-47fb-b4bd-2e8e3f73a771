export class SearchBarInputCo {
  private readonly SEARCH_BAR_COMPONENT: string = '[data-testid="search-bar-input"]';
  private readonly SEARCH_BAR_COMPONENT_INPUT: string = 'input';
  private readonly SEARCH_BAR_COMPONENT_CLEAR_BUTTON: string = '[data-testid="do-clear-button"]';
  private readonly SEARCH_BAR_COMPONENT_SEARCH_BUTTON: string = '[data-testid="do-search-button"]';

  public getSearchbarComponent(): Cypress.Chainable {
    return cy.get(this.SEARCH_BAR_COMPONENT);
  }

  public getSearchbarComponentInput(): Cypress.Chainable {
    return this.getSearchbarComponent().find(this.SEARCH_BAR_COMPONENT_INPUT);
  }

  public getSearchbarComponentClearButton(): Cypress.Chainable {
    return cy.get(this.SEARCH_BAR_COMPONENT_CLEAR_BUTTON);
  }

  public getSearchbarComponentSearchButton(): Cypress.Chainable {
    return cy.get(this.SEARCH_BAR_COMPONENT_SEARCH_BUTTON);
  }
}
