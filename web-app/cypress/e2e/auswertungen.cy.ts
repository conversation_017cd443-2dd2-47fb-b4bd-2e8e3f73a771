import * as allure from "allure-js-commons";
import { HeaderComponentCo } from "@/support/sharedComponents/header-component.co";
import { ExternalLoginPagePo } from "../support/pageObjects/external-login-page.po";
import { LoginPagePo } from "../support/pageObjects/login-page.po";
import { StatisticsPo } from "@/support/pageObjects/statistics.po";
import { apiRequests } from "@/support/API/requests";

describe("Auswertungen",()=>{
  const headerComponent:HeaderComponentCo = new HeaderComponentCo()
  const loginPage:LoginPagePo = new LoginPagePo()
  const externalLoginPage:ExternalLoginPagePo = new ExternalLoginPagePo()
  const statisticsComponent:StatisticsPo = new StatisticsPo()
  const apiRequest: apiRequests = new apiRequests()
  let access_token;
  let testUsers: any;
  let testData: any;
  let testCitizen: any;
  let apiURL: any;
  const system = Cypress.env('system');
  
  before(() => {
    cy.clearAllCookies();
    allure.feature('Fischereischeinabgabe Anlage');
    allure.owner('Hegedus Kinga');
    
    cy.fixture(`${system}/suche/testuser.json`).then((users) => {
      cy.log('reading the test users file');
      testUsers = users;
      cy.log(`run Tests using test users on: ${system}`);
    });
    
    cy.fixture(`${system}/suche/testdata.json`).then((data) => {
      cy.log('reading the test data file');
      testData = data;
      cy.log(`run Tests using test users on: ${system}`);
      cy.log(JSON.stringify(testData));
    });
    
    cy.fixture(`${system}/suche/testcitizen.json`).then((citizen) => {
      cy.log('reading the test data file');
      testCitizen = citizen;
      cy.log(`run Tests using test citizens on: ${system}`);
      cy.log(JSON.stringify(testCitizen));
    });

    cy.fixture(`${system}/suche/api.json`).then((URL) => {
      cy.log('reading the test data file');
      apiURL = URL;
      cy.log(`run Tests using URLs on: ${system}`);
      cy.log(JSON.stringify(apiURL));
    });

    apiRequest.getToken().then((response) => {
      expect(response.status).to.eq(200);
      expect(response.body).to.have.property('access_token');
      access_token = response.body.access_token
    });

    allure.step("Mitarbeiter der oberen Fischereibehörde NRW anmelden",()=>{
      allure.parameter("username", testUsers.user7.username);
      allure.parameter("userPassword", '');
      cy.visit('/')
      loginPage.getLoginButton().click();
      externalLoginPage.getLoginPageUsernameInput().type(testUsers.user7.username);
      externalLoginPage.getLoginPagePasswordInput().type(testUsers.user7.password, {log: false});
      externalLoginPage.getLoginPageSubmitButton().click();
    })
  })

  it('Überprüfung des Dashboards "Auswertungen-Übersicht": Typ=Default, Jahr=Default, Bundesland=Default',()=>{
    allure.step("Überprüfung aller Elemente aus dem Header",()=>{
      headerComponent.getHeaderComponent().should('be.visible')
      headerComponent.getHeaderLogo().should('be.visible')
      headerComponent.getHeaderStateLogo().should('have.attr','alt','Bundesland Logo von Nordrhein-Westfalen')
      headerComponent.getHeaderUserInfo().should('be.visible')
      headerComponent.getHeaderUserInfoOffice().contains('Obere Fischereischeinbehörde Nordrhein-Westfalen')
      headerComponent.getHeaderUserInfoUsername().contains('Martin Mütze')
      headerComponent.getRequestsButton().should('be.visible')
      headerComponent.getStatisticsButton().should('be.visible')
    })

    allure.step('Überprüfung des Dashboards "Auswertungen-Übersicht"',()=>{
      headerComponent.getStatisticsButton().click()
      statisticsComponent.getStatisticsOptions().should('be.visible')
      statisticsComponent.getStatisticsOptionsSlider().should('be.visible')
      statisticsComponent.getStatisticsDashbboard().should('be.visible')
      statisticsComponent.getStatisticsFilterType().should('be.visible')
      statisticsComponent.getStatisticsFilterYear().should('be.visible')
      statisticsComponent.getStatisticsFilterState().should('be.visible')
    })

    allure.step('Überprüfung des Bereischs "Fischereiabgaben"',()=>{
      statisticsComponent.getTaxesOverview().should('be.visible')
      statisticsComponent.getTaxesOverviewTitle().should('be.visible').contains("Fischereiabgaben")
      statisticsComponent.getTaxesOverviewSubtitle().should('be.visible').contains("Alle Daten")
      statisticsComponent.getOverviewPrimaryLabels().eq(0).should('be.visible').contains("2025")
      statisticsComponent.getOverviewPrimaryDescriptions().eq(0).should('be.visible').contains("Abgaben & Gebühren")
      statisticsComponent.getOverviewPrimaryDescriptions().eq(0).contains("inkl. U/A-Fischereischeine")
      statisticsComponent.getOverviewSecondaryLabels().eq(0).should('be.visible').contains("2024")
      statisticsComponent.getOverviewTertiaryLabels().eq(0).should('be.visible').contains("Gültig für 1 Jahr")
      statisticsComponent.getOverviewTertiaryLabels().eq(1).should('be.visible').contains("Gültig für mehrere Jahre")
      statisticsComponent.getSelectedYearOverview().eq(0).should('be.visible').contains("2025")
      statisticsComponent.getPreviousYearOverview().eq(0).should('be.visible').contains("2024")
      //TODO:add request and assert
      statisticsComponent.getOverviewPrimaryValues().eq(0).should('be.visible')
      statisticsComponent.getOverviewSecondaryValues().eq(0).should('be.visible')
      statisticsComponent.getOverviewTertiaryValues().eq(0).should('be.visible')
    })

    allure.step('Überprüfung des Bereischs "Fischereischeine"',()=>{
      statisticsComponent.getLicencesOverview().should('be.visible')
      statisticsComponent.getLicencesOverviewTitle().should('be.visible').contains("Fischereischeine")
      statisticsComponent.getLicencesOverviewSubtitle().should('be.visible').contains("Alle Daten")
      statisticsComponent.getOverviewPrimaryLabels().eq(1).should('be.visible').contains("2025")
      statisticsComponent.getOverviewSecondaryLabels().eq(1).should('be.visible').contains("2024")
      statisticsComponent.getSelectedYearOverview().eq(1).should('be.visible').contains("2025")
      statisticsComponent.getPreviousYearOverview().eq(1).should('be.visible').contains("2024")
      //TODO: add requests and asserts
      statisticsComponent.getOverviewPrimaryValues().eq(1).should('be.visible')
      statisticsComponent.getOverviewSecondaryValues().eq(1).should('be.visible')
    })

    allure.step('Überprüfung des Bereischs "Urlauber-/Ausländerfischereischeine"',()=>{
      statisticsComponent.getVacationLicencesOverview().should('be.visible')
      statisticsComponent.getVacationLicencesOverviewTitle().should('be.visible').contains("Urlauber- / Ausländerfischereischeine")
      statisticsComponent.getVacationLicencesOverviewSubtitle().should('be.visible').contains("Alle Daten")
      statisticsComponent.getOverviewPrimaryLabels().eq(2).should('be.visible').contains("2025")
      statisticsComponent.getOverviewSecondaryLabels().eq(2).should('be.visible').contains("2024")
      statisticsComponent.getSelectedYearOverview().eq(2).should('be.visible').contains("2025")
      statisticsComponent.getPreviousYearOverview().eq(2).should('be.visible').contains("2024")
      //TODO: add requests and asserts
      statisticsComponent.getOverviewPrimaryValues().eq(2).should('be.visible')
      statisticsComponent.getOverviewSecondaryValues().eq(2).should('be.visible')
    })
    
    allure.step('Überprüfung des Bereischs "Sonderfischereischeine"',()=>{
      statisticsComponent.getLimitedLicencesOverview().should('be.visible').scrollIntoView()
      statisticsComponent.getLimitedLicencesOverviewTitle().should('be.visible').contains("Sonderfischereischeine")
      statisticsComponent.getLimitedLicencesOverviewSubtitle().should('be.visible').contains("Alle Daten")
      statisticsComponent.getOverviewPrimaryLabels().eq(3).should('be.visible').contains("2025")
      statisticsComponent.getOverviewSecondaryLabels().eq(3).should('be.visible').contains("2024")
      statisticsComponent.getSelectedYearOverview().eq(3).should('be.visible').contains("2025")
      statisticsComponent.getPreviousYearOverview().eq(3).should('be.visible').contains("2024")
      //TODO:add requests and asserts
      statisticsComponent.getOverviewPrimaryValues().eq(3).should('be.visible')
      statisticsComponent.getOverviewSecondaryValues().eq(3).should('be.visible')
    })

    allure.step('Überprüfung des Bereischs "Prüfungen"',()=>{
      statisticsComponent.getCertificationsOverview().should('be.visible')
      statisticsComponent.getCertificationLicencesOverviewTitle().should('be.visible').contains("Prüfungen")
      statisticsComponent.getCertificationLicencesOverviewSubtitle().should('be.visible').contains("Alle Daten")
      statisticsComponent.getOverviewPrimaryLabels().eq(4).should('be.visible').contains("2025")
      statisticsComponent.getOverviewSecondaryLabels().eq(4).should('be.visible').contains("2024")
      statisticsComponent.getCertificationsSelectedYear().should('be.visible').contains("2025")
      statisticsComponent.getCertificationsPreviousYearOverview().should('be.visible').contains("2024")
      //TODO:add requests and asserts
      statisticsComponent.getOverviewPrimaryValues().eq(4).should('be.visible')
      statisticsComponent.getOverviewSecondaryValues().eq(4).should('be.visible')
    })

    allure.step('Überprüfung des Bereischs "Kontrollen"',()=>{
      statisticsComponent.getInspectionOverview().should('be.visible')
      statisticsComponent.getInspectionOverviewTitle().should('be.visible').contains("Kontrollen")
      statisticsComponent.getInspectionOverviewSubtitle().should('be.visible').contains("Alle Daten")
      statisticsComponent.getOverviewPrimaryLabels().eq(5).should('be.visible').contains("2025")
      statisticsComponent.getOverviewSecondaryLabels().eq(5).should('be.visible').contains("2024")
      //TODO:add requests and asserts
      statisticsComponent.getOverviewSecondaryValues().eq(5).should('be.visible')
      statisticsComponent.getOverviewPrimaryValues().eq(5).should('be.visible')
    })

    allure.step('Überprüfung des Bereischs "Sperren"',()=>{
      statisticsComponent.getBanOverview().should('be.visible')
      statisticsComponent.getBanOverviewTitle().should('be.visible').contains("Sperren")
      statisticsComponent.getBanOverviewSubtitle().should('be.visible').contains("Alle Daten")
      statisticsComponent.getOverviewPrimaryLabels().eq(6).scrollIntoView().should('be.visible').contains("2025")
      statisticsComponent.getCertificationsSelectedYear().should('be.visible').contains("2025")
      statisticsComponent.getCertificationsPreviousYearOverview().should('be.visible').contains("2024")
      //TODO:add requests and asserts
      statisticsComponent.getOverviewPrimaryValues().eq(6).should('be.visible')
    })
  })

  it('Überprüfung des Dashboards "Auswertungen-Übersicht": Typ=Fischereiabgaben & Fischereischeine, Jahr=2025, Bundesland=SH, Fischereischeinbehörde=Default',()=>{
    const title1 = "Fischereiabgaben";
    const title2 = "Fischereischeine";
    const title3 = "Urlauber- / Ausländerfischereischeine";
    const title4 = "Sonderfischereischeine";
    const subtitle = "Alle Daten";
    const selectedYear = 2025;
    const previousYear = selectedYear - 1;
    const state = "SH";
    const office = "Fischereibehörde Kiel";
    const taxesURL = apiURL.URLs.baseURL + apiURL.URLs.statisticsTaxesURL
    const regularLicencesURL = apiURL.URLs.baseURL + apiURL.URLs.statisticsRegularLicencesURL
    const specialLicencesURL = apiURL.URLs.baseURL + apiURL.URLs.statisticsSpecialLicencesURL
    const limitedLicencesURL = apiURL.URLs.baseURL + apiURL.URLs.statisticsLimitedLicencesURL

    allure.step('Wähle "Fischereischein & Abagaben" aus der Filter "Typ"',()=>{
      statisticsComponent.getStatisticsFilterType().should('be.visible').click()
      statisticsComponent.getStatisticsFilterLists().should('be.visible')
      statisticsComponent.getStatisticsFilterListItems().eq(1).should('be.visible').click()
    })

    allure.step('Wähle "2025" aus der Filter "Jahr"',()=>{
      statisticsComponent.getStatisticsFilterYear().should('be.visible').click()
      statisticsComponent.getStatisticsFilterListItems().eq(0).should('be.visible').click()
    })

    allure.step('Wähle "SH" aus der Filter "Bundesland"',()=>{
      statisticsComponent.getStatisticsFilterState().should('be.visible').click()
      statisticsComponent.getStatisticsFilterListItems().eq(14).scrollIntoView().should('be.visible').click()
    })

    allure.step('Wähle "Fischereibehörde Kiel" aus der Filter "Fischereibehörde"',()=>{
      statisticsComponent.getStatisticsFilterOffice().should('be.visible').click()
      statisticsComponent.getStatisticsFilterListItems().eq(9).scrollIntoView().should('be.visible').click()
    })

    allure.step('Überprüfung des Bereischs "Fischereiabgaben"',()=>{
      statisticsComponent.getTaxesOverview().should('be.visible')
      statisticsComponent.getTaxesOverviewTitle().scrollIntoView().should('be.visible').contains(title1)
      statisticsComponent.getTaxesOverviewSubtitle().should('be.visible').contains(subtitle)
      statisticsComponent.getOverviewPrimaryLabels().eq(0).should('be.visible').contains(selectedYear)
      statisticsComponent.getOverviewPrimaryDescriptions().eq(0).should('be.visible').contains("Abgaben & Gebühren")
      statisticsComponent.getOverviewPrimaryDescriptions().eq(0).contains("inkl. U/A-Fischereischeine")
      statisticsComponent.getOverviewSecondaryLabels().eq(0).should('be.visible').contains(previousYear)
      statisticsComponent.getOverviewTertiaryLabels().eq(0).should('be.visible').contains("Gültig für 1 Jahr")
      statisticsComponent.getOverviewTertiaryLabels().eq(1).should('be.visible').contains("Gültig für mehrere Jahre")
      statisticsComponent.getSelectedYearOverview().eq(0).should('be.visible').contains(selectedYear)
      statisticsComponent.getPreviousYearOverview().eq(0).should('be.visible').contains(previousYear)
      apiRequest.getStatisticsByYearStateAndOffice(access_token,taxesURL,selectedYear,state,office).then((response) => {
        expect(response.status).to.eq(200)
        const responseBody = response.body
        const entries = responseBody[0].data
        const sum = entries.filter(item=>item.submissionType==='ANALOG')
                            .reduce((sum,value)=> sum + value.revenue, 0)
        statisticsComponent.getOverviewPrimaryValues().eq(0).should('be.visible').invoke('text').then((text)=>{
          const uiValue = parseInt(text.replace(/[^\d]/g, ''), 10)
          expect(uiValue).to.eq(sum)
        })
      });
      apiRequest.getStatisticsByYearStateAndOffice(access_token,taxesURL,previousYear,state,office).then((response) => {
        expect(response.status).to.eq(200)
        const responseBody = response.body
        const entries = responseBody[0].data
        const sum = entries.filter(item=>item.submissionType==='ANALOG')
                            .reduce((sum,value)=> sum + value.revenue, 0)
        statisticsComponent.getOverviewSecondaryValues().eq(0).should('be.visible').invoke('text').then((text)=>{
          const uiValue = parseInt(text.replace(/[^\d]/g, ''), 10)
          expect(uiValue).to.eq(sum)
        })
      });
      apiRequest.getStatisticsByYearStateAndOffice(access_token,taxesURL,selectedYear,state,office).then((response) => {
        expect(response.status).to.eq(200)
        const responseBody = response.body
        const entries = responseBody[0].data
        const sum = entries.filter(item=>item.submissionType==='ANALOG' && item.duration===1)
                            .reduce((sum,value) => sum + value.count, 0)
        statisticsComponent.getOverviewTertiaryValues().eq(0).should('be.visible').invoke('text').then((text)=>{
          const uiValue = parseInt(text.replace(/[^\d]/g, ''), 10)
          expect(uiValue).to.eq(sum)
        })
      });
    })

    allure.step('Überprüfung des Bereischs "Fischereischeine"',()=>{
      statisticsComponent.getLicencesOverview().should('be.visible')
      statisticsComponent.getLicencesOverviewTitle().should('be.visible').contains(title2)
      statisticsComponent.getLicencesOverviewSubtitle().should('be.visible').contains(subtitle)
      statisticsComponent.getOverviewPrimaryLabels().eq(1).should('be.visible').contains(selectedYear)
      statisticsComponent.getOverviewSecondaryLabels().eq(1).should('be.visible').contains(previousYear)
      statisticsComponent.getSelectedYearOverview().eq(1).should('be.visible').contains(selectedYear)
      statisticsComponent.getPreviousYearOverview().eq(1).should('be.visible').contains(previousYear)
      apiRequest.getStatisticsByYearStateAndOffice(access_token,regularLicencesURL,selectedYear,state,office).then((response) => {
        expect(response.status).to.eq(200)
        const responseBody = response.body
        const entries = responseBody[0].data
        const sum = entries.filter(item=>item.submissionType==='ANALOG')
                            .reduce((sum,value)=> sum + value.count, 0)
        statisticsComponent.getOverviewPrimaryValues().eq(1).should('be.visible').invoke('text').then((text)=>{
          const uiValue = parseInt(text.replace(/[^\d]/g, ''), 10)
          expect(uiValue).to.eq(sum)
        })
      });
      apiRequest.getStatisticsByYearStateAndOffice(access_token,regularLicencesURL,previousYear,state,office).then((response) => {
        expect(response.status).to.eq(200)
        const responseBody = response.body
        const entries = responseBody[0].data
        const sum = entries.filter(item=>item.submissionType==='ANALOG')
                            .reduce((sum,value)=> sum + value.count, 0)
        statisticsComponent.getOverviewSecondaryValues().eq(1).should('be.visible').invoke('text').then((text)=>{
          const uiValue = parseInt(text.replace(/[^\d]/g, ''), 10)
          expect(uiValue).to.eq(sum)
        })
      });
    })

    allure.step('Überprüfung des Bereischs "Urlauber-/Ausländerfischereischeine"',()=>{
      statisticsComponent.getVacationLicencesOverview().should('be.visible')
      statisticsComponent.getVacationLicencesOverviewTitle().should('be.visible').contains(title3)
      statisticsComponent.getVacationLicencesOverviewSubtitle().should('be.visible').contains(subtitle)
      statisticsComponent.getOverviewPrimaryLabels().eq(2).should('be.visible').contains(selectedYear)
      statisticsComponent.getOverviewSecondaryLabels().eq(2).should('be.visible').contains(previousYear)
      statisticsComponent.getSelectedYearOverview().eq(2).should('be.visible').contains(selectedYear)
      statisticsComponent.getPreviousYearOverview().eq(2).should('be.visible').contains(previousYear)
      apiRequest.getStatisticsByYearStateAndOffice(access_token,specialLicencesURL,selectedYear,state,office).then((response) => {
        expect(response.status).to.eq(200)
        const responseBody = response.body
        const entries = responseBody[0].data
        const sum = entries.filter(item=>item.submissionType==='ANALOG')
                            .reduce((sum,value)=> sum + value.count, 0)
        statisticsComponent.getOverviewPrimaryValues().eq(2).should('be.visible').invoke('text').then((text)=>{
          const uiValue = parseInt(text.replace(/[^\d]/g, ''), 10)
          expect(uiValue).to.eq(sum)
        })
      });
      apiRequest.getStatisticsByYearStateAndOffice(access_token,specialLicencesURL,previousYear,state,office).then((response) => {
        expect(response.status).to.eq(200)
        const responseBody = response.body
        const entries = responseBody[0].data
        const sum = entries.filter(item=>item.submissionType==='ANALOG')
                            .reduce((sum,value)=> sum + value.count, 0)
        statisticsComponent.getOverviewSecondaryValues().eq(2).should('be.visible').invoke('text').then((text)=>{
          const uiValue = parseInt(text.replace(/[^\d]/g, ''), 10)
          expect(uiValue).to.eq(sum)
        })
      });
    })
    
    allure.step('Überprüfung des Bereischs "Sonderfischereischeine"',()=>{
      statisticsComponent.getLimitedLicencesOverview().should('be.visible').scrollIntoView()
      statisticsComponent.getLimitedLicencesOverviewTitle().should('be.visible').contains(title4)
      statisticsComponent.getLimitedLicencesOverviewSubtitle().should('be.visible').contains(subtitle)
      statisticsComponent.getOverviewPrimaryLabels().eq(3).should('be.visible').contains(selectedYear)
      statisticsComponent.getOverviewSecondaryLabels().eq(3).should('be.visible').contains(previousYear)
      statisticsComponent.getSelectedYearOverview().eq(3).should('be.visible').contains(selectedYear)
      statisticsComponent.getPreviousYearOverview().eq(3).should('be.visible').contains(previousYear)
      apiRequest.getStatisticsByYearStateAndOffice(access_token,limitedLicencesURL,selectedYear,state,office).then((response) => {
        expect(response.status).to.eq(200)
        const responseBody = response.body
        const entries = responseBody[0].data
        const sum = entries.filter(item=>item.submissionType==='ANALOG')
                            .reduce((sum,value)=> sum + value.count, 0)
        statisticsComponent.getOverviewPrimaryValues().eq(3).should('be.visible').invoke('text').then((text)=>{
          const uiValue = parseInt(text.replace(/[^\d]/g, ''), 10)
          expect(uiValue).to.eq(sum)
        })
      });
      apiRequest.getStatisticsByYearStateAndOffice(access_token,limitedLicencesURL,previousYear,state,office).then((response) => {
        expect(response.status).to.eq(200)
        const responseBody = response.body
        const entries = responseBody[0].data
        const sum = entries.filter(item=>item.submissionType==='ANALOG')
                            .reduce((sum,value)=> sum + value.count, 0)
        statisticsComponent.getOverviewSecondaryValues().eq(3).should('be.visible').invoke('text').then((text)=>{
          const uiValue = parseInt(text.replace(/[^\d]/g, ''), 10)
          expect(uiValue).to.eq(sum)
        })
      });
    })
  })

  it('Überprüfung des Dashboards "Auswertungen-Übersicht": Typ=Prüfungen, Jahr=2025, Bundesland=SH, Prüfungsbehörde=Schleswiger Angel- und Wassersportverein',()=>{
    const title = "Prüfungen";
    const subtitle = "Alle Daten";
    const selectedYear = 2025;
    const previousYear = selectedYear - 1;
    const state = "SH";
    const office = "Schleswiger Angel- und Wassersportverein";
    const URL = apiURL.URLs.baseURL + apiURL.URLs.statisticsCertificationsURL

    allure.step('Wähle "Fischereischein & Abagaben" aus der Filter "Typ"',()=>{
      statisticsComponent.getStatisticsFilterType().should('be.visible').click()
      statisticsComponent.getStatisticsFilterLists().should('be.visible')
      statisticsComponent.getStatisticsFilterListItems().eq(2).should('be.visible').click()
    })

    allure.step('Wähle "2025" aus der Filter "Jahr"',()=>{
      statisticsComponent.getStatisticsFilterYear().should('be.visible').click()
      statisticsComponent.getStatisticsFilterListItems().eq(0).should('be.visible').click()
    })

    allure.step('Wähle "SH" aus der Filter "Bundesland"',()=>{
      statisticsComponent.getStatisticsFilterState().should('be.visible').click()
      statisticsComponent.getStatisticsFilterListItems().eq(14).scrollIntoView().should('be.visible').click()
    })

    allure.step('Wähle "Schleswiger Angel- und Wassersportverein" aus der Filter "Prüfungsbehörde"',()=>{
      statisticsComponent.getStatisticsFilterExamIssuer().should('be.visible').click()
      statisticsComponent.getStatisticsFilterListItems().eq(4).scrollIntoView().should('be.visible').click()
    })

    allure.step('Überprüfung des Bereischs "Prüfungen"',()=>{
      statisticsComponent.getCertificationsOverview().should('be.visible')
      statisticsComponent.getCertificationLicencesOverviewTitle().should('be.visible').contains(title)
      statisticsComponent.getCertificationLicencesOverviewSubtitle().should('be.visible').contains(subtitle)
      statisticsComponent.getOverviewPrimaryLabels().eq(0).should('be.visible').contains(selectedYear)
      statisticsComponent.getOverviewSecondaryLabels().eq(0).should('be.visible').contains(previousYear)
      statisticsComponent.getCertificationsSelectedYear().should('be.visible').contains(selectedYear)
      statisticsComponent.getCertificationsPreviousYearOverview().should('be.visible').contains(previousYear)
      apiRequest.getStatisticsByYearStateAndOffice(access_token,URL,selectedYear,state,office).then((response) => {
        expect(response.status).to.eq(200)
        const responseBody = response.body
        statisticsComponent.getOverviewPrimaryValues().should('be.visible').invoke('text').then((text)=>{
          const uiValue = text.trim()
          const apiValue = responseBody[0].data[0].amount
          expect(parseInt(uiValue, 10)).to.equal(apiValue)
        })
      });
      apiRequest.getStatisticsByYearStateAndOffice(access_token,URL,previousYear,state,office).then((response) => {
        expect(response.status).to.eq(200)
        const responseBody = response.body
        statisticsComponent.getOverviewSecondaryValues().should('be.visible').invoke('text').then((text)=>{
          const uiValue = text.trim()
          const apiValue = responseBody[0].data[0].amount
          expect(parseInt(uiValue, 10)).to.equal(apiValue)
        })
      });
    })
  })

  it('Überprüfung des Dashboards "Auswertungen-Übersicht": Typ=Kontrollen, Jahr=2025, Bundesland=SH',()=>{
    const title = "Kontrollen";
    const subtitle = "Alle Daten";
    const selectedYear = 2025;
    const previousYear = selectedYear - 1;
    const state = "SH";
    const URL = apiURL.URLs.baseURL + apiURL.URLs.statisticsInspectionsURL
    
    allure.step('Wähle "Fischereischein & Abagaben" aus der Filter "Typ"',()=>{
      statisticsComponent.getStatisticsFilterType().should('be.visible').click()
      statisticsComponent.getStatisticsFilterLists().should('be.visible')
      statisticsComponent.getStatisticsFilterListItems().eq(3).should('be.visible').click()
    })

    allure.step('Wähle "2025" aus der Filter "Jahr"',()=>{
      statisticsComponent.getStatisticsFilterYear().should('be.visible').click()
      statisticsComponent.getStatisticsFilterListItems().eq(0).should('be.visible').click()
    })

    allure.step('Wähle "SH" aus der Filter "Bundesland"',()=>{
      statisticsComponent.getStatisticsFilterState().should('be.visible').click()
      statisticsComponent.getStatisticsFilterListItems().eq(14).scrollIntoView().should('be.visible').click()
    })

    allure.step('Überprüfung des Bereischs "Kontrollen"',()=>{
      statisticsComponent.getInspectionOverview().should('be.visible')
      statisticsComponent.getInspectionOverviewTitle().should('be.visible').contains(title)
      statisticsComponent.getInspectionOverviewSubtitle().should('be.visible').contains(subtitle)
      statisticsComponent.getOverviewPrimaryLabels().should('be.visible').contains(selectedYear)
      statisticsComponent.getOverviewSecondaryLabels().should('be.visible').contains(previousYear)
      apiRequest.getStatisticsByYearAndState(access_token,URL,selectedYear,state).then((response) => {
        expect(response.status).to.eq(200)
        const responseBody = response.body
        statisticsComponent.getOverviewPrimaryValues().should('be.visible').invoke('text').then((text)=>{
          const uiValue = text.trim()
          const apiValue = responseBody[0].data.numberOfInspections.toString()
          expect(parseInt(uiValue, 10)).to.equal(parseInt(apiValue,10))
        })
      });
      apiRequest.getStatisticsByYearAndState(access_token,URL,previousYear,state).then((response) => {
        expect(response.status).to.eq(200)
        const responseBody = response.body
        statisticsComponent.getOverviewSecondaryValues().should('be.visible').invoke('text').then((text)=>{
          const uiValue = text.trim()
          const apiValue = responseBody[0].data.numberOfInspections.toString()
          expect(parseInt(uiValue, 10)).to.equal(parseInt(apiValue,10))
        })
      });
    })
  })

  it('Überprüfung des Dashboards "Auswertungen-Übersicht": Typ=Sperren, Jahr=2025, Bundesland=SH',()=>{
    const title = "Sperren";
    const subtitle = "Alle Daten";
    const selectedYear = 2025;
    const previousYear = selectedYear - 1;
    const state = "SH";
    const URL1 = apiURL.URLs.baseURL + apiURL.URLs.statisticsBansURL
    const URL2 = apiURL.URLs.baseURL + apiURL.URLs.statisticsActiveBans

    allure.step('Wähle "Fischereischein & Abagaben" aus der Filter "Typ"',()=>{
      statisticsComponent.getStatisticsFilterType().should('be.visible').click()
      statisticsComponent.getStatisticsFilterLists().should('be.visible')
      statisticsComponent.getStatisticsFilterListItems().eq(4).scrollIntoView().should('be.visible').click()
    })

    allure.step('Wähle "2025" aus der Filter "Jahr"',()=>{
      statisticsComponent.getStatisticsFilterYear().should('be.visible').click()
      statisticsComponent.getStatisticsFilterListItems().eq(0).should('be.visible').click()
    })

    allure.step('Wähle "SH" aus der Filter "Bundesland"',()=>{
      statisticsComponent.getStatisticsFilterState().should('be.visible').click()
      statisticsComponent.getStatisticsFilterListItems().eq(14).scrollIntoView().should('be.visible').click()
    })

    allure.step('Überprüfung des Bereischs "Sperren"',()=>{
      statisticsComponent.getBanOverview().should('be.visible')
      statisticsComponent.getBanOverviewTitle().should('be.visible').contains(title)
      statisticsComponent.getBanOverviewSubtitle().should('be.visible').contains(subtitle)
      statisticsComponent.getOverviewPrimaryLabels().should('be.visible').contains(selectedYear)
      statisticsComponent.getOverviewTertiaryLabels().should('be.visible').contains("Aktuell gesperrt")
      statisticsComponent.getBansSelectedYear().should('be.visible').contains(selectedYear)
      statisticsComponent.getBansPreviousYearOverview().should('be.visible').contains(previousYear)
      apiRequest.getStatisticsByYearAndState(access_token,URL1,selectedYear,state).then((response) => {
        expect(response.status).to.eq(200)
        const responseBody = response.body
        statisticsComponent.getOverviewPrimaryValues().should('be.visible').invoke('text').then((text)=>{
          const uiValue = text.trim()
          const apiValue = responseBody[0].data.issued.toString()
          expect(parseInt(uiValue, 10)).to.equal(parseInt(apiValue,10))
        })
      });
      apiRequest.getStatisticsActiveBans(access_token,URL2).then((response) => {
        expect(response.status).to.eq(200)
        const responseBody = response.body
        statisticsComponent.getOverviewTertiaryValues().should('be.visible').invoke('text').then((text)=>{
          const uiValue = text.trim()
          const apiValue = responseBody.toString()
          expect(parseInt(uiValue, 10)).to.equal(parseInt(apiValue,10))
        })
      });
    })
  })

  after(()=>{
    allure.step("Mitarbeiter der oberen Fischereibehörde NRW abmelden",()=>{
      headerComponent.getHeaderDropdownMenu().should('be.visible').click()
      headerComponent.getHeaderLogoutButton().should('be.visible').click() 
    })
  })
})