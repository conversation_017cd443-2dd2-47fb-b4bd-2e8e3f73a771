import {ExternalLoginPagePo} from "../support/pageObjects/external-login-page.po";
import {LoginPagePo} from "../support/pageObjects/login-page.po";
import {HomePagePo} from "../support/pageObjects/home-page.po";
import {SearchResultsPo} from "../support/pageObjects/search-results.po";
import {ConsentDigitizePo} from "../support/pageObjects/consent-digitize-page.po";
import {DigitizeLicensePo} from "../support/pageObjects/digitize-licence/digitize-license-page.po";
import {ServiceOverviewPo} from "../support/pageObjects/service-overview.po";
import * as allure from "allure-js-commons";
import { HeaderComponentCo } from "../support/sharedComponents/header-component.co";
import { ExaminationPagePo } from "../support/pageObjects/examination-page.po";

describe('Urlauber-/Ausländer-Fischereischein-digitalisieren', () => {
  const externalLoginPage: ExternalLoginPagePo = new ExternalLoginPagePo();
  const loginPage: LoginPagePo = new LoginPagePo();
  const homePage: HomePagePo = new HomePagePo();
  const searchResultsPage: SearchResultsPo = new SearchResultsPo();
  const consentDigitizePage: ConsentDigitizePo = new ConsentDigitizePo();
  const digitizeLicencePage: DigitizeLicensePo = new DigitizeLicensePo();
  const headerComponent: HeaderComponentCo = new HeaderComponentCo();
  const serviceOverviewPage: ServiceOverviewPo = new ServiceOverviewPo();
  const examinationPage: ExaminationPagePo = new ExaminationPagePo();
  let testUsers: any;
  let testData: any;
  let testCitizen: any;
  const currentDate = new Date().toISOString().split('T')[0];
  const system = Cypress.env('system');

  before(() => {
    cy.clearAllCookies();
    allure.feature('Prüfung-und-Fischereischein-anlegen');      
    allure.owner('Hegedus Kinga');
    cy.fixture(`${system}/suche/testuser.json`).then((users) => {
        cy.log('reading the test users file');
        testUsers = users;
    });
  
    cy.fixture(`${system}/suche/testdata.json`).then((data) => {    
        cy.log('reading the test data file');
        testData = data;
        cy.log(JSON.stringify(testData));
    });
  
    cy.fixture(`${system}/suche/testcitizen.json`).then((citizen) => {      
        cy.log('reading the test users file');      
        testCitizen = citizen;        
    });
    })

  it('Einen neuen digitalen Urlauberfischereischein-Eintrag erstellen: vom Nutzer erstellt, mit 28 Tage Gültigkeit ab heute, mit Verwaltungsgebühr, mit Abgabe, Kartenzahlung.',()=>{
    allure.story('Einen neuen digitalen Urlauberfischereischein-Eintrag erstellen: vom Nutzer erstellt, mit 28 Tage Gültigkeit ab heute, mit Verwaltungsgebühr, mit Abgabe, Kartenzahlung.');
    allure.severity('normal');

    allure.step('Als SH Mitarbeiter anmelden', () => {
      allure.parameter("username", testUsers.user1.username);
      allure.parameter("userPassword", '');
      cy.visit('/');
      loginPage.getLoginButton().click();
      externalLoginPage.getLoginPageUsernameInput().type(testUsers.user1.username);
      externalLoginPage.getLoginPagePasswordInput().type(testUsers.user1.password, {log: false});
      externalLoginPage.getLoginPageSubmitButton().click();
    })

    allure.step('Wähle "Fischereischein anlegen" aus dem Menü "Neuer Eintrag"', () => {
      homePage.getSearchBarInputComponent().getSearchbarComponentSearchButton().click();
      searchResultsPage.getSearchResultsPageCreationMenu().should('be.visible');
      searchResultsPage.getSearchResultsPageCreationMenu().click();
      searchResultsPage.getSearchResultsPageCreationMenuOpener().should('be.visible').click();
    })

    allure.step('Akzeptiere GDPR', () => {
      searchResultsPage.getSearchResultsPageCreationMenuVacation().should('be.visible').click();
      consentDigitizePage.getGdprCheckbox().click().find('input').should('have.class', 'ng-valid');
      consentDigitizePage.getSelfDisclosureCheckbox().click().find('input').should('have.class', 'ng-valid');
      consentDigitizePage.getConsentPageContinueButton().should('be.enabled').click()
    });
    allure.step('Ausfüllung der Personen Daten Formular',()=>{
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormFirstnameInput().should('be.visible').type(testCitizen.buerger4.person.firstname)
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormLastnameInput().should('be.visible').type(testCitizen.buerger4.person.lastname)
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormBirthDateInput().should('be.visible').type(testCitizen.buerger4.person.birthdate)
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormBirthPlaceInput().should('be.visible').type(testCitizen.buerger4.person.birthplace)
      digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().should('be.visible').click()
    })
    
    allure.step('Ausfüllen des Formulars für Zeitraum und Dauer',()=>{
      //TODO: update after implementation is ready
      //digitizeLicencePage.getDurationComponent().getDurationTab().should('be.visible')
      digitizeLicencePage.getDurationComponent().getDurationTable().should('be.visible')
      digitizeLicencePage.getDurationComponent().getDurationTablePeriodInput().should('be.visible').should('have.value',"28")
      digitizeLicencePage.getDurationComponent().getDurationTableStartDateInput().should('be.visible').type(currentDate)
      digitizeLicencePage.getDurationComponent().getDurationTableEndDateInput()
        .invoke('val')
        .then((startDate) => {
          digitizeLicencePage.getDurationComponent().getDurationTablePeriodInput()
            .invoke('val')
            .then((days) => {
              const numberOfDays = parseInt(days, 10);
              cy.calculateEndDate(startDate, numberOfDays).then((expectedEndDate) => {
                digitizeLicencePage.getDurationComponent().getDurationTableEndDateInput()
                  .invoke('val').should('eq', expectedEndDate);
              });
            });
        })
        digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().click()
    })
    
    allure.step('Fülle das Formular "Gebühren und Abgaben" aus',()=>{
      digitizeLicencePage.getTaxesComponent().getTaxesBoxHeader().should('be.visible').contains('37,00 €')
      digitizeLicencePage.getTaxesComponent().getTaxesNewLicence().should('be.visible').contains('20,00 €')
      digitizeLicencePage.getTaxesComponent().getTaxesNewLicenceCheckbox().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getStaticTaxValue().should('be.visible').contains('17,00 €')
      digitizeLicencePage.getTaxesComponent().getStaticTaxValueCheckbox().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getPaymentMethodCash().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getPaymentMethodCard().should('not.be.checked').click()
      digitizeLicencePage.getEditFooterComponent().getFooterFinishButton().should('be.visible').click()
    })
    
    allure.step('Überprüfe und versende die erstellten Dokumente',()=>{
      cy.wait(2000)
      digitizeLicencePage.getDocumentsComponent().getVacationDocumentsPage().should('be.visible')
      digitizeLicencePage.getDocumentsComponent().getDocumentsBox().should('be.visible').contains('Digitale Dokumente')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(0).should('be.visible').contains('Urlauberfischereischein')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(1).should('be.visible').contains('Fischereiabgabe')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(2).should('be.visible').contains('E-Mail-Versand')
      digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(0).should('be.visible').contains('Drucken')
      digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(1).should('be.visible').contains('Drucken')
      digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(2).should('be.visible').contains('Senden').click()
      digitizeLicencePage.getDocumentsComponent().getDocumentsDialog().should('be.visible')
      digitizeLicencePage.getDocumentsComponent().getDocumentsEmailInput().should('be.visible').type('<EMAIL>')
      digitizeLicencePage.getDocumentsComponent().getDocumentsEmailConfirmInput().should('be.visible').type('<EMAIL>')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDialogSend().should('be.visible').click()
      digitizeLicencePage.getDocumentsComponent().getDocumentsSuccessContainer().should('be.visible').contains('Dokumente wurden versendet')
    })

    allure.step('Als SH Mitarbeiter abmelden', () => {
      headerComponent.getHeaderDropdownMenu().should('be.visible').click()
      headerComponent.getHeaderLogoutButton().should('be.visible').click() 
    })
  })

  it('Einen neuen digitalen Urlauberfischereischein-Eintrag erstellen: vom Dritten erstellt, mit 28 Tage Gültigkeit ab heute, mit Verwaltungsgebühr, mit Abgabe, Kartenzahlung.',()=>{
    allure.story('Einen neuen digitalen Urlauberfischereischein-Eintrag erstellen: vom Dritten erstellt, mit 28 Tage Gültigkeit ab heute, mit Verwaltungsgebühr, mit Abgabe, Kartenzahlung.');
    allure.severity('normal');

    allure.step('Als SH Mitarbeiter anmelden', () => {
      allure.parameter("username", testUsers.user1.username);
      allure.parameter("userPassword", '');
      cy.visit('/');
      loginPage.getLoginButton().click();
      externalLoginPage.getLoginPageUsernameInput().type(testUsers.user1.username);
      externalLoginPage.getLoginPagePasswordInput().type(testUsers.user1.password, {log: false});
      externalLoginPage.getLoginPageSubmitButton().click();
    })

    allure.step('Wähle "Fischereischein anlegen" aus dem Menü "Neuer Eintrag"', () => {
      homePage.getSearchBarInputComponent().getSearchbarComponentSearchButton().click();
      searchResultsPage.getSearchResultsPageCreationMenu().should('be.visible');
      searchResultsPage.getSearchResultsPageCreationMenu().click();
      searchResultsPage.getSearchResultsPageCreationMenuOpener().should('be.visible').click();
    })

    allure.step('Akzeptiere GDPR', () => {
      searchResultsPage.getSearchResultsPageCreationMenuVacation().should('be.visible').click();
      consentDigitizePage.getGdprCheckbox().click().find('input').should('have.class', 'ng-valid');
      consentDigitizePage.getSelfDisclosureCheckbox().click().find('input').should('have.class', 'ng-valid');
      consentDigitizePage.getConsentPageContinueButton().should('be.enabled').click()
    });
    allure.step('Ausfüllung der Personen Daten Formular',()=>{
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormFirstnameInput().should('be.visible').type(testCitizen.buerger4.person.firstname)
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormLastnameInput().should('be.visible').type(testCitizen.buerger4.person.lastname)
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormBirthDateInput().should('be.visible').type(testCitizen.buerger4.person.birthdate)
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormBirthPlaceInput().should('be.visible').type(testCitizen.buerger4.person.birthplace)
      digitizeLicencePage.getEditFooterComponent().getFooterConfirmBox().should('be.visible').click()
      digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().should('be.visible').click()
    })
    
    allure.step('Ausfüllen des Formulars für Zeitraum und Dauer',()=>{
      //TODO: update after implementation is ready
      //digitizeLicencePage.getDurationComponent().getDurationTab().should('be.visible')
      digitizeLicencePage.getDurationComponent().getDurationTable().should('be.visible')
      digitizeLicencePage.getDurationComponent().getDurationTablePeriodInput().should('be.visible').should('have.value',"28")
      digitizeLicencePage.getDurationComponent().getDurationTableStartDateInput().should('be.visible').type(currentDate)
      digitizeLicencePage.getDurationComponent().getDurationTableEndDateInput()
        .invoke('val')
        .then((startDate) => {
          digitizeLicencePage.getDurationComponent().getDurationTablePeriodInput()
            .invoke('val')
            .then((days) => {
              const numberOfDays = parseInt(days, 10);
              cy.calculateEndDate(startDate, numberOfDays).then((expectedEndDate) => {
                digitizeLicencePage.getDurationComponent().getDurationTableEndDateInput()
                  .invoke('val').should('eq', expectedEndDate);
              });
            });
        })
        digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().click()
    })
    
    allure.step('Fülle das Formular "Gebühren und Abgaben" aus',()=>{
      digitizeLicencePage.getTaxesComponent().getTaxesBoxHeader().should('be.visible').contains('37,00 €')
      digitizeLicencePage.getTaxesComponent().getTaxesNewLicence().should('be.visible').contains('20,00 €')
      digitizeLicencePage.getTaxesComponent().getTaxesNewLicenceCheckbox().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getStaticTaxValue().should('be.visible').contains('17,00 €')
      digitizeLicencePage.getTaxesComponent().getStaticTaxValueCheckbox().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getPaymentMethodCash().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getPaymentMethodCard().should('not.be.checked').click()
      digitizeLicencePage.getEditFooterComponent().getFooterFinishButton().should('be.visible').click()
    })
    
    allure.step('Überprüfe und versende die erstellten Dokumente',()=>{
      cy.wait(2000)
      digitizeLicencePage.getDocumentsComponent().getVacationDocumentsPage().should('be.visible')
      digitizeLicencePage.getDocumentsComponent().getDocumentsBox().should('be.visible').contains('Digitale Dokumente')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(0).should('be.visible').contains('Urlauberfischereischein')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(1).should('be.visible').contains('Fischereiabgabe')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(2).should('be.visible').contains('E-Mail-Versand')
      digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(0).should('be.visible').contains('Drucken')
      digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(1).should('be.visible').contains('Drucken')
      digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(2).should('be.visible').contains('Senden').click()
      digitizeLicencePage.getDocumentsComponent().getDocumentsDialog().should('be.visible')
      digitizeLicencePage.getDocumentsComponent().getDocumentsEmailInput().should('be.visible').type('<EMAIL>')
      digitizeLicencePage.getDocumentsComponent().getDocumentsEmailConfirmInput().should('be.visible').type('<EMAIL>')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDialogSend().should('be.visible').click()
      digitizeLicencePage.getDocumentsComponent().getDocumentsSuccessContainer().should('be.visible').contains('Dokumente wurden versendet')
    })

    allure.step('Als SH Mitarbeiter abmelden', () => {
      headerComponent.getHeaderDropdownMenu().should('be.visible').click()
      headerComponent.getHeaderLogoutButton().should('be.visible').click() 
    })
  })

  it('Einen neuen digitalen Ausländerfischereischein-Eintrag erstellen: vom Nutzer erstellt, mit 1 Jahr Gültigkeit ab heute, mit Verwaltungsgebühr, mit Abgabe, Barzahlung.',()=>{
    allure.story('Einen neuen digitalen Ausländerfischereischein-Eintrag erstellen: vom Nutzer erstellt, mit 1 Jahr Gültigkeit ab heute, mit Verwaltungsgebühr, mit Abgabe, Barzahlung.');
    allure.severity('normal');

    allure.step('Als NRW Mitarbeiter anmelden', () => {
      allure.parameter("username", testUsers.user6.username);
      allure.parameter("userPassword", '');
      cy.visit('/');
      loginPage.getLoginButton().click();
      externalLoginPage.getLoginPageUsernameInput().type(testUsers.user6.username);
      externalLoginPage.getLoginPagePasswordInput().type(testUsers.user6.password, {log: false});
      externalLoginPage.getLoginPageSubmitButton().click();
    })

    allure.step('Wähle "Fischereischein anlegen" aus dem Menü "Neuer Eintrag"', () => {
      homePage.getSearchBarInputComponent().getSearchbarComponentSearchButton().click();
      searchResultsPage.getSearchResultsPageCreationMenu().should('be.visible');
      searchResultsPage.getSearchResultsPageCreationMenu().click();
      searchResultsPage.getSearchResultsPageCreationMenuOpener().should('be.visible').click();
    })

    allure.step('Akzeptiere GDPR', () => {
      searchResultsPage.getSearchResultsPageCreationMenuVacation().should('be.visible').click();
      consentDigitizePage.getGdprCheckbox().click().find('input').should('have.class', 'ng-valid');
      consentDigitizePage.getSelfDisclosureCheckbox().click().find('input').should('have.class', 'ng-valid');
      consentDigitizePage.getConsentPageContinueButton().should('be.enabled').click()
    });
    allure.step('Ausfüllung der Personen Daten Formular',()=>{
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormFirstnameInput().should('be.visible').type(testCitizen.buerger4.person.firstname)
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormLastnameInput().should('be.visible').type(testCitizen.buerger4.person.lastname)
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormBirthDateInput().should('be.visible').type(testCitizen.buerger4.person.birthdate)
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormBirthPlaceInput().should('be.visible').type(testCitizen.buerger4.person.birthplace)
      digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().should('be.visible').click()
    })
    
    allure.step('Ausfüllen des Formulars für Zeitraum und Dauer',()=>{
      //TODO: update after implementation is ready
      //digitizeLicencePage.getDurationComponent().getDurationTab().should('be.visible')
      digitizeLicencePage.getDurationComponent().getDurationTable().should('be.visible')
      digitizeLicencePage.getDurationComponent().getDurationTablePeriodInput().should('be.visible').should('have.value',"1")
      digitizeLicencePage.getDurationComponent().getDurationTableStartDateInput().should('be.visible').type(currentDate)
      digitizeLicencePage.getDurationComponent().getDurationTableEndDateInput()
        .invoke('val')
        .then((startDate) => {
          digitizeLicencePage.getDurationComponent().getDurationTablePeriodInput()
            .invoke('val')
            .then((days) => {
              const numberOfDays = parseInt(days, 10);
              cy.calculateEndDate(startDate, numberOfDays).then((expectedEndDate) => {
                digitizeLicencePage.getDurationComponent().getDurationTableEndDateInput()
                  .invoke('val').should('eq', expectedEndDate);
              });
            });
        })
        digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().click()
    })
    
    allure.step('Fülle das Formular "Gebühren und Abgaben" aus',()=>{
      digitizeLicencePage.getTaxesComponent().getTaxesBoxHeader().should('be.visible').contains('26,00 €')
      digitizeLicencePage.getTaxesComponent().getTaxesNewLicence().should('be.visible').contains('14,00 €')
      digitizeLicencePage.getTaxesComponent().getTaxesNewLicenceCheckbox().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getStaticTaxValue().should('be.visible').contains('12,00 €')
      digitizeLicencePage.getTaxesComponent().getStaticTaxValueCheckbox().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getPaymentMethodCash().should('not.be.checked').click()
      digitizeLicencePage.getTaxesComponent().getPaymentMethodCard().should('be.visible')
      digitizeLicencePage.getEditFooterComponent().getFooterFinishButton().should('be.visible').click()
    })
    
    allure.step('Überprüfe und versende die erstellten Dokumente',()=>{
      cy.wait(2000)
      digitizeLicencePage.getDocumentsComponent().getVacationDocumentsPage().should('be.visible')
      digitizeLicencePage.getDocumentsComponent().getDocumentsBox().should('be.visible').contains('Digitale Dokumente')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(0).should('be.visible').contains('Ausländerfischereischein')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(1).should('be.visible').contains('Fischereiabgabe')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(2).should('be.visible').contains('E-Mail-Versand')
      digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(0).should('be.visible').contains('Drucken')
      digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(1).should('be.visible').contains('Drucken')
      digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(2).should('be.visible').contains('Senden').click()
      digitizeLicencePage.getDocumentsComponent().getDocumentsDialog().should('be.visible')
      digitizeLicencePage.getDocumentsComponent().getDocumentsEmailInput().should('be.visible').type('<EMAIL>')
      digitizeLicencePage.getDocumentsComponent().getDocumentsEmailConfirmInput().should('be.visible').type('<EMAIL>')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDialogSend().should('be.visible').click()
      digitizeLicencePage.getDocumentsComponent().getDocumentsSuccessContainer().should('be.visible').contains('Dokumente wurden versendet')
    })

    allure.step('Als NRW Mitarbeiter abmelden', () => {
      headerComponent.getHeaderDropdownMenu().should('be.visible').click()
      headerComponent.getHeaderLogoutButton().should('be.visible').click() 
    })
  })

  it('Einen neuen digitalen Ausländerfischereischein-Eintrag erstellen: vom Dritten erstellt, mit 1 Jahr Gültigkeit ab heute, mit Verwaltungsgebühr, mit Abgabe, Barzahlung.',()=>{
    allure.story('Einen neuen digitalen Ausländerfischereischein-Eintrag erstellen: vom Dritten erstellt, mit 1 Jahr Gültigkeit ab heute, mit Verwaltungsgebühr, mit Abgabe, Barzahlung.');
    allure.severity('normal');

    allure.step('Als NRW Mitarbeiter anmelden', () => {
      allure.parameter("username", testUsers.user6.username);
      allure.parameter("userPassword", '');
      cy.visit('/');
      loginPage.getLoginButton().click();
      externalLoginPage.getLoginPageUsernameInput().type(testUsers.user6.username);
      externalLoginPage.getLoginPagePasswordInput().type(testUsers.user6.password, {log: false});
      externalLoginPage.getLoginPageSubmitButton().click();
    })

    allure.step('Wähle "Fischereischein anlegen" aus dem Menü "Neuer Eintrag"', () => {
      homePage.getSearchBarInputComponent().getSearchbarComponentSearchButton().click();
      searchResultsPage.getSearchResultsPageCreationMenu().should('be.visible');
      searchResultsPage.getSearchResultsPageCreationMenu().click();
      searchResultsPage.getSearchResultsPageCreationMenuOpener().should('be.visible').click();
    })

    allure.step('Akzeptiere GDPR', () => {
      searchResultsPage.getSearchResultsPageCreationMenuVacation().should('be.visible').click();
      consentDigitizePage.getGdprCheckbox().click().find('input').should('have.class', 'ng-valid');
      consentDigitizePage.getSelfDisclosureCheckbox().click().find('input').should('have.class', 'ng-valid');
      consentDigitizePage.getConsentPageContinueButton().should('be.enabled').click()
    });

    allure.step('Ausfüllung der Personen Daten Formular',()=>{
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormFirstnameInput().should('be.visible').type(testCitizen.buerger4.person.firstname)
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormLastnameInput().should('be.visible').type(testCitizen.buerger4.person.lastname)
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormBirthDateInput().should('be.visible').type(testCitizen.buerger4.person.birthdate)
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormBirthPlaceInput().should('be.visible').type(testCitizen.buerger4.person.birthplace)
      digitizeLicencePage.getEditFooterComponent().getFooterConfirmBox().should('be.visible').click()
      digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().should('be.visible').click()
    })
    
    allure.step('Ausfüllen des Formulars für Zeitraum und Dauer',()=>{
      //TODO: update after implementation is ready
      //digitizeLicencePage.getDurationComponent().getDurationTab().should('be.visible')
      digitizeLicencePage.getDurationComponent().getDurationTable().should('be.visible')
      digitizeLicencePage.getDurationComponent().getDurationTablePeriodInput().should('be.visible').should('have.value',"1")
      digitizeLicencePage.getDurationComponent().getDurationTableStartDateInput().should('be.visible').type(currentDate)
      digitizeLicencePage.getDurationComponent().getDurationTableEndDateInput()
        .invoke('val')
        .then((startDate) => {
          digitizeLicencePage.getDurationComponent().getDurationTablePeriodInput()
            .invoke('val')
            .then((days) => {
              const numberOfDays = parseInt(days, 10);
              cy.calculateEndDate(startDate, numberOfDays).then((expectedEndDate) => {
                digitizeLicencePage.getDurationComponent().getDurationTableEndDateInput()
                  .invoke('val').should('eq', expectedEndDate);
              });
            });
        })
        digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().click()
    })
    
    allure.step('Fülle das Formular "Gebühren und Abgaben" aus',()=>{
      digitizeLicencePage.getTaxesComponent().getTaxesBoxHeader().should('be.visible').contains('26,00 €')
      digitizeLicencePage.getTaxesComponent().getTaxesNewLicence().should('be.visible').contains('14,00 €')
      digitizeLicencePage.getTaxesComponent().getTaxesNewLicenceCheckbox().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getStaticTaxValue().should('be.visible').contains('12,00 €')
      digitizeLicencePage.getTaxesComponent().getStaticTaxValueCheckbox().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getPaymentMethodCash().should('not.be.checked').click()
      digitizeLicencePage.getTaxesComponent().getPaymentMethodCard().should('be.visible')
      digitizeLicencePage.getEditFooterComponent().getFooterFinishButton().should('be.visible').click()
    })
    
    allure.step('Überprüfe und versende die erstellten Dokumente',()=>{
      cy.wait(2000)
      digitizeLicencePage.getDocumentsComponent().getVacationDocumentsPage().should('be.visible')
      digitizeLicencePage.getDocumentsComponent().getDocumentsBox().should('be.visible').contains('Digitale Dokumente')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(0).should('be.visible').contains('Ausländerfischereischein')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(1).should('be.visible').contains('Fischereiabgabe')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(2).should('be.visible').contains('E-Mail-Versand')
      digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(0).should('be.visible').contains('Drucken')
      digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(1).should('be.visible').contains('Drucken')
      digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(2).should('be.visible').contains('Senden').click()
      digitizeLicencePage.getDocumentsComponent().getDocumentsDialog().should('be.visible')
      digitizeLicencePage.getDocumentsComponent().getDocumentsEmailInput().should('be.visible').type('<EMAIL>')
      digitizeLicencePage.getDocumentsComponent().getDocumentsEmailConfirmInput().should('be.visible').type('<EMAIL>')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDialogSend().should('be.visible').click()
      digitizeLicencePage.getDocumentsComponent().getDocumentsSuccessContainer().should('be.visible').contains('Dokumente wurden versendet')
    })

    allure.step('Als NRW Mitarbeiter abmelden', () => {
      headerComponent.getHeaderDropdownMenu().should('be.visible').click()
      headerComponent.getHeaderLogoutButton().should('be.visible').click() 
    })
  })

  it('Eine neue Fischereischein Prüfung und Urlauberfischereischein anlegen: vom Nutzer erstellt, mit 28 Tage Gültigkeit ab heute, mit Verwaltungsgebühr, mit Abgabe, Barzahlung.',()=>{
      allure.story('Eine neue Fischereischein Prüfung und Urlauberfischereischein anlegen: vom Nutzer erstellt, mit 28 Tage Gültigkeit ab heute, mit Verwaltungsgebühr, mit Abgabe, Barzahlung.');
      allure.severity('normal');
  
      allure.step('Als Prüfer anmelden', () => {
        allure.parameter("username", testUsers.user2.username);
        allure.parameter("userPassword", '');
        cy.visit('/');
        loginPage.getLoginButton().click();
        externalLoginPage.getLoginPageUsernameInput().type(testUsers.user2.username);
        externalLoginPage.getLoginPagePasswordInput().type(testUsers.user2.password, {log: false});
        externalLoginPage.getLoginPageSubmitButton().click();
      });
  
      allure.step('Befüllen des Prüfungsformulars',()=>{
        examinationPage.getExaminationPage().should('be.visible')      
        examinationPage.getExamPageFormFirstname().should('be.visible').type(testCitizen.buerger1.person.firstname)
        examinationPage.getExamPageFormLastname().should('be.visible').type(testCitizen.buerger1.person.lastname)
        examinationPage.getExamPageFormBirthdate().should('be.visible').type(testCitizen.buerger1.person.birthdate)        
        examinationPage.getExamPageFormBirthplace().should('be.visible').type(testCitizen.buerger1.person.birthplace)
        examinationPage.getExamPageFormPassDate().should('be.visible').type("2009-01-01")
        examinationPage.getExamPageContinueButton().should('be.visible').click()
        examinationPage.getExamSubmitButton().should('be.visible').click()
        examinationPage.getSuccessPage().should('be.visible').contains("Erfolgreich angelegt!")
        examinationPage.getSuccessPage().should('be.visible').contains("Bestandene Prüfung wurde erfolgreich im Fischereiregister aufgenommen.")
        examinationPage.getSuccessEntries().invoke('text')
        .then((text) => {
          const match = text.match(/ZF\d{2}-\d{4}-\d{4}-\d{4}/);
          cy.wrap(match[0]).as('accessCode')
        });
        cy.get('@accessCode').then((accessCode) => {
          cy.log(`Extracted Code: ${accessCode}`);
          examinationPage.getSuccessEntries().should('be.visible').contains(accessCode)
        });
      }) 
  
      allure.step('Als Prüfer abmelden', () => {
        headerComponent.getHeaderDropdownMenu().should('be.visible').click()
        headerComponent.getHeaderLogoutButton().should('be.visible').click() 
      });
  
      allure.step('Als SH Mitarbeiter anmelden', () => {
        allure.parameter("username", testUsers.user1.username);
        allure.parameter("userPassword", '');
        cy.visit('/');
        loginPage.getLoginButton().click();
        externalLoginPage.getLoginPageUsernameInput().type(testUsers.user1.username);
        externalLoginPage.getLoginPagePasswordInput().type(testUsers.user1.password, {log: false});
        externalLoginPage.getLoginPageSubmitButton().click();
      });
      
      allure.step('Suche nach LizenzID durchführen und Serviceübersicht öffnen', () => {
        cy.get('@accessCode').then((accessCode) => {
          searchResultsPage.getSearchBarInputComponent().getSearchbarComponentInput().should('be.visible')
            .type(accessCode);
        });
        homePage.getSearchBarInputComponent().getSearchbarComponentSearchButton().click();
        searchResultsPage.getSearchResultsComponent().getSearchResultsRowInpectionLink().should('be.visible').click()
        serviceOverviewPage.getUserProfileHeader().should('be.visible')
          .contains(`${testCitizen.buerger1.person.firstname} ${testCitizen.buerger1.person.lastname}`)
      })
  
      allure.step('Urlauberfischereischein anlegen',()=>{
        serviceOverviewPage.getFishingLicenceButton().should('be.visible').click()
        serviceOverviewPage.getFishingLicenceCard().should('be.visible')
        serviceOverviewPage.getFishingLicenceVacation().should('be.visible').click()
        //TODO:update after implementation is ready
        //serviceOverviewPage.getFishingLicenceOther().should('be.visible')
        //serviceOverviewPage.getFishingLicenceCreate().should('be.visible').click()
      })
  
      allure.step('Akzeptiere GDPR', () => {
        consentDigitizePage.getGdprCheckbox().click().find('input').should('have.class', 'ng-valid');
        consentDigitizePage.getSelfDisclosureCheckbox().click().find('input').should('have.class', 'ng-valid');
        consentDigitizePage.getConsentPageContinueButton().should('be.enabled').click()
      });
  
      allure.step('Überprüfe der Personen Daten im Formular',()=>{
        digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().should('be.visible')
        digitizeLicencePage.getTabPersonalDataComponent().getUserDataForm()
        digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormFirstnameInput().should('have.value', testCitizen.buerger1.person.firstname)
        digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormLastnameInput().should('have.value', testCitizen.buerger1.person.lastname)
        digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormBirthDateInput().should('have.value', testCitizen.buerger1.person.birthdate)
        digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormBirthPlaceInput().should('have.value', testCitizen.buerger1.person.birthplace)
        headerComponent.getAllTabTitleState().eq(0).should('have.class', 'active')
        headerComponent.getAllTabTitleState().eq(0).should('not.be.disabled')
        headerComponent.getAllTabTitleState().eq(1).should('have.class', 'inactive')
        headerComponent.getAllTabTitleState().eq(1).should('not.be.disabled')
        headerComponent.getAllTabTitleState().eq(2).should('have.class', 'inactive')
        headerComponent.getAllTabTitleState().eq(2).should('be.disabled')
        headerComponent.getAllTabTitleState().eq(3).should('have.class', 'inactive')
        headerComponent.getAllTabTitleState().eq(3).should('be.disabled')
        digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormNationalityInput().should('have.value', testCitizen.buerger1.person.nationality)
        digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().click()
      })
  
      allure.step('Ausfüllen des Formulars für Zeitraum und Dauer',()=>{
        //TODO: update after implementation is ready
        //digitizeLicencePage.getDurationComponent().getDurationTab().should('be.visible')
        digitizeLicencePage.getDurationComponent().getDurationTable().should('be.visible')
        digitizeLicencePage.getDurationComponent().getDurationTablePeriodInput().should('be.visible').should('have.value',"28")
        headerComponent.getAllTabTitleState().eq(0).should('have.class', 'inactive')
        headerComponent.getAllTabTitleState().eq(0).should('not.be.disabled')
        headerComponent.getAllTabTitleState().eq(1).should('have.class', 'active')
        headerComponent.getAllTabTitleState().eq(1).should('not.be.disabled')
        headerComponent.getAllTabTitleState().eq(2).should('have.class', 'inactive')
        headerComponent.getAllTabTitleState().eq(2).should('be.disabled')
        headerComponent.getAllTabTitleState().eq(3).should('have.class', 'inactive')
        headerComponent.getAllTabTitleState().eq(3).should('be.disabled')
        digitizeLicencePage.getDurationComponent().getDurationTableStartDateInput().should('be.visible').type(currentDate)
        digitizeLicencePage.getDurationComponent().getDurationTableEndDateInput()
          .invoke('val')
          .then((startDate) => {
            digitizeLicencePage.getDurationComponent().getDurationTablePeriodInput()
              .invoke('val')
              .then((days) => {
                const numberOfDays = parseInt(days, 10);
                cy.calculateEndDate(startDate, numberOfDays).then((expectedEndDate) => {
                  digitizeLicencePage.getDurationComponent().getDurationTableEndDateInput()
                    .invoke('val').should('eq', expectedEndDate);
                });
              });
          })
        digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().click()
      })
  
      allure.step('Fülle das Formular "Gebühren und Abgaben" aus',()=>{
        digitizeLicencePage.getTaxesComponent().getTaxesBoxHeader().should('be.visible').contains('37,00 €')
        digitizeLicencePage.getTaxesComponent().getTaxesNewLicence().should('be.visible').contains('20,00 €')
        headerComponent.getAllTabTitleState().eq(0).should('have.class', 'inactive')
        headerComponent.getAllTabTitleState().eq(0).should('not.be.disabled')
        headerComponent.getAllTabTitleState().eq(1).should('have.class', 'inactive')
        headerComponent.getAllTabTitleState().eq(1).should('not.be.disabled')
        headerComponent.getAllTabTitleState().eq(2).should('have.class', 'active')
        headerComponent.getAllTabTitleState().eq(2).should('not.be.disabled')
        headerComponent.getAllTabTitleState().eq(3).should('have.class', 'inactive')
        headerComponent.getAllTabTitleState().eq(3).should('be.disabled')
        digitizeLicencePage.getTaxesComponent().getTaxesNewLicenceCheckbox().should('be.visible')
        digitizeLicencePage.getTaxesComponent().getStaticTaxValue().should('be.visible').contains('17,00 €')
        digitizeLicencePage.getTaxesComponent().getStaticTaxValueCheckbox().should('be.visible')
        digitizeLicencePage.getTaxesComponent().getPaymentMethodCash().should('not.be.checked').click()
        digitizeLicencePage.getTaxesComponent().getPaymentMethodCard().should('be.visible')
        digitizeLicencePage.getEditFooterComponent().getFooterFinishButton().should('be.visible').click()
      })
  
      allure.step('Überprüfe und versende die erstellten Dokumente',()=>{
        cy.wait(2000)
        digitizeLicencePage.getDocumentsComponent().getVacationDocumentsPage().should('be.visible')
        digitizeLicencePage.getDocumentsComponent().getDocumentsBox().should('be.visible').contains('Digitale Dokumente')
        digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(0).should('be.visible').contains('Urlauberfischereischein')
        digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(1).should('be.visible').contains('Fischereiabgabe')
        digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(2).should('be.visible').contains('E-Mail-Versand')
        digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(0).should('be.visible').contains('Drucken')
        digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(1).should('be.visible').contains('Drucken')
        digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(2).should('be.visible').contains('Senden').click()
        digitizeLicencePage.getDocumentsComponent().getDocumentsDialog().should('be.visible')
        digitizeLicencePage.getDocumentsComponent().getDocumentsEmailInput().should('be.visible').type('<EMAIL>')
        digitizeLicencePage.getDocumentsComponent().getDocumentsEmailConfirmInput().should('be.visible').type('<EMAIL>')
        digitizeLicencePage.getDocumentsComponent().getDocumentsDialogSend().should('be.visible').click()
        digitizeLicencePage.getDocumentsComponent().getDocumentsSuccessContainer().should('be.visible').contains('Dokumente wurden versendet')
        headerComponent.getAllTabTitleState().eq(0).should('have.class', 'inactive')
        headerComponent.getAllTabTitleState().eq(0).should('be.disabled')
        headerComponent.getAllTabTitleState().eq(1).should('have.class', 'inactive')
        headerComponent.getAllTabTitleState().eq(1).should('be.disabled')
        headerComponent.getAllTabTitleState().eq(2).should('have.class', 'inactive')
        headerComponent.getAllTabTitleState().eq(2).should('be.disabled')
        headerComponent.getAllTabTitleState().eq(3).should('have.class', 'active')
        headerComponent.getAllTabTitleState().eq(3).should('not.be.disabled')
      })
  
      allure.step('Als SH Mitarbeiter abmelden', () => {
        headerComponent.getHeaderDropdownMenu().should('be.visible').click()
        headerComponent.getHeaderLogoutButton().should('be.visible').click() 
      })
  })

  it('Eine neue Fischereischein Prüfung und Urlauberfischereischein anlegen: vom Dritten erstellt, mit 28 Tage Gültigkeit ab heute, mit Verwaltungsgebühr, mit Abgabe, Barzahlung.',()=>{
    allure.story('Eine neue Fischereischein Prüfung und Urlauberfischereischein anlegen: vom Dritten erstellt, mit 28 Tage Gültigkeit ab heute, mit Verwaltungsgebühr, mit Abgabe, Barzahlung.');
    allure.severity('normal');

    allure.step('Als Prüfer anmelden', () => {
      allure.parameter("username", testUsers.user2.username);
      allure.parameter("userPassword", '');
      cy.visit('/');
      loginPage.getLoginButton().click();
      externalLoginPage.getLoginPageUsernameInput().type(testUsers.user2.username);
      externalLoginPage.getLoginPagePasswordInput().type(testUsers.user2.password, {log: false});
      externalLoginPage.getLoginPageSubmitButton().click();
    });
  
    allure.step('Befüllen des Prüfungsformulars',()=>{
      examinationPage.getExaminationPage().should('be.visible')      
      examinationPage.getExamPageFormFirstname().should('be.visible').type(testCitizen.buerger1.person.firstname)
      examinationPage.getExamPageFormLastname().should('be.visible').type(testCitizen.buerger1.person.lastname)
      examinationPage.getExamPageFormBirthdate().should('be.visible').type(testCitizen.buerger1.person.birthdate)        
      examinationPage.getExamPageFormBirthplace().should('be.visible').type(testCitizen.buerger1.person.birthplace)
      examinationPage.getExamPageFormPassDate().should('be.visible').type("2009-01-01")
      examinationPage.getExamPageContinueButton().should('be.visible').click()
      examinationPage.getExamSubmitButton().should('be.visible').click()
      examinationPage.getSuccessPage().should('be.visible').contains("Erfolgreich angelegt!")
      examinationPage.getSuccessPage().should('be.visible').contains("Bestandene Prüfung wurde erfolgreich im Fischereiregister aufgenommen.")
      examinationPage.getSuccessEntries().invoke('text')
      .then((text) => {          
        const match = text.match(/ZF\d{2}-\d{4}-\d{4}-\d{4}/);
        cy.wrap(match[0]).as('accessCode')
      });
      cy.get('@accessCode').then((accessCode) => {
        cy.log(`Extracted Code: ${accessCode}`);
        examinationPage.getSuccessEntries().should('be.visible').contains(accessCode)
      });
    }) 
  
    allure.step('Als Prüfer abmelden', () => {
      headerComponent.getHeaderDropdownMenu().should('be.visible').click()
      headerComponent.getHeaderLogoutButton().should('be.visible').click() 
    });
  
    allure.step('Als SH Mitarbeiter anmelden', () => {
      allure.parameter("username", testUsers.user1.username);
      allure.parameter("userPassword", '');
      cy.visit('/');
      loginPage.getLoginButton().click();
      externalLoginPage.getLoginPageUsernameInput().type(testUsers.user1.username);
      externalLoginPage.getLoginPagePasswordInput().type(testUsers.user1.password, {log: false});
      externalLoginPage.getLoginPageSubmitButton().click();
    });
      
    allure.step('Suche nach LizenzID durchführen und Serviceübersicht öffnen', () => {
      cy.get('@accessCode').then((accessCode) => {
        searchResultsPage.getSearchBarInputComponent().getSearchbarComponentInput().should('be.visible')
          .type(accessCode);
      });
      homePage.getSearchBarInputComponent().getSearchbarComponentSearchButton().click();
      searchResultsPage.getSearchResultsComponent().getSearchResultsRowInpectionLink().should('be.visible').click()
      serviceOverviewPage.getUserProfileHeader().should('be.visible')
        .contains(`${testCitizen.buerger1.person.firstname} ${testCitizen.buerger1.person.lastname}`)
    })
  
    allure.step('Urlauberfischereischein anlegen',()=>{
      serviceOverviewPage.getFishingLicenceButton().should('be.visible').click()
      serviceOverviewPage.getFishingLicenceCard().should('be.visible')
      serviceOverviewPage.getFishingLicenceVacation().should('be.visible').click()
      //TODO:update after implementation is ready
      //serviceOverviewPage.getFishingLicenceOther().should('be.visible')
      //serviceOverviewPage.getFishingLicenceCreate().should('be.visible').click()
    })
  
    allure.step('Akzeptiere GDPR', () => {
      consentDigitizePage.getGdprCheckbox().click().find('input').should('have.class', 'ng-valid');
      consentDigitizePage.getSelfDisclosureCheckbox().click().find('input').should('have.class', 'ng-valid');
      consentDigitizePage.getConsentPageContinueButton().should('be.enabled').click()
    });
  
    allure.step('Überprüfe der Personen Daten im Formular',()=>{
      digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().should('be.visible')
      digitizeLicencePage.getTabPersonalDataComponent().getUserDataForm()
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormFirstnameInput().should('have.value', testCitizen.buerger1.person.firstname)
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormLastnameInput().should('have.value', testCitizen.buerger1.person.lastname)
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormBirthDateInput().should('have.value', testCitizen.buerger1.person.birthdate)
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormBirthPlaceInput().should('have.value', testCitizen.buerger1.person.birthplace)
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormNationalityInput().should('have.value', testCitizen.buerger1.person.nationality)
      digitizeLicencePage.getEditFooterComponent().getFooterConfirmBox().should('be.visible').click()
      digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().click()
    })
  
    allure.step('Ausfüllen des Formulars für Zeitraum und Dauer',()=>{
      //TODO: update after implementation is ready
      //digitizeLicencePage.getDurationComponent().getDurationTab().should('be.visible')
      digitizeLicencePage.getDurationComponent().getDurationTable().should('be.visible')
      digitizeLicencePage.getDurationComponent().getDurationTablePeriodInput().should('be.visible').should('have.value',"28")
      digitizeLicencePage.getDurationComponent().getDurationTableStartDateInput().should('be.visible').type(currentDate)
      digitizeLicencePage.getDurationComponent().getDurationTableEndDateInput()
        .invoke('val')
        .then((startDate) => {
          digitizeLicencePage.getDurationComponent().getDurationTablePeriodInput()
            .invoke('val')
            .then((days) => {
              const numberOfDays = parseInt(days, 10);
              cy.calculateEndDate(startDate, numberOfDays).then((expectedEndDate) => {
                digitizeLicencePage.getDurationComponent().getDurationTableEndDateInput()
                  .invoke('val').should('eq', expectedEndDate);
              });
            });
        })
      digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().click()
    })
  
    allure.step('Fülle das Formular "Gebühren und Abgaben" aus',()=>{
      digitizeLicencePage.getTaxesComponent().getTaxesBoxHeader().should('be.visible').contains('37,00 €')
      digitizeLicencePage.getTaxesComponent().getTaxesNewLicence().should('be.visible').contains('20,00 €')
      digitizeLicencePage.getTaxesComponent().getTaxesNewLicenceCheckbox().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getStaticTaxValue().should('be.visible').contains('17,00 €')
      digitizeLicencePage.getTaxesComponent().getStaticTaxValueCheckbox().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getPaymentMethodCash().should('not.be.checked').click()
      digitizeLicencePage.getTaxesComponent().getPaymentMethodCard().should('be.visible')
      digitizeLicencePage.getEditFooterComponent().getFooterFinishButton().should('be.visible').click()
    })
  
    allure.step('Überprüfe und versende die erstellten Dokumente',()=>{
      cy.wait(2000)
      digitizeLicencePage.getDocumentsComponent().getVacationDocumentsPage().should('be.visible')
      digitizeLicencePage.getDocumentsComponent().getDocumentsBox().should('be.visible').contains('Digitale Dokumente')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(0).should('be.visible').contains('Urlauberfischereischein')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(1).should('be.visible').contains('Fischereiabgabe')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(2).should('be.visible').contains('E-Mail-Versand')
      digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(0).should('be.visible').contains('Drucken')
      digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(1).should('be.visible').contains('Drucken')
      digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(2).should('be.visible').contains('Senden').click()
      digitizeLicencePage.getDocumentsComponent().getDocumentsDialog().should('be.visible')
      digitizeLicencePage.getDocumentsComponent().getDocumentsEmailInput().should('be.visible').type('<EMAIL>')
      digitizeLicencePage.getDocumentsComponent().getDocumentsEmailConfirmInput().should('be.visible').type('<EMAIL>')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDialogSend().should('be.visible').click()
      digitizeLicencePage.getDocumentsComponent().getDocumentsSuccessContainer().should('be.visible').contains('Dokumente wurden versendet')
    })
  
    allure.step('Als SH Mitarbeiter abmelden', () => {
      headerComponent.getHeaderDropdownMenu().should('be.visible').click()
      headerComponent.getHeaderLogoutButton().should('be.visible').click() 
    })
  })

  it('Eine neue Fischereischein Prüfung und Ausländerfischereischein anlegen: vom Nutzer erstellt, mit 1 Jahr Gültigkeit ab heute, mit Verwaltungsgebühr, mit Abgabe, Kartenzahlung.',()=>{
    allure.story('Eine neue Fischereischein Prüfung und Ausländerfischereischein anlegen: vom Nutzer erstellt, mit 1 Jahr Gültigkeit ab heute, mit Verwaltungsgebühr, mit Abgabe, Kartenzahlung.');
    allure.severity('normal');
  
    allure.step('Als Prüfer anmelden', () => {
      allure.parameter("username", testUsers.user2.username);
      allure.parameter("userPassword", '');        
      cy.visit('/');
      loginPage.getLoginButton().click();
      externalLoginPage.getLoginPageUsernameInput().type(testUsers.user2.username);
      externalLoginPage.getLoginPagePasswordInput().type(testUsers.user2.password, {log: false});
      externalLoginPage.getLoginPageSubmitButton().click();
    });
  
    allure.step('Befüllen des Prüfungsformulars',()=>{
      examinationPage.getExaminationPage().should('be.visible')      
      examinationPage.getExamPageFormFirstname().should('be.visible').type(testCitizen.buerger1.person.firstname)
      examinationPage.getExamPageFormLastname().should('be.visible').type(testCitizen.buerger1.person.lastname)
      examinationPage.getExamPageFormBirthdate().should('be.visible').type(testCitizen.buerger1.person.birthdate)        
      examinationPage.getExamPageFormBirthplace().should('be.visible').type(testCitizen.buerger1.person.birthplace)
      examinationPage.getExamPageFormPassDate().should('be.visible').type("2009-01-01")
      examinationPage.getExamPageContinueButton().should('be.visible').click()
      examinationPage.getExamSubmitButton().should('be.visible').click()
      examinationPage.getSuccessPage().should('be.visible').contains("Erfolgreich angelegt!")
      examinationPage.getSuccessPage().should('be.visible').contains("Bestandene Prüfung wurde erfolgreich im Fischereiregister aufgenommen.")
      examinationPage.getSuccessEntries().invoke('text')
      .then((text) => {
        const match = text.match(/ZF\d{2}-\d{4}-\d{4}-\d{4}/);
        cy.wrap(match[0]).as('accessCode')
      });
      cy.get('@accessCode').then((accessCode) => {
        cy.log(`Extracted Code: ${accessCode}`);
        examinationPage.getSuccessEntries().should('be.visible').contains(accessCode)
      });
    }) 
  
    allure.step('Als Prüfer abmelden', () => {
      headerComponent.getHeaderDropdownMenu().should('be.visible').click()
      headerComponent.getHeaderLogoutButton().should('be.visible').click() 
    });
  
    allure.step('Als NRW Mitarbeiter anmelden', () => {
      allure.parameter("username", testUsers.user6.username);
      allure.parameter("userPassword", '');
      cy.visit('/');
      loginPage.getLoginButton().click();
      externalLoginPage.getLoginPageUsernameInput().type(testUsers.user6.username);
      externalLoginPage.getLoginPagePasswordInput().type(testUsers.user6.password, {log: false});
      externalLoginPage.getLoginPageSubmitButton().click();
    });
      
    allure.step('Suche nach LizenzID durchführen und Serviceübersicht öffnen', () => {
      cy.get('@accessCode').then((accessCode) => {
        searchResultsPage.getSearchBarInputComponent().getSearchbarComponentInput().should('be.visible')
          .type(accessCode);
      });
      homePage.getSearchBarInputComponent().getSearchbarComponentSearchButton().click();
      searchResultsPage.getSearchResultsComponent().getSearchResultsRowInpectionLink().should('be.visible').click()
      serviceOverviewPage.getUserProfileHeader().should('be.visible')
        .contains(`${testCitizen.buerger1.person.firstname} ${testCitizen.buerger1.person.lastname}`)
    })
      
    allure.step('Überprüfe den Bereich Fischereischein',()=>{        
      serviceOverviewPage.getFishingLicenceCard().should('be.visible')
      serviceOverviewPage.getFishingLicenceButton().should('be.visible').click()
      serviceOverviewPage.getVacationLicenceButton().should('be.visible').click()
    })
  
    allure.step('Akzeptiere GDPR', () => {
      consentDigitizePage.getGdprCheckbox().click().find('input').should('have.class', 'ng-valid');
      consentDigitizePage.getSelfDisclosureCheckbox().click().find('input').should('have.class', 'ng-valid');
      consentDigitizePage.getConsentPageContinueButton().should('be.enabled').click()
    });
  
    allure.step('Fülle das Formular "Personen Daten" aus',()=>{
      digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().should('be.visible')
      headerComponent.getAllTabTitleState().eq(0).should('have.class', 'active')
      headerComponent.getAllTabTitleState().eq(0).should('not.be.disabled')
      headerComponent.getAllTabTitleState().eq(1).should('have.class', 'inactive')
      headerComponent.getAllTabTitleState().eq(1).should('not.be.disabled')
      headerComponent.getAllTabTitleState().eq(2).should('have.class', 'inactive')
      headerComponent.getAllTabTitleState().eq(2).should('be.disabled')
      headerComponent.getAllTabTitleState().eq(3).should('have.class', 'inactive')
      headerComponent.getAllTabTitleState().eq(3).should('be.disabled')
      digitizeLicencePage.getTabPersonalDataComponent().getUserDataForm()
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormFirstnameInput().should('have.value', testCitizen.buerger1.person.firstname)
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormLastnameInput().should('have.value', testCitizen.buerger1.person.lastname)
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormBirthDateInput().should('have.value', testCitizen.buerger1.person.birthdate)
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormBirthPlaceInput().should('have.value', testCitizen.buerger1.person.birthplace)
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormNationalityInput().should('have.value', testCitizen.buerger1.person.nationality)
      digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().click()
    })
  
    allure.step('Fülle das Formular Zeitraum und Dauer aus',()=>{
      //TODO: update after implementation is ready
      //digitizeLicencePage.getDurationComponent().getDurationTab().should('be.visible')
      digitizeLicencePage.getDurationComponent().getDurationTable().should('be.visible')
      headerComponent.getAllTabTitleState().eq(0).should('have.class', 'inactive')
      headerComponent.getAllTabTitleState().eq(0).should('not.be.disabled')
      headerComponent.getAllTabTitleState().eq(1).should('have.class', 'active')
      headerComponent.getAllTabTitleState().eq(1).should('not.be.disabled')
      headerComponent.getAllTabTitleState().eq(2).should('have.class', 'inactive')
      headerComponent.getAllTabTitleState().eq(2).should('be.disabled')
      headerComponent.getAllTabTitleState().eq(3).should('have.class', 'inactive')
      headerComponent.getAllTabTitleState().eq(3).should('be.disabled')
      digitizeLicencePage.getDurationComponent().getDurationTablePeriodInput().should('be.visible').should('have.value',"1")
      digitizeLicencePage.getDurationComponent().getDurationTableStartDateInput().should('be.visible').type(currentDate)
      digitizeLicencePage.getDurationComponent().getDurationTableEndDateInput()
        .invoke('val')
        .then((startDate) => {
          digitizeLicencePage.getDurationComponent().getDurationTablePeriodInput()
            .invoke('val')
            .then((days) => {
              const numberOfDays = parseInt(days, 10);
              cy.calculateEndDate(startDate, numberOfDays).then((expectedEndDate) => {
                digitizeLicencePage.getDurationComponent().getDurationTableEndDateInput()
                  .invoke('val').should('eq', expectedEndDate);
              });
            });
        });
      digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().click()
    })
  
    allure.step('Fülle das Formular "Gebühren und Abgaben" aus',()=>{
      digitizeLicencePage.getTaxesComponent().getTaxesBoxHeader().should('be.visible').contains('26,00 €')
      digitizeLicencePage.getTaxesComponent().getTaxesNewLicence().should('be.visible').contains('14,00 €')
      
      headerComponent.getAllTabTitleState().eq(0).should('have.class', 'inactive')
      headerComponent.getAllTabTitleState().eq(0).should('not.be.disabled')
      headerComponent.getAllTabTitleState().eq(1).should('have.class', 'inactive')
      headerComponent.getAllTabTitleState().eq(1).should('not.be.disabled')
      headerComponent.getAllTabTitleState().eq(2).should('have.class', 'active')
      headerComponent.getAllTabTitleState().eq(2).should('not.be.disabled')
      headerComponent.getAllTabTitleState().eq(3).should('have.class', 'inactive')
      headerComponent.getAllTabTitleState().eq(3).should('be.disabled')
      digitizeLicencePage.getTaxesComponent().getTaxesNewLicenceCheckbox().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getStaticTaxValue().should('be.visible').contains('12,00 €')
      digitizeLicencePage.getTaxesComponent().getStaticTaxValueCheckbox().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getPaymentMethodCash().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getPaymentMethodCard().should('not.be.checked').click()
      digitizeLicencePage.getEditFooterComponent().getFooterFinishButton().should('be.visible').click()
    })
  
    allure.step('Überprüfe und versende die erstellten Dokumente',()=>{
      cy.wait(2000)
      digitizeLicencePage.getDocumentsComponent().getDocumentsBox().should('be.visible').contains('Digitale Dokumente')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(0).should('be.visible').contains('Ausländerfischereischein')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(1).should('be.visible').contains('Fischereiabgabe')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(2).should('be.visible').contains('E-Mail-Versand')
      digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(0).should('be.visible').contains('Drucken')
      digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(1).should('be.visible').contains('Drucken')
      headerComponent.getAllTabTitleState().eq(0).should('have.class', 'inactive')
      headerComponent.getAllTabTitleState().eq(0).should('be.disabled')
      headerComponent.getAllTabTitleState().eq(1).should('have.class', 'inactive')
      headerComponent.getAllTabTitleState().eq(1).should('be.disabled')
      headerComponent.getAllTabTitleState().eq(2).should('have.class', 'inactive')
      headerComponent.getAllTabTitleState().eq(2).should('be.disabled')
      headerComponent.getAllTabTitleState().eq(3).should('have.class', 'active')
      headerComponent.getAllTabTitleState().eq(3).should('not.be.disabled')
      digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(2).should('be.visible').contains('Senden').click()
      digitizeLicencePage.getDocumentsComponent().getDocumentsDialog().should('be.visible')
      digitizeLicencePage.getDocumentsComponent().getDocumentsEmailInput().should('be.visible').type('<EMAIL>')
      digitizeLicencePage.getDocumentsComponent().getDocumentsEmailConfirmInput().should('be.visible').type('<EMAIL>')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDialogSend().should('be.visible').click()
      digitizeLicencePage.getDocumentsComponent().getDocumentsSuccessContainer().should('be.visible').contains('Dokumente wurden versendet')
    })
  
    allure.step('Als NRW Mitarbeiter abmelden', () => {
      headerComponent.getHeaderDropdownMenu().should('be.visible').click()
      headerComponent.getHeaderLogoutButton().should('be.visible').click() 
    })
  })

  it('Eine neue Fischereischein Prüfung und Ausländerfischereischein anlegen: vom Dritten erstellt, mit 1 Jahr Gültigkeit ab heute, mit Verwaltungsgebühr, mit Abgabe, Kartenzahlung.',()=>{
    allure.story('Eine neue Fischereischein Prüfung und Ausländerfischereischein anlegen: vom Dritten erstellt, mit 1 Jahr Gültigkeit ab heute, mit Verwaltungsgebühr, mit Abgabe, Kartenzahlung.');
    allure.severity('normal');
  
    allure.step('Als Prüfer anmelden', () => {
      allure.parameter("username", testUsers.user2.username);
      allure.parameter("userPassword", '');
      cy.visit('/');
      loginPage.getLoginButton().click();
      externalLoginPage.getLoginPageUsernameInput().type(testUsers.user2.username);
      externalLoginPage.getLoginPagePasswordInput().type(testUsers.user2.password, {log: false});
      externalLoginPage.getLoginPageSubmitButton().click();
    });
  
    allure.step('Befüllen des Prüfungsformulars',()=>{
      examinationPage.getExaminationPage().should('be.visible')      
      examinationPage.getExamPageFormFirstname().should('be.visible').type(testCitizen.buerger1.person.firstname)
      examinationPage.getExamPageFormLastname().should('be.visible').type(testCitizen.buerger1.person.lastname)
      examinationPage.getExamPageFormBirthdate().should('be.visible').type(testCitizen.buerger1.person.birthdate)        
      examinationPage.getExamPageFormBirthplace().should('be.visible').type(testCitizen.buerger1.person.birthplace)
      examinationPage.getExamPageFormPassDate().should('be.visible').type("2009-01-01")
      examinationPage.getExamPageContinueButton().should('be.visible').click()
      examinationPage.getExamSubmitButton().should('be.visible').click()
      examinationPage.getSuccessPage().should('be.visible').contains("Erfolgreich angelegt!")
      examinationPage.getSuccessPage().should('be.visible').contains("Bestandene Prüfung wurde erfolgreich im Fischereiregister aufgenommen.")
      examinationPage.getSuccessEntries().invoke('text')
      .then((text) => {
        const match = text.match(/ZF\d{2}-\d{4}-\d{4}-\d{4}/);
        cy.wrap(match[0]).as('accessCode')
      });
      cy.get('@accessCode').then((accessCode) => {
        cy.log(`Extracted Code: ${accessCode}`);
        examinationPage.getSuccessEntries().should('be.visible').contains(accessCode)
      });
    }) 
  
    allure.step('Als Prüfer abmelden', () => {
      headerComponent.getHeaderDropdownMenu().should('be.visible').click()
      headerComponent.getHeaderLogoutButton().should('be.visible').click() 
    });
  
    allure.step('Als NRW Mitarbeiter anmelden', () => {
      allure.parameter("username", testUsers.user6.username);
      allure.parameter("userPassword", '');
      cy.visit('/');
      loginPage.getLoginButton().click();
      externalLoginPage.getLoginPageUsernameInput().type(testUsers.user6.username);
      externalLoginPage.getLoginPagePasswordInput().type(testUsers.user6.password, {log: false});
      externalLoginPage.getLoginPageSubmitButton().click();
    });
      
    allure.step('Suche nach LizenzID durchführen und Serviceübersicht öffnen', () => {
      cy.get('@accessCode').then((accessCode) => {
        searchResultsPage.getSearchBarInputComponent().getSearchbarComponentInput().should('be.visible')
          .type(accessCode);
      });
      homePage.getSearchBarInputComponent().getSearchbarComponentSearchButton().click();
      searchResultsPage.getSearchResultsComponent().getSearchResultsRowInpectionLink().should('be.visible').click()
      serviceOverviewPage.getUserProfileHeader().should('be.visible')
        .contains(`${testCitizen.buerger1.person.firstname} ${testCitizen.buerger1.person.lastname}`)
    })
      
    allure.step('Überprüfe den Bereich Fischereischein',()=>{
      serviceOverviewPage.getFishingLicenceCard().should('be.visible')
      serviceOverviewPage.getFishingLicenceButton().should('be.visible').click()
      serviceOverviewPage.getVacationLicenceButton().should('be.visible').click()
    })
  
    allure.step('Akzeptiere GDPR', () => {
      consentDigitizePage.getGdprCheckbox().click().find('input').should('have.class', 'ng-valid');
      consentDigitizePage.getSelfDisclosureCheckbox().click().find('input').should('have.class', 'ng-valid');
      consentDigitizePage.getConsentPageContinueButton().should('be.enabled').click()
    });
  
    allure.step('Fülle das Formular "Personen Daten" aus',()=>{
      digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().should('be.visible')
      digitizeLicencePage.getTabPersonalDataComponent().getUserDataForm()
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormFirstnameInput().should('have.value', testCitizen.buerger1.person.firstname)
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormLastnameInput().should('have.value', testCitizen.buerger1.person.lastname)
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormBirthDateInput().should('have.value', testCitizen.buerger1.person.birthdate)
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormBirthPlaceInput().should('have.value', testCitizen.buerger1.person.birthplace)
      digitizeLicencePage.getTabPersonalDataComponent().getPersonalDataFormNationalityInput().should('have.value', testCitizen.buerger1.person.nationality)
      digitizeLicencePage.getEditFooterComponent().getFooterConfirmBox().should('be.visible').click()
      digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().click()
    })
  
    allure.step('Fülle das Formular Zeitraum und Dauer aus',()=>{
      //TODO: update after implementation is ready
      //digitizeLicencePage.getDurationComponent().getDurationTab().should('be.visible')
      digitizeLicencePage.getDurationComponent().getDurationTable().should('be.visible')
      digitizeLicencePage.getDurationComponent().getDurationTablePeriodInput().should('be.visible').should('have.value',"1")
      digitizeLicencePage.getDurationComponent().getDurationTableStartDateInput().should('be.visible').type(currentDate)
      digitizeLicencePage.getDurationComponent().getDurationTableEndDateInput()
        .invoke('val')
        .then((startDate) => {
          digitizeLicencePage.getDurationComponent().getDurationTablePeriodInput()
            .invoke('val')
            .then((days) => {
              const numberOfDays = parseInt(days, 10);
              cy.calculateEndDate(startDate, numberOfDays).then((expectedEndDate) => {
                digitizeLicencePage.getDurationComponent().getDurationTableEndDateInput()
                  .invoke('val').should('eq', expectedEndDate);
              });
            });
        });
      digitizeLicencePage.getEditFooterComponent().getFooterContinueIcon().click()
    })
  
    allure.step('Fülle das Formular "Gebühren und Abgaben" aus',()=>{
      digitizeLicencePage.getTaxesComponent().getTaxesBoxHeader().should('be.visible').contains('26,00 €')
      digitizeLicencePage.getTaxesComponent().getTaxesNewLicence().should('be.visible').contains('14,00 €')
      digitizeLicencePage.getTaxesComponent().getTaxesNewLicenceCheckbox().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getStaticTaxValue().should('be.visible').contains('12,00 €')
      digitizeLicencePage.getTaxesComponent().getStaticTaxValueCheckbox().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getPaymentMethodCash().should('be.visible')
      digitizeLicencePage.getTaxesComponent().getPaymentMethodCard().should('not.be.checked').click()
      digitizeLicencePage.getEditFooterComponent().getFooterFinishButton().should('be.visible').click()
    })
  
    allure.step('Überprüfe und versende die erstellten Dokumente',()=>{
      cy.wait(2000)
      digitizeLicencePage.getDocumentsComponent().getDocumentsBox().should('be.visible').contains('Digitale Dokumente')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(0).should('be.visible').contains('Ausländerfischereischein')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(1).should('be.visible').contains('Fischereiabgabe')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDescription().eq(2).should('be.visible').contains('E-Mail-Versand')
      digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(0).should('be.visible').contains('Drucken')
      digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(1).should('be.visible').contains('Drucken')
      digitizeLicencePage.getDocumentsComponent().getDocumentsActions().eq(2).should('be.visible').contains('Senden').click()
      digitizeLicencePage.getDocumentsComponent().getDocumentsDialog().should('be.visible')
      digitizeLicencePage.getDocumentsComponent().getDocumentsEmailInput().should('be.visible').type('<EMAIL>')
      digitizeLicencePage.getDocumentsComponent().getDocumentsEmailConfirmInput().should('be.visible').type('<EMAIL>')
      digitizeLicencePage.getDocumentsComponent().getDocumentsDialogSend().should('be.visible').click()
      digitizeLicencePage.getDocumentsComponent().getDocumentsSuccessContainer().should('be.visible').contains('Dokumente wurden versendet')
    })
  
    allure.step('Als NRW Mitarbeiter abmelden', () => {
      headerComponent.getHeaderDropdownMenu().should('be.visible').click()
      headerComponent.getHeaderLogoutButton().should('be.visible').click() 
    })
  })
})