<table class="w-full border-collapse">
  <caption></caption>
  <tr class="border-b border-border-divider-alternative odd:bg-background even:bg-background-2">
    <th class="bg-background px-4 py-2 text-left font-bold" [innerText]="'register_file.titles.data_table.field' | translate"></th>
    <th class="bg-background px-4 py-2 text-left font-bold" [innerText]="'register_file.titles.data_table.value' | translate"></th>
  </tr>
  <tr
    fish-register-file-table-row
    [cells]="['register_file.labels.filed_process.filed_process_data.qualifications_proof.type' | translate, data().type]"
  ></tr>
  <tr
    fish-register-file-table-row
    [cells]="[
      'register_file.labels.filed_process.filed_process_data.qualifications_proof.fishing_certificate_id' | translate,
      data().fishingCertificateId,
    ]"
  ></tr>
  <tr
    fish-register-file-table-row
    [cells]="[
      'register_file.labels.filed_process.filed_process_data.qualifications_proof.other_form_of_proof_id' | translate,
      data().otherFormOfProofId,
    ]"
  ></tr>
  <tr
    fish-register-file-table-row
    [cells]="['register_file.labels.filed_process.filed_process_data.qualifications_proof.federal_state' | translate, data().federalState]"
  ></tr>
  <tr
    fish-register-file-table-row
    [cells]="['register_file.labels.filed_process.filed_process_data.qualifications_proof.passed_on' | translate, data().passedOn]"
  ></tr>
  <tr
    fish-register-file-table-row
    [cells]="['register_file.labels.filed_process.filed_process_data.qualifications_proof.issued_by' | translate, data().issuedBy]"
  ></tr>
</table>
