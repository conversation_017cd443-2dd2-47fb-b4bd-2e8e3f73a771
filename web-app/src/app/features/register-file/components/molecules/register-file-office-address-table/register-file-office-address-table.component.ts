import { ChangeDetectionStrategy, Component, input } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';

import { AddressFP } from '@digifischdok/ngx-register-sdk';

import { RegisterFileTableRowComponent } from '@/app/features/register-file/components/atoms/register-file-table-row/register-file-table-row.component';

@Component({
  selector: 'fish-register-file-office-address-table',
  imports: [TranslateModule, RegisterFileTableRowComponent],
  templateUrl: './register-file-office-address-table.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RegisterFileOfficeAddressTableComponent {
  public readonly data = input.required<AddressFP>();
}
