import { ChangeDetectionStrategy, Component, input } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';

import { ValidityPeriodFP } from '@digifischdok/ngx-register-sdk';

import { RegisterFileTableRowComponent } from '@/app/features/register-file/components/atoms/register-file-table-row/register-file-table-row.component';

@Component({
  selector: 'fish-register-file-validity-periods-table',
  imports: [RegisterFileTableRowComponent, TranslateModule],
  templateUrl: './register-file-validity-periods-table.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RegisterFileValidityPeriodsTableComponent {
  public readonly data = input.required<ValidityPeriodFP[]>();
}
