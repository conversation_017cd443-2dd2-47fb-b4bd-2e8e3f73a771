<table class="w-full border-collapse">
  <caption></caption>
  <tr class="border-b border-border-divider-alternative odd:bg-background even:bg-background-2">
    <th
      class="bg-background px-4 py-2 text-left font-bold"
      [innerText]="'register_file.labels.filed_process.filed_process_data.taxes.tax_id' | translate"
    ></th>
    <th
      class="bg-background px-4 py-2 text-left font-bold"
      [innerText]="'register_file.labels.filed_process.filed_process_data.taxes.federal_state' | translate"
    ></th>
    <th
      class="bg-background px-4 py-2 text-left font-bold"
      [innerText]="'register_file.labels.filed_process.filed_process_data.taxes.valid_from' | translate"
    ></th>
    <th
      class="bg-background px-4 py-2 text-left font-bold"
      [innerText]="'register_file.labels.filed_process.filed_process_data.taxes.valid_to' | translate"
    ></th>
    <th
      class="bg-background px-4 py-2 text-left font-bold"
      [innerText]="'register_file.labels.filed_process.filed_process_data.taxes.payment_info.amount' | translate"
    ></th>
    <th
      class="bg-background px-4 py-2 text-left font-bold"
      [innerText]="'register_file.labels.filed_process.filed_process_data.taxes.payment_info.type' | translate"
    ></th>
  </tr>
  @for (dataEntry of data(); track dataEntry) {
    <tr
      fish-register-file-table-row
      [cells]="[
        dataEntry.taxId,
        dataEntry.federalState,
        dataEntry.validFrom,
        dataEntry?.validTo,
        dataEntry.paymentInfo.amount,
        dataEntry.paymentInfo.type,
      ]"
    ></tr>
  }
</table>
