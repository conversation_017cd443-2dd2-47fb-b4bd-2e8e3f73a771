<div class="flex flex-col gap-10">
  @for (process of processes(); track process.processTimestamp) {
    <div>
      <h2 [innerText]="getTranslatedProcessType$(process.processType) | async" class="mb-4 mt-2 text-[30px] text-action-primary"></h2>
      <h3 [innerText]="'register_file.titles.head' | translate" class="mb-2 mt-6 text-[20px] font-bold"></h3>
      <fish-register-file-process-head-table [data]="process"></fish-register-file-process-head-table>
      @if (process.filedProcessData.person) {
        <h3 [innerText]="'register_file.titles.person' | translate" class="mb-2 mt-6 text-[20px] font-bold"></h3>
        <fish-register-file-person-table
          [data]="personalDataWithServiceAccountId(process.filedProcessData.person, process.filedProcessData.serviceAccountId)"
        ></fish-register-file-person-table>
      }
      @if (process.filedProcessData.person?.officeAddress) {
        <h3 [innerText]="'register_file.titles.office_address' | translate" class="mb-2 mt-6 text-[20px] font-bold"></h3>
        <fish-register-file-office-address-table [data]="process.filedProcessData.person!.officeAddress!"></fish-register-file-office-address-table>
      }
      @if (process.filedProcessData.qualificationsProof) {
        <h3 [innerText]="'register_file.titles.qualification_proof' | translate" class="mb-2 mt-6 text-[20px] font-bold"></h3>
        <fish-register-file-qualifications-proof-table
          [data]="process.filedProcessData.qualificationsProof"
        ></fish-register-file-qualifications-proof-table>
      }
      @if (process.filedProcessData.fees && process.filedProcessData.fees.length > 0) {
        <h3 [innerText]="'register_file.titles.fees' | translate" class="mb-2 mt-6 text-[20px] font-bold"></h3>
        <fish-register-file-fees-table [data]="process.filedProcessData.fees"></fish-register-file-fees-table>
      }
      @if (process.filedProcessData.taxes && process.filedProcessData.taxes.length > 0) {
        <h3 [innerText]="'register_file.titles.taxes' | translate" class="mb-2 mt-6 text-[20px] font-bold"></h3>
        <fish-register-file-taxes-table [data]="process.filedProcessData.taxes"></fish-register-file-taxes-table>
      }
      @if (process.filedProcessData.fishingLicense) {
        <h3 [innerText]="'register_file.titles.fishing_license' | translate" class="mb-2 mt-6 text-[20px] font-bold"></h3>
        <fish-register-file-fishing-license-table [data]="process.filedProcessData.fishingLicense"></fish-register-file-fishing-license-table>
      }
      @if (process.filedProcessData.fishingLicense?.validityPeriods && process.filedProcessData.fishingLicense!.validityPeriods.length > 0) {
        <h3 [innerText]="'register_file.titles.validity_periods' | translate" class="mb-2 mt-6 text-[20px] font-bold"></h3>
        <fish-register-file-validity-periods-table
          [data]="process.filedProcessData.fishingLicense!.validityPeriods"
        ></fish-register-file-validity-periods-table>
      }
      @if (process.filedProcessData.ban) {
        <h3 [innerText]="'register_file.titles.ban' | translate" class="mb-2 mt-6 text-[20px] font-bold"></h3>
        <fish-register-file-ban-table [data]="process.filedProcessData.ban"></fish-register-file-ban-table>
      }
      @if (process.filedProcessData.consentInfo) {
        <h3 [innerText]="'register_file.titles.consent_info' | translate" class="mb-2 mt-6 text-[20px] font-bold"></h3>
        <fish-register-file-consent-info-table [data]="process.filedProcessData.consentInfo"></fish-register-file-consent-info-table>
      }
      @if (process.filedProcessData.identificationDocuments && process.filedProcessData.identificationDocuments.length > 0) {
        <h3 [innerText]="'register_file.titles.identification_documents' | translate" class="mb-2 mt-6 text-[20px] font-bold"></h3>
        <fish-register-file-identification-documents-table
          [data]="process.filedProcessData.identificationDocuments"
        ></fish-register-file-identification-documents-table>
      }
      @if (process.filedProcessData.deletionFlag) {
        <h3 [innerText]="'register_file.titles.marked_for_deletion' | translate" class="mb-2 mt-6 text-[20px] font-bold"></h3>
        <fish-register-file-deletion-flag-table [data]="process.filedProcessData.deletionFlag"></fish-register-file-deletion-flag-table>
      }
    </div>
  }
</div>
