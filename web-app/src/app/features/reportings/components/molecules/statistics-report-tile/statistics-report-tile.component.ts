import { ChangeDetectionStrategy, Component, input, output } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';

import { HistoricalBarChartComponent } from '@/app/features/reportings/components/atoms/historical-bar-chart/historical-bar-chart.component';
import { HistoricalBarChartValue } from '@/app/features/reportings/components/atoms/historical-bar-chart/historical-bar-chart.model.models';
import { Statistic } from '@/app/features/reportings/models/statistic.model';
import { ButtonComponent } from '@/app/shared/atoms/button/button.component';
import { FocusRingComponent } from '@/app/shared/atoms/focus-ring/focus-ring.component';
import { IconDownloadComponent } from '@/app/shared/icons/download/download.component';
import { AmountPipe } from '@/app/shared/pipes/amount.pipe';

@Component({
  selector: 'fish-statistics-report-tile',
  standalone: true,
  imports: [FocusRingComponent, ButtonComponent, IconDownloadComponent, AmountPipe, TranslateModule, HistoricalBarChartComponent],
  templateUrl: './statistics-report-tile.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StatisticsReportTileComponent {
  // Inputs
  public titleText = input<string>();

  public titleSubtext = input<string>();

  public primaryStatisticNote = input<string>();

  public primaryStatistic = input<Statistic>();

  public secondaryStatistic = input<Statistic>();

  public tertiaryStatistics = input<Array<Statistic>>();

  public chartData = input<Array<HistoricalBarChartValue>>();

  public isDownloadButtonLoading = input<boolean>(false);

  // Outputs
  public readonly downloadButtonClicked = output<void>();
}
