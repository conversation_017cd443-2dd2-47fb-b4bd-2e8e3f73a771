<fish-focus-ring shadowClass="rounded-lg" class="w-full rounded">
  <div class="group flex w-full flex-col items-stretch divide-y divide-border-divider overflow-hidden rounded-lg shadow-glass-white-tint">
    <div class="flex flex-row items-center gap-2 bg-background p-2">
      <ng-content select="[header-icon]"></ng-content>
      <div class="max-w-[calc(100%-105px)] flex-col leading-5">
        @if (titleText()) {
          <div class="truncate font-bold" [innerText]="titleText()" data-testid="statistics-report-tile-title"></div>
        }
        @if (titleSubtext()) {
          <div [innerText]="titleSubtext()" data-testid="statistics-report-tile-subtitle"></div>
        }
      </div>
      <div class="grow"></div>
      <fish-button
        type="secondary"
        [loading]="isDownloadButtonLoading()"
        (clicked)="downloadButtonClicked.emit()"
        data-testid="statistics-report-tile-export-button"
      >
        <fish-icon-download class="-mx-2" size="32"></fish-icon-download>
      </fish-button>
    </div>

    <div class="flex flex-col bg-background px-4 py-2">
      <span
        class="leading-0 py-0 font-bold"
        [innerText]="primaryStatistic()?.label"
        data-testid="statistics-report-tile-primary-statistic-label"
      ></span>
      @if (primaryStatisticNote()) {
        <p class="mb-2 text-font-secondary" [innerHTML]="primaryStatisticNote()" data-testid="statistics-report-tile-primary-statistic-note"></p>
      }
      <span
        class="mb-2 text-l font-thin leading-8"
        [innerText]="primaryStatistic()?.value | amount: primaryStatistic()?.amountType"
        data-testid="statistics-report-tile-primary-statistic-value"
      ></span>
    </div>

    @if (secondaryStatistic()) {
      <div class="flex flex-row justify-between bg-background px-4 py-1 text-font-secondary">
        <span [innerText]="secondaryStatistic()?.label" data-testid="statistics-report-tile-secondary-statistic-label"></span>
        <span
          class="leading-0 py-0 font-bold"
          [innerText]="secondaryStatistic()?.value | amount: secondaryStatistic()?.amountType"
          data-testid="statistics-report-tile-secondary-statistic-value"
        ></span>
      </div>
    }

    @if (tertiaryStatistics()) {
      <div class="flex w-full flex-col bg-background px-4 py-2 text-font-secondary">
        @for (statistic of tertiaryStatistics(); track statistic.label) {
          <div class="flex justify-between">
            <span [innerText]="statistic.label" data-testid="statistics-report-tile-tertiary-statistic-label"></span>
            <span
              class="leading-0 py-0 font-bold"
              [innerText]="statistic.value | amount: statistic.amountType"
              data-testid="statistics-report-tile-tertiary-statistic-value"
            ></span>
          </div>
        }
      </div>
    }

    <div class="flex flex-col bg-background-2 p-4">
      <fish-historical-bar-chart [data]="chartData()"></fish-historical-bar-chart>
    </div>

    <ng-content select="[table-section]"></ng-content>
  </div>
</fish-focus-ring>
