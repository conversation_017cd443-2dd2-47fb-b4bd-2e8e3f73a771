import { ChangeDetectionStrategy, Component, Signal, computed, inject, input, output } from '@angular/core';

import { TranslateModule, TranslateService } from '@ngx-translate/core';

import { TaxesStatistics } from '@digifischdok/ngx-register-sdk';
import { TaxesStatisticsDataInner } from '@digifischdok/ngx-register-sdk/lib/model/taxesStatisticsDataInner';

import { HistoricalBarChartValue } from '@/app/features/reportings/components/atoms/historical-bar-chart/historical-bar-chart.model.models';
import { StatisticsBySubmissionTypeTableComponent } from '@/app/features/reportings/components/molecules/statistics-by-submission-type-table/statistics-by-submission-type-table.component';
import { StatisticsBySubmissionTypeTableData } from '@/app/features/reportings/components/molecules/statistics-by-submission-type-table/statistics-by-submission-type-table.models';
import { StatisticsReportTileComponent } from '@/app/features/reportings/components/molecules/statistics-report-tile/statistics-report-tile.component';
import { Statistic } from '@/app/features/reportings/models/statistic.model';
import { fadeInAnimation } from '@/app/shared/animations/fade.animations';
import { IconFishingTaxComponent } from '@/app/shared/icons/fisching-tax/fishing-tax.component';

@Component({
  selector: 'fish-taxes-statistics-report-tile',
  standalone: true,
  imports: [StatisticsBySubmissionTypeTableComponent, StatisticsReportTileComponent, IconFishingTaxComponent, TranslateModule],
  templateUrl: './taxes-statistics-report-tile.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  animations: [fadeInAnimation],
})
export class TaxesStatisticsReportTileComponent {
  // Inputs
  public statistics = input<Array<TaxesStatistics>>();

  public isDownloadButtonLoading = input<boolean>(false);

  // Outputs
  public readonly downloadButtonClicked = output<void>();

  // Dependencies
  private readonly translate = inject(TranslateService);

  // Computed signals
  protected readonly primaryStatistic: Signal<Statistic | undefined> = computed(() => {
    const lastYearStatistics = this.lastYearStatistics();
    if (!lastYearStatistics) return undefined;

    return {
      label: lastYearStatistics.year,
      value: this.calculateTotalRevenue(lastYearStatistics.data),
      amountType: 'money',
    };
  });

  protected readonly secondaryStatistic: Signal<Statistic | undefined> = computed(() => {
    const penultimateYearStatistics = this.penultimateYearStatistics();
    if (!penultimateYearStatistics) return undefined;

    return {
      label: penultimateYearStatistics.year,
      value: this.calculateTotalRevenue(penultimateYearStatistics.data),
      amountType: 'money',
    };
  });

  protected readonly tertiaryStatistics: Signal<Statistic[] | undefined> = computed(() => {
    const lastYearStatistics = this.lastYearStatistics();
    if (!lastYearStatistics) return undefined;

    return [
      {
        label: this.translate.instant('statistics.tile.taxes_and_fees.duration_one_year'),
        value: this.getCountByDuration(lastYearStatistics.data, (duration) => duration === 1),
        amountType: 'unit',
      },
      {
        label: this.translate.instant('statistics.tile.taxes_and_fees.duration_multiple_years'),
        value: this.getCountByDuration(lastYearStatistics.data, (duration) => duration > 1),
        amountType: 'unit',
      },
    ];
  });

  protected readonly yearData: Signal<StatisticsBySubmissionTypeTableData | undefined> = computed(() => {
    const lastYearStatistics = this.lastYearStatistics();
    if (!lastYearStatistics) return undefined;

    return {
      year: lastYearStatistics.year,
      analog: this.getCountBySubmissionType(lastYearStatistics.data, 'ANALOG'),
      online: this.getCountBySubmissionType(lastYearStatistics.data, 'ONLINE'),
    };
  });

  protected readonly previousYearData: Signal<StatisticsBySubmissionTypeTableData | undefined> = computed(() => {
    const penultimateYearStatistics = this.penultimateYearStatistics();
    if (!penultimateYearStatistics) return undefined;

    return {
      year: penultimateYearStatistics.year,
      analog: this.getCountBySubmissionType(penultimateYearStatistics.data, 'ANALOG'),
      online: this.getCountBySubmissionType(penultimateYearStatistics.data, 'ONLINE'),
    };
  });

  protected readonly chartData: Signal<HistoricalBarChartValue[]> = computed(() => {
    return [...this.sortedByYearStatistics()].reverse().map((stat) => ({
      year: stat.year,
      value: this.calculateTotalRevenue(stat.data),
    }));
  });

  private readonly sortedByYearStatistics: Signal<TaxesStatistics[]> = computed(() => {
    return [...(this.statistics() ?? [])].sort((a, b) => b.year - a.year);
  });

  // the most recent year of the inputted statistics
  private readonly lastYearStatistics: Signal<TaxesStatistics | null> = computed(() => {
    const sorted = this.sortedByYearStatistics();
    return sorted[0] ?? null;
  });

  // the second most recent year of the inputted statistics
  private readonly penultimateYearStatistics: Signal<TaxesStatistics | null> = computed(() => {
    const sorted = this.sortedByYearStatistics();
    return sorted.length > 1 ? sorted[1] : null;
  });

  private calculateTotalRevenue(data: TaxesStatisticsDataInner[]): number {
    return data.reduce((sum, item) => sum + item.revenue, 0);
  }

  private getCountBySubmissionType(data: TaxesStatisticsDataInner[], type: string): number {
    return data.filter((item) => item.submissionType === type).reduce((sum, item) => sum + item.count, 0);
  }

  private getCountByDuration(data: TaxesStatisticsDataInner[], condition: (duration: number) => boolean): number {
    return data.filter((d) => condition(d.duration)).reduce((sum, item) => sum + item.count, 0);
  }
}
