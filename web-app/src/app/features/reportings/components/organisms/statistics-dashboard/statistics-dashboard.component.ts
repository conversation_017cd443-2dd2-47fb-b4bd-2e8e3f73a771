import { HttpResponse } from '@angular/common/http';
import { ChangeDetectionStrategy, Component, computed, inject, signal } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';
import { catchError, finalize, of } from 'rxjs';

import { LicenseType, StatisticsExportService, StatisticsService } from '@digifischdok/ngx-register-sdk';

import { BansStatisticsReportTileComponent } from '@/app/features/reportings/components/organisms/bans-statistics-report-tile/bans-statistics-report-tile.component';
import { CertificationsStatisticsReportTileComponent } from '@/app/features/reportings/components/organisms/certifications-statistics-report-tile/certifications-statistics-report-tile.component';
import { ErrorsStatisticsReportTileComponent } from '@/app/features/reportings/components/organisms/errors-statistics-report-tile/errors-statistics-report-tile.component';
import { InspectionsStatisticsReportTileComponent } from '@/app/features/reportings/components/organisms/inspections-statistics-report-tile/inspections-statistics-report-tile.component';
import { LicensesStatisticsReportTileComponent } from '@/app/features/reportings/components/organisms/licenses-statistics-report-tile/licenses-statistics-report-tile.component';
import { StatisticsDashboardFilterComponent } from '@/app/features/reportings/components/organisms/statistics-dashboard-filter/statistics-dashboard-filter.component';
import {
  ALL_CERTIFICATE_ISSUERS_FILTER_OPTION,
  ALL_OFFICES_FILTER_OPTION,
  StatisticsDashboardFilterData,
  StatisticsDashboardFilterType,
} from '@/app/features/reportings/components/organisms/statistics-dashboard-filter/statistics-dashboard-filter.models';
import { TaxesStatisticsReportTileComponent } from '@/app/features/reportings/components/organisms/taxes-statistics-report-tile/taxes-statistics-report-tile.component';
import { ButtonComponent } from '@/app/shared/atoms/button/button.component';
import { IconDownloadComponent } from '@/app/shared/icons/download/download.component';
import { ServerDialogService } from '@/app/shared/services/server-dialog.service';
import { numberRange } from '@/app/shared/utils/array.utils';
import { toComputed } from '@/app/shared/utils/rxJsInterop.utils';

@Component({
  selector: 'fish-statistics-dashboard',
  standalone: true,
  imports: [
    ButtonComponent,
    IconDownloadComponent,
    StatisticsDashboardFilterComponent,
    TaxesStatisticsReportTileComponent,
    LicensesStatisticsReportTileComponent,
    CertificationsStatisticsReportTileComponent,
    InspectionsStatisticsReportTileComponent,
    TranslateModule,
    BansStatisticsReportTileComponent,
    ErrorsStatisticsReportTileComponent,
  ],
  templateUrl: './statistics-dashboard.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StatisticsDashboardComponent {
  // Constants
  protected readonly StatisticsDashboardFilterType = StatisticsDashboardFilterType;

  protected readonly LicenseType = LicenseType;

  private readonly YEARS_TO_QUERY_AMOUNT = 6;

  // Dependencies
  private readonly statisticsService = inject(StatisticsService);

  private readonly statisticsExportService = inject(StatisticsExportService);

  private readonly serverDialogService = inject(ServerDialogService);

  // Signals
  protected readonly filterData = signal<StatisticsDashboardFilterData | null>(null);

  // Loading signals
  protected readonly isTaxesExportLoading = signal<boolean>(false);

  protected readonly isRegularLicensesExportLoading = signal<boolean>(false);

  protected readonly isVacationLicensesExportLoading = signal<boolean>(false);

  protected readonly isLimitedLicensesExportLoading = signal<boolean>(false);

  protected readonly isCertificationsExportLoading = signal<boolean>(false);

  protected readonly isInspectionsExportLoading = signal<boolean>(false);

  protected readonly isBansExportLoading = signal<boolean>(false);

  protected readonly isErrorsExportLoading = signal<boolean>(false);

  protected readonly typeFilter = computed(() => this.filterData()?.type);

  private readonly yearFilter = computed(() => this.filterData()?.year);

  private readonly federalStateFilter = computed(() => this.filterData()?.federalState);

  private readonly officeFilter = computed(() => this.filterData()?.office);

  private readonly certificateIssuerFilter = computed(() => this.filterData()?.certificateIssuer);

  protected readonly taxesStatistics = toComputed(() => {
    const year = this.yearFilter();
    const state = this.federalStateFilter();
    const office = this.officeFilter();

    if (!year || !state) {
      return of([]);
    }

    const yearsToQuery = this.generateYearsToQuery(year);
    const officeParam = office && office !== ALL_OFFICES_FILTER_OPTION ? office : undefined;

    return this.statisticsService.statisticsControllerGetTaxesStatistics(yearsToQuery, officeParam, state);
  });

  protected readonly regularLicensesStatistics = toComputed(() => {
    const year = this.yearFilter();
    const state = this.federalStateFilter();
    const office = this.officeFilter();

    if (!year || !state) {
      return of([]);
    }

    const yearsToQuery = this.generateYearsToQuery(year);
    const officeParam = office && office !== ALL_OFFICES_FILTER_OPTION ? office : undefined;

    return this.statisticsService.statisticsControllerGetRegularLicensesStatistics(yearsToQuery, officeParam, state);
  });

  protected readonly vacationLicensesStatistics = toComputed(() => {
    const year = this.yearFilter();
    const state = this.federalStateFilter();
    const office = this.officeFilter();

    if (!year || !state) {
      return of([]);
    }

    const yearsToQuery = this.generateYearsToQuery(year);
    const officeParam = office && office !== ALL_OFFICES_FILTER_OPTION ? office : undefined;

    return this.statisticsService.statisticsControllerGetVacationLicensesStatistics(yearsToQuery, officeParam, state);
  });

  protected readonly limitedLicensesStatistics = toComputed(() => {
    const year = this.yearFilter();
    const state = this.federalStateFilter();
    const office = this.officeFilter();

    if (!year || !state) {
      return of([]);
    }

    const yearsToQuery = this.generateYearsToQuery(year);
    const officeParam = office && office !== ALL_OFFICES_FILTER_OPTION ? office : undefined;

    return this.statisticsService.statisticsControllerGetLimitedLicensesStatistics(yearsToQuery, officeParam, state);
  });

  protected readonly certificationsStatistics = toComputed(() => {
    const year = this.yearFilter();
    const state = this.federalStateFilter();
    const certificateIssuer = this.certificateIssuerFilter();

    if (!year || !state) {
      return of([]);
    }

    const yearsToQuery = this.generateYearsToQuery(year);
    const certificateIssuerParam = certificateIssuer && certificateIssuer !== ALL_CERTIFICATE_ISSUERS_FILTER_OPTION ? certificateIssuer : undefined;

    return this.statisticsService.statisticsControllerGetCertificationsStatistics(yearsToQuery, certificateIssuerParam, state);
  });

  protected readonly inspectionsStatistics = toComputed(() => {
    const year = this.yearFilter();
    const state = this.federalStateFilter();

    if (!year || !state) {
      return of([]);
    }

    const yearsToQuery = this.generateYearsToQuery(year);

    return this.statisticsService.statisticsControllerGetInspectionsStatistics(yearsToQuery, state);
  });

  protected readonly bansStatistics = toComputed(() => {
    const year = this.yearFilter();
    const state = this.federalStateFilter();

    if (!year || !state) {
      return of([]);
    }

    const yearsToQuery = this.generateYearsToQuery(year);

    return this.statisticsService.statisticsControllerGetBansStatistics(yearsToQuery, state);
  });

  protected readonly activeBansAmount = toComputed(() => {
    const year = this.yearFilter();
    if (year && year !== String(new Date().getFullYear())) {
      // if year is not the current year, then the active bans amount is not shown
      return of(undefined);
    }
    const state = this.federalStateFilter();
    return this.statisticsService.statisticsControllerGetActiveBansStatistics(state);
  });

  protected readonly errorsStatistics = toComputed(() => {
    const year = this.yearFilter();
    const state = this.federalStateFilter();
    const office = undefined; // no office filter for errors needed, so far

    if (!year || !state) {
      return of([]);
    }

    const yearsToQuery = this.generateYearsToQuery(year);

    return this.statisticsService.statisticsControllerGetErrorsStatistics(yearsToQuery, office, state);
  });

  private generateYearsToQuery(year: string): number[] {
    const startYear = Number(year);
    const endYear = startYear - this.YEARS_TO_QUERY_AMOUNT + 1;
    return numberRange(startYear, endYear);
  }

  protected handleBansExport(): void {
    const year = this.yearFilter();
    const state = this.federalStateFilter();

    if (!year || !state) {
      return;
    }

    const yearsToQuery = this.generateYearsToQuery(year);

    this.isBansExportLoading.set(true);

    this.statisticsExportService
      .statisticsExportControllerExportBansStatistics(yearsToQuery, state, 'response')
      .pipe(
        catchError((err: unknown) => this.serverDialogService.handleServerError(err)),
        finalize(() => this.isBansExportLoading.set(false))
      )
      .subscribe((response) => this.saveResponseAsFile(response));
  }

  protected handleErrorsExport(): void {
    const year = this.yearFilter();
    const state = this.federalStateFilter();
    const office = undefined; // no office filter for errors needed, so far

    if (!year || !state) {
      return;
    }

    const yearsToQuery = this.generateYearsToQuery(year);

    this.isErrorsExportLoading.set(true);

    this.statisticsExportService
      .statisticsExportControllerExportErrorsStatistics(yearsToQuery, office, state, 'response')
      .pipe(
        catchError((err: unknown) => this.serverDialogService.handleServerError(err)),
        finalize(() => this.isErrorsExportLoading.set(false))
      )
      .subscribe((response) => this.saveResponseAsFile(response));
  }

  protected handleCertificationsExport(): void {
    const year = this.yearFilter();
    const state = this.federalStateFilter();
    const certificateIssuer = this.certificateIssuerFilter();

    if (!year || !state) {
      return;
    }

    const yearsToQuery = this.generateYearsToQuery(year);
    const certificateIssuerParam = certificateIssuer && certificateIssuer !== ALL_CERTIFICATE_ISSUERS_FILTER_OPTION ? certificateIssuer : undefined;

    this.isCertificationsExportLoading.set(true);

    this.statisticsExportService
      .statisticsExportControllerExportCertificationsStatistics(yearsToQuery, certificateIssuerParam, state, 'response')
      .pipe(
        catchError((err: unknown) => this.serverDialogService.handleServerError(err)),
        finalize(() => this.isCertificationsExportLoading.set(false))
      )
      .subscribe((response) => this.saveResponseAsFile(response));
  }

  protected handleInspectionsExport(): void {
    const year = this.yearFilter();
    const state = this.federalStateFilter();

    if (!year || !state) {
      return;
    }

    const yearsToQuery = this.generateYearsToQuery(year);

    this.isInspectionsExportLoading.set(true);

    this.statisticsExportService
      .statisticsExportControllerExportInspectionsStatistics(yearsToQuery, state, 'response')
      .pipe(
        catchError((err: unknown) => this.serverDialogService.handleServerError(err)),
        finalize(() => this.isInspectionsExportLoading.set(false))
      )
      .subscribe((response) => this.saveResponseAsFile(response));
  }

  protected handleTaxesExport(): void {
    const year = this.yearFilter();
    const state = this.federalStateFilter();
    const office = this.officeFilter();

    if (!year || !state) {
      return;
    }

    const yearsToQuery = this.generateYearsToQuery(year);
    const officeParam = office && office !== ALL_OFFICES_FILTER_OPTION ? office : undefined;

    this.isTaxesExportLoading.set(true);

    this.statisticsExportService
      .statisticsExportControllerExportTaxesStatistics(yearsToQuery, officeParam, state, 'response')
      .pipe(
        catchError((err: unknown) => this.serverDialogService.handleServerError(err)),
        finalize(() => this.isTaxesExportLoading.set(false))
      )
      .subscribe((response) => this.saveResponseAsFile(response));
  }

  protected handleRegularLicensesExport(): void {
    const year = this.yearFilter();
    const state = this.federalStateFilter();
    const office = this.officeFilter();

    if (!year || !state) {
      return;
    }

    const yearsToQuery = this.generateYearsToQuery(year);
    const officeParam = office && office !== ALL_OFFICES_FILTER_OPTION ? office : undefined;

    this.isRegularLicensesExportLoading.set(true);

    this.statisticsExportService
      .statisticsExportControllerExportRegularLicensesStatistics(yearsToQuery, officeParam, state, 'response')
      .pipe(
        catchError((err: unknown) => this.serverDialogService.handleServerError(err)),
        finalize(() => this.isRegularLicensesExportLoading.set(false))
      )
      .subscribe((response) => this.saveResponseAsFile(response));
  }

  protected handleVacationLicensesExport(): void {
    const year = this.yearFilter();
    const state = this.federalStateFilter();
    const office = this.officeFilter();

    if (!year || !state) {
      return;
    }

    const yearsToQuery = this.generateYearsToQuery(year);
    const officeParam = office && office !== ALL_OFFICES_FILTER_OPTION ? office : undefined;

    this.isVacationLicensesExportLoading.set(true);

    this.statisticsExportService
      .statisticsExportControllerExportVacationLicensesStatistics(yearsToQuery, officeParam, state, 'response')
      .pipe(
        catchError((err: unknown) => this.serverDialogService.handleServerError(err)),
        finalize(() => this.isVacationLicensesExportLoading.set(false))
      )
      .subscribe((response) => this.saveResponseAsFile(response));
  }

  protected handleLimitedLicensesExport(): void {
    const year = this.yearFilter();
    const state = this.federalStateFilter();
    const office = this.officeFilter();

    if (!year || !state) return;

    const years = this.generateYearsToQuery(year);
    const officeParam = office !== ALL_OFFICES_FILTER_OPTION ? office : undefined;

    this.isLimitedLicensesExportLoading.set(true);

    this.statisticsExportService
      .statisticsExportControllerExportLimitedLicensesStatistics(years, officeParam, state, 'response')
      .pipe(
        catchError((err: unknown) => this.serverDialogService.handleServerError(err)),
        finalize(() => this.isLimitedLicensesExportLoading.set(false))
      )
      .subscribe((response) => this.saveResponseAsFile(response));
  }

  private saveResponseAsFile(response: HttpResponse<Blob>): void {
    const filename = this.extractFilename(response);
    this.downloadFile(response.body as Blob, filename);
  }

  private extractFilename(response: HttpResponse<Blob>): string {
    const contentDisposition = response.headers.get('Content-Disposition');
    const match = contentDisposition?.match(/filename="(.+?)"/);
    return match?.[1] || 'statistics_file';
  }

  private downloadFile(blob: Blob, filename: string): void {
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    a.click();
    URL.revokeObjectURL(url);
  }
}
