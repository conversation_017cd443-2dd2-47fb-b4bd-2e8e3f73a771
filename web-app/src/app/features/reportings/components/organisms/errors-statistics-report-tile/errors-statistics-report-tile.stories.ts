import { Meta, StoryObj, argsToTemplate } from '@storybook/angular';

import { ErrorsStatisticsReportTileComponent } from '@/app/features/reportings/components/organisms/errors-statistics-report-tile/errors-statistics-report-tile.component';

const actualYear = new Date().getFullYear();

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
const meta: Meta<ErrorsStatisticsReportTileComponent> = {
  title: 'statistics/ErrorsStatisticsReportTile',
  component: ErrorsStatisticsReportTileComponent,
  render: (args) => ({
    props: { ...args },
    template: `<div class="flex justify-center">
                  <div class="w-[460px]">
                    <fish-errors-statistics-report-tile ${argsToTemplate(args)}> </fish-errors-statistics-report-tile> 
                  </div>
               </div>`,
  }),
  args: {
    statistics: [
      {
        year: actualYear - 5,
        data: { onlineService: 58, cardPrinterService: 20, system: 49 },
      },
      {
        year: actualYear - 4,
        data: { onlineService: 45, cardPrinterService: 55, system: 44 },
      },
      {
        year: actualYear - 3,
        data: { onlineService: 53, cardPrinterService: 17, system: 50 },
      },
      {
        year: actualYear - 2,
        data: { onlineService: 40, cardPrinterService: 20, system: 30 },
      },
      {
        year: actualYear - 1,
        data: { onlineService: 20, cardPrinterService: 30, system: 40 },
      },
      {
        year: actualYear,
        data: { onlineService: 33, cardPrinterService: 11, system: 22 },
      },
    ],
  },
};

export default meta;

type Story = StoryObj<ErrorsStatisticsReportTileComponent>;

export const Default: Story = {};
