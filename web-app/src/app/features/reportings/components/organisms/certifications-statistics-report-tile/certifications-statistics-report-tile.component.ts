import { ChangeDetectionStrategy, Component, Signal, computed, input, output } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';

import { CertificationsStatistics } from '@digifischdok/ngx-register-sdk';

import { HistoricalBarChartValue } from '@/app/features/reportings/components/atoms/historical-bar-chart/historical-bar-chart.model.models';
import { StatisticsByIssuersTableComponent } from '@/app/features/reportings/components/molecules/statistics-by-issuers-table/statistics-by-issuers-table.component';
import { StatisticsByIssuersTableData } from '@/app/features/reportings/components/molecules/statistics-by-issuers-table/statistics-by-issuers-table.models';
import { StatisticsReportTileComponent } from '@/app/features/reportings/components/molecules/statistics-report-tile/statistics-report-tile.component';
import { Statistic } from '@/app/features/reportings/models/statistic.model';
import { fadeInAnimation } from '@/app/shared/animations/fade.animations';
import { IconExaminationInstituteComponent } from '@/app/shared/icons/examination-institute/examination-institute.component';

@Component({
  selector: 'fish-certifications-statistics-report-tile',
  standalone: true,
  imports: [StatisticsByIssuersTableComponent, StatisticsReportTileComponent, TranslateModule, IconExaminationInstituteComponent],
  templateUrl: './certifications-statistics-report-tile.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  animations: [fadeInAnimation],
})
export class CertificationsStatisticsReportTileComponent {
  // Inputs
  public statistics = input<Array<CertificationsStatistics>>();

  public isDownloadButtonLoading = input<boolean>(false);

  // Outputs
  public readonly downloadButtonClicked = output<void>();

  // Computed signals
  protected readonly primaryStatistic: Signal<Statistic | undefined> = computed(() => {
    const lastYearStatistics = this.lastYearStatistics();
    if (!lastYearStatistics) return undefined;

    return {
      label: lastYearStatistics.year,
      value: this.calculateTotalCount(lastYearStatistics.data),
      amountType: 'unit',
    };
  });

  protected readonly secondaryStatistic: Signal<Statistic | undefined> = computed(() => {
    const penultimateYearStatistics = this.penultimateYearStatistics();
    if (!penultimateYearStatistics) return undefined;

    return {
      label: penultimateYearStatistics.year,
      value: this.calculateTotalCount(penultimateYearStatistics.data),
      amountType: 'unit',
    };
  });

  protected readonly lastYearData: Signal<StatisticsByIssuersTableData | undefined> = computed(() => {
    const lastYearStatistics = this.lastYearStatistics();
    if (!lastYearStatistics) {
      return undefined;
    }

    const issuers: Record<string, number> = {};
    lastYearStatistics.data.forEach((item) => {
      issuers[item.issuer] = item.amount;
    });

    return {
      year: lastYearStatistics.year,
      issuers,
    };
  });

  protected readonly penultimateYearData: Signal<StatisticsByIssuersTableData | undefined> = computed(() => {
    const penultimateYearStatistics = this.penultimateYearStatistics();
    if (!penultimateYearStatistics) return undefined;

    const issuers: Record<string, number> = {};
    penultimateYearStatistics.data.forEach((item) => {
      issuers[item.issuer] = item.amount;
    });

    return {
      year: penultimateYearStatistics.year,
      issuers,
    };
  });

  protected readonly chartData: Signal<HistoricalBarChartValue[]> = computed(() => {
    return [...this.sortedByYearStatistics()].reverse().map((stat) => ({
      year: stat.year,
      value: this.calculateTotalCount(stat.data),
    }));
  });

  private readonly sortedByYearStatistics: Signal<CertificationsStatistics[]> = computed(() => {
    return [...(this.statistics() ?? [])].sort((a, b) => b.year - a.year);
  });

  // the most recent year of the inputted statistics
  private readonly lastYearStatistics: Signal<CertificationsStatistics | null> = computed(() => {
    const sorted = this.sortedByYearStatistics();
    return sorted[0] ?? null;
  });

  // the second most recent year of the inputted statistics
  private readonly penultimateYearStatistics: Signal<CertificationsStatistics | null> = computed(() => {
    const sorted = this.sortedByYearStatistics();
    return sorted.length > 1 ? sorted[1] : null;
  });

  private calculateTotalCount(data: Array<{ amount: number }>): number {
    return data.reduce((sum, item) => sum + item.amount, 0);
  }
}
