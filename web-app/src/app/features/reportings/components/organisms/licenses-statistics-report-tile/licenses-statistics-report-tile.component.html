<fish-statistics-report-tile
  @fadeIn
  [titleText]="getTitleKey() | translate"
  [titleSubtext]="'statistics.tile.licenses.title_subtext' | translate"
  [primaryStatistic]="primaryStatistic()"
  [secondaryStatistic]="secondaryStatistic()"
  [chartData]="chartData()"
  [isDownloadButtonLoading]="isDownloadButtonLoading()"
  (downloadButtonClicked)="downloadButtonClicked.emit()"
>
  @if (licenseType() === LicenseType.Regular) {
    <fish-icon-license-card size="32" header-icon></fish-icon-license-card>
  }
  @if (licenseType() === LicenseType.Vacation) {
    <fish-icon-vacation size="32" header-icon></fish-icon-vacation>
  }
  @if (licenseType() === LicenseType.Limited) {
    <fish-icon-handicapped size="32" header-icon></fish-icon-handicapped>
  }
  <!-- The statistics-submission-type-table is not part of the statistics-report-tile because
  in some variants another type of table is to be displayed  -->
  <fish-statistics-by-submission-type-table
    table-section
    [yearData]="lastYearData()"
    [previousYearData]="penultimateYearData()"
  ></fish-statistics-by-submission-type-table>
</fish-statistics-report-tile>
