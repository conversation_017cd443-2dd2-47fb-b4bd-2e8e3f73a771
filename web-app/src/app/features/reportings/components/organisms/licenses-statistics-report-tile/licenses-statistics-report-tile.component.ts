import { ChangeDetectionStrategy, Component, Signal, computed, input, output } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';

import { LicenseType } from '@digifischdok/ngx-register-sdk';
import { LicensesStatistics } from '@digifischdok/ngx-register-sdk/lib/model/licensesStatistics';

import { HistoricalBarChartValue } from '@/app/features/reportings/components/atoms/historical-bar-chart/historical-bar-chart.model.models';
import { StatisticsBySubmissionTypeTableComponent } from '@/app/features/reportings/components/molecules/statistics-by-submission-type-table/statistics-by-submission-type-table.component';
import { StatisticsBySubmissionTypeTableData } from '@/app/features/reportings/components/molecules/statistics-by-submission-type-table/statistics-by-submission-type-table.models';
import { StatisticsReportTileComponent } from '@/app/features/reportings/components/molecules/statistics-report-tile/statistics-report-tile.component';
import { Statistic } from '@/app/features/reportings/models/statistic.model';
import { fadeInAnimation } from '@/app/shared/animations/fade.animations';
import { IconHandicappedComponent } from '@/app/shared/icons/handicapped/handicapped.component';
import { IconLicenseCardComponent } from '@/app/shared/icons/license-card/license-card.component';
import { IconVacationComponent } from '@/app/shared/icons/vacation/vacation.component';

@Component({
  selector: 'fish-licenses-statistics-report-tile',
  standalone: true,
  imports: [
    StatisticsReportTileComponent,
    IconLicenseCardComponent,
    StatisticsBySubmissionTypeTableComponent,
    TranslateModule,
    IconVacationComponent,
    IconHandicappedComponent,
  ],
  templateUrl: './licenses-statistics-report-tile.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  animations: [fadeInAnimation],
})
export class LicensesStatisticsReportTileComponent {
  // Inputs
  public statistics = input<Array<LicensesStatistics>>();

  public licenseType = input<LicenseType>(LicenseType.Regular);

  public isDownloadButtonLoading = input<boolean>(false);

  // Outputs
  public readonly downloadButtonClicked = output<void>();

  // Computed signals
  protected readonly primaryStatistic: Signal<Statistic | undefined> = computed(() => {
    const lastYearStatistics = this.lastYearStatistics();
    if (!lastYearStatistics) return undefined;

    return {
      label: lastYearStatistics.year,
      value: this.calculateTotalCount(lastYearStatistics.data),
      amountType: 'unit',
    };
  });

  protected readonly secondaryStatistic: Signal<Statistic | undefined> = computed(() => {
    const penultimateYearStatistics = this.penultimateYearStatistics();
    if (!penultimateYearStatistics) return undefined;

    return {
      label: penultimateYearStatistics.year,
      value: this.calculateTotalCount(penultimateYearStatistics.data),
      amountType: 'unit',
    };
  });

  protected readonly chartData: Signal<HistoricalBarChartValue[]> = computed(() => {
    return [...this.sortedByYearStatistics()].reverse().map((stat) => ({
      year: stat.year,
      value: this.calculateTotalCount(stat.data),
    }));
  });

  protected readonly lastYearData: Signal<StatisticsBySubmissionTypeTableData | undefined> = computed(() => {
    const lastYearStatistics = this.lastYearStatistics();
    if (!lastYearStatistics) return undefined;

    return {
      year: lastYearStatistics.year,
      analog: this.getCountBySubmissionType(lastYearStatistics.data, 'ANALOG'),
      online: this.getCountBySubmissionType(lastYearStatistics.data, 'ONLINE'),
    };
  });

  protected readonly penultimateYearData: Signal<StatisticsBySubmissionTypeTableData | undefined> = computed(() => {
    const penultimateYearStatistics = this.penultimateYearStatistics();
    if (!penultimateYearStatistics) return undefined;

    return {
      year: penultimateYearStatistics.year,
      analog: this.getCountBySubmissionType(penultimateYearStatistics.data, 'ANALOG'),
      online: this.getCountBySubmissionType(penultimateYearStatistics.data, 'ONLINE'),
    };
  });

  private readonly sortedByYearStatistics: Signal<LicensesStatistics[]> = computed(() => {
    return [...(this.statistics() ?? [])].sort((a, b) => b.year - a.year);
  });

  // the most recent year of the inputted statistics
  private readonly lastYearStatistics: Signal<LicensesStatistics | null> = computed(() => {
    const sorted = this.sortedByYearStatistics();
    return sorted[0] ?? null;
  });

  // the second most recent year of the inputted statistics
  private readonly penultimateYearStatistics: Signal<LicensesStatistics | null> = computed(() => {
    const sorted = this.sortedByYearStatistics();
    return sorted.length > 1 ? sorted[1] : null;
  });

  private calculateTotalCount(data: Array<{ count: number }>): number {
    return data.reduce((sum, item) => sum + item.count, 0);
  }

  private getCountBySubmissionType(data: Array<{ submissionType: string; count: number }>, type: string): number {
    return data.find((d) => d.submissionType === type)?.count || 0;
  }

  protected readonly LicenseType = LicenseType;

  protected getTitleKey(): string {
    switch (this.licenseType()) {
      case LicenseType.Regular:
        return 'statistics.tile.licenses.title.regular';
      case LicenseType.Vacation:
        return 'statistics.tile.licenses.title.vacation';
      case LicenseType.Limited:
        return 'statistics.tile.licenses.title.limited';
      default:
        throw new Error('Unknown License Type');
    }
  }
}
