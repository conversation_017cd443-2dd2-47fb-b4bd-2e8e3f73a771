import { ChangeDetectionStrategy, Component, Signal, computed, input, output } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';

import { InspectionsStatistics } from '@digifischdok/ngx-register-sdk';

import { HistoricalBarChartValue } from '@/app/features/reportings/components/atoms/historical-bar-chart/historical-bar-chart.model.models';
import { StatisticsReportTileComponent } from '@/app/features/reportings/components/molecules/statistics-report-tile/statistics-report-tile.component';
import { Statistic } from '@/app/features/reportings/models/statistic.model';
import { fadeInAnimation } from '@/app/shared/animations/fade.animations';
import { IconControlInstituteComponent } from '@/app/shared/icons/control-institute/control-institute.component';

@Component({
  selector: 'fish-inspections-statistics-report-tile',
  imports: [StatisticsReportTileComponent, TranslateModule, IconControlInstituteComponent],
  templateUrl: './inspections-statistics-report-tile.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  animations: [fadeInAnimation],
})
export class InspectionsStatisticsReportTileComponent {
  // Inputs
  public statistics = input<Array<InspectionsStatistics>>();

  public isDownloadButtonLoading = input<boolean>(false);

  // Outputs
  public readonly downloadButtonClicked = output<void>();

  protected readonly primaryStatistic: Signal<Statistic | undefined> = computed(() => {
    const lastYearStatistics = this.lastYearStatistics();
    if (!lastYearStatistics) return undefined;

    return {
      label: lastYearStatistics.year,
      value: lastYearStatistics.data.numberOfInspections,
      amountType: 'unit',
    };
  });

  protected readonly secondaryStatistic: Signal<Statistic | undefined> = computed(() => {
    const penultimateYearStatistics = this.penultimateYearStatistics();
    if (!penultimateYearStatistics) return undefined;

    return {
      label: penultimateYearStatistics.year,
      value: penultimateYearStatistics.data.numberOfInspections,
      amountType: 'unit',
    };
  });

  protected readonly chartData: Signal<HistoricalBarChartValue[]> = computed(() => {
    return [...this.sortedByYearStatistics()].reverse().map((stat) => ({
      year: stat.year,
      value: stat.data.numberOfInspections,
    }));
  });

  private readonly sortedByYearStatistics: Signal<InspectionsStatistics[]> = computed(() => {
    return [...(this.statistics() ?? [])].sort((a, b) => b.year - a.year);
  });

  // the most recent year of the inputted statistics
  private readonly lastYearStatistics: Signal<InspectionsStatistics | null> = computed(() => {
    const sorted = this.sortedByYearStatistics();
    return sorted[0] ?? null;
  });

  // the second most recent year of the inputted statistics
  private readonly penultimateYearStatistics: Signal<InspectionsStatistics | null> = computed(() => {
    const sorted = this.sortedByYearStatistics();
    return sorted.length > 1 ? sorted[1] : null;
  });
}
