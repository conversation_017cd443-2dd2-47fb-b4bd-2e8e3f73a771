import { ChangeDetectionStrategy, Component, input, output } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';

import { ButtonComponent } from '@/app/shared/atoms/button/button.component';
import { CardContentComponent } from '@/app/shared/atoms/card-content/card-content.component';
import { CardFooterComponent } from '@/app/shared/atoms/card-footer/card-footer.component';
import { CardHeaderComponent } from '@/app/shared/atoms/card-header/card-header.component';
import { FormErrorMessageComponent } from '@/app/shared/atoms/form-error-message/form-error-message.component';
import { PaymentItemMainAreaComponent } from '@/app/shared/atoms/payment-item-main-area/payment-item-main-area.component';
import { IconDocumentPdfComponent } from '@/app/shared/icons/document-pdf/document-pdf.component';
import { IconLinkComponent } from '@/app/shared/icons/link/link.component';
import { CardComponent } from '@/app/shared/molecules/card/card.component';
import { PaymentItemComponent } from '@/app/shared/molecules/payment-item/payment-item.component';

@Component({
  selector: 'fish-limited-license-approval-document-preview-box',
  imports: [
    CardComponent,
    CardHeaderComponent,
    ButtonComponent,
    IconLinkComponent,
    CardContentComponent,
    CardFooterComponent,
    IconDocumentPdfComponent,
    PaymentItemComponent,
    PaymentItemMainAreaComponent,
    TranslateModule,
    FormErrorMessageComponent,
  ],
  templateUrl: './limited-license-approval-document-preview-box.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LimitedLicenseApprovalDocumentPreviewBoxComponent {
  // Inputs
  public readonly isPreviewButtonLoading = input<boolean>(false);

  public readonly showValidationErrorMessage = input<boolean>(false);

  // Outputs
  public readonly previewButtonClicked = output<void>();
}
