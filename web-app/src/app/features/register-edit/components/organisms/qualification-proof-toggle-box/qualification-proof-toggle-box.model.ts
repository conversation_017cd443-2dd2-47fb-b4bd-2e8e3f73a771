import { CertificateFormValues } from '@/app/features/register-edit/components/organisms/certificate-form/certificate-form.model';
import { LicenseFormValues } from '@/app/features/register-edit/components/organisms/license-form/license-form.models';
import { OtherQualificationsProofFormValues } from '@/app/features/register-edit/components/organisms/other-qualifications-proof-form/other-qualifications-proof-form.model';

export interface IQualificationProofToggleBoxValues {
  certificateFormValues?: CertificateFormValues;
  licenseFormValues?: LicenseFormValues;
  otherQualificationsProofFormValues?: OtherQualificationsProofFormValues;
}
