<fish-toggle-box class="flex w-full" direction="vertical" (changed)="handleToggleBoxChanged($event)">
  <fish-toggle-box-header>
    <fish-slider-button-group direction="vertical">
      <fish-slider-button>
        <fish-icon-license-card size="32" data-testid="qualification-proof-toggle-box-licence-card" />
        <span
          class="invisible whitespace-nowrap font-bold"
          [innerText]="'edit_form.qualification_proof.button_license_text' | translate"
          aria-hidden="true"
        >
        </span>
        <span class="absolute left-12" [innerText]="'edit_form.qualification_proof.button_license_text' | translate"></span>
      </fish-slider-button>

      <fish-slider-button>
        <fish-icon-certificate size="32" data-testid="qualification-proof-toggle-box-certificate" />
        <span
          class="invisible whitespace-nowrap font-bold"
          [innerText]="'edit_form.qualification_proof.button_certificate_text' | translate"
          aria-hidden="true"
        >
        </span>
        <span class="absolute left-12" [innerText]="'edit_form.qualification_proof.button_certificate_text' | translate"></span>
      </fish-slider-button>

      <fish-slider-button>
        <fish-icon-qualifications-proof size="32" data-testid="qualification-proof-toggle-box-other-proof" />
        <span
          class="invisible whitespace-nowrap font-bold"
          [innerText]="'edit_form.qualification_proof.button_other_proof_text' | translate"
          aria-hidden="true"
        >
        </span>
        <span class="absolute left-12" [innerText]="'edit_form.qualification_proof.button_other_proof_text' | translate"></span>
      </fish-slider-button>
    </fish-slider-button-group>
  </fish-toggle-box-header>

  <fish-toggle-box-content>
    <fish-toggle-box-tab>
      <fish-license-form
        [preventLeaving]="true"
        (changed)="handleLicenseFormChanged($event)"
        (validated)="handleLicenseFormValidated($event)"
        data-testid="fish-licence-form"
      ></fish-license-form>
    </fish-toggle-box-tab>

    <fish-toggle-box-tab>
      <fish-certificate-form
        [preventLeaving]="true"
        (changed)="handleCertificateFormChanged($event)"
        (validated)="handleCertificateFormValidated($event)"
        data-testid="certificate-form"
      ></fish-certificate-form>
    </fish-toggle-box-tab>

    <fish-toggle-box-tab>
      <fish-other-qualifications-proof-form
        [preventLeaving]="true"
        (changed)="handleOtherQualificationsProofFormChanged($event)"
        (validated)="handleOtherQualificationsProofFormValidated($event)"
        data-testid="other-qualifications-proof-form"
      ></fish-other-qualifications-proof-form>
    </fish-toggle-box-tab>
  </fish-toggle-box-content>
</fish-toggle-box>
