import { ChangeDetectionStrategy, Component, OnInit, inject } from '@angular/core';
import { FormBuilder, Validators } from '@angular/forms';

import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Observable, forkJoin, map } from 'rxjs';

import { PeriodRadioItemComponent } from '@/app/features/register-edit/components/molecules/period-radio-item/period-radio-item.component';
import {
  BanPeriodFormGroup,
  PermanentBanPeriodFormGroup,
  TemporaryBanPeriodFormGroup,
} from '@/app/features/register-edit/components/organisms/ban-period-form/ban-period-form.models';
import { FormComponent } from '@/app/shared/atoms/form/form.component';
import { FishBeforeUnloadAndCanDeactivate } from '@/app/shared/decorators/before-unload-and-can-deactivate/before-unload-and-can-deactivate.decorator';
import { ValidationErrorMapping } from '@/app/shared/organisms/form-field/form-field.models';
import { DateValidators } from '@/app/shared/validators/date.validators';

@FishBeforeUnloadAndCanDeactivate()
@Component({
  selector: 'fish-ban-period-form',
  imports: [PeriodRadioItemComponent, TranslateModule],
  templateUrl: './ban-period-form.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BanPeriodFormComponent extends FormComponent<BanPeriodFormGroup> implements OnInit {
  // fields
  public override formGroup!: BanPeriodFormGroup;

  protected bannedToErrorMapping$!: Observable<ValidationErrorMapping>;

  protected bannedFromErrorMapping$!: Observable<ValidationErrorMapping>;

  // dependencies
  private readonly formBuilder: FormBuilder = inject(FormBuilder);

  private readonly translate: TranslateService = inject(TranslateService);

  constructor() {
    super();
    this.initFormGroup();
    this.captureInitialState();
  }

  public override ngOnInit(): void {
    super.ngOnInit();
    this.initErrorMappings();
  }

  private initFormGroup(): void {
    const temporaryBanStartDateControl = this.formBuilder.nonNullable.control('', [Validators.required]);

    const temporaryBanEndDateControl = this.formBuilder.nonNullable.control('', [
      Validators.required,
      DateValidators.notInPast(false),
      DateValidators.notBefore(temporaryBanStartDateControl),
    ]);

    const permanentBanStartDateControl = this.formBuilder.nonNullable.control('', [Validators.required]);

    this.formGroup = this.formBuilder.group({
      type: this.formBuilder.control('temporary'), // by default is temporary preselected
      temporary: this.formBuilder.group({
        from: temporaryBanStartDateControl,
        to: temporaryBanEndDateControl,
      }) as TemporaryBanPeriodFormGroup,
      permanent: this.formBuilder.group({
        from: permanentBanStartDateControl,
      }) as PermanentBanPeriodFormGroup,
    }) as BanPeriodFormGroup;
  }

  private initErrorMappings(): void {
    this.bannedToErrorMapping$ = forkJoin([
      this.translate.get('common.form.error.date.format'),
      this.translate.get('common.form.error.date.not_in_past'),
      this.translate.get('edit_form.ban_period.error.ban_end_before_ban_start'),
    ]).pipe(
      map(([formatError, notInPastError, beforeStartError]) => ({
        format: formatError,
        notInPast: notInPastError,
        notBefore: beforeStartError,
      }))
    );

    this.bannedFromErrorMapping$ = forkJoin([this.translate.get('common.form.error.date.format')]).pipe(
      map(([formatError]) => ({
        format: formatError,
      }))
    );
  }

  public override validate(): void {
    const type = this.formGroup.controls.type.value;
    if (type) {
      this.formGroup.controls[type].markAllAsTouched();
      this.formGroup.controls[type].updateValueAndValidity();
    }
  }
}
