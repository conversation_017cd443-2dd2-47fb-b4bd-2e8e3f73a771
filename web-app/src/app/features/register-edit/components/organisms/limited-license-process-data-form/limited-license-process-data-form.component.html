<div class="flex flex-col items-stretch justify-start gap-4 self-stretch">
  <div class="justify-start text-base font-thin" [innerText]="'edit_form.limited_license_approval.form.process_data_title' | translate"></div>
  <div class="flex flex-col items-stretch justify-start gap-6 self-stretch">
    <fish-form-field
      [label]="'edit_form.limited_license_approval.form.created_at_label' | translate"
      type="date"
      [control]="formGroup.controls.createdAt"
      [errorMapping$]="createdAtErrorMapping$"
      data-testid="limited-license-approval-form-created-at"
    ></fish-form-field>
    <fish-form-field
      [label]="'edit_form.limited_license_approval.form.file_number_label' | translate"
      [placeholder]="'edit_form.limited_license_approval.form.file_number_placeholder' | translate"
      [control]="formGroup.controls.fileNumber"
      [maxLength]="200"
      data-testid="limited-license-approval-form-file-number"
    ></fish-form-field>
    <fish-form-field
      [label]="'edit_form.limited_license_approval.form.cash_register_sign_label' | translate"
      [placeholder]="'edit_form.limited_license_approval.form.cash_register_sign_placeholder' | translate"
      [control]="formGroup.controls.cashRegisterSign"
      [maxLength]="200"
      data-testid="limited-license-approval-form-cash-register-sign"
    ></fish-form-field>
    @if (requireJustificationForLimitedDurationNotice()) {
      <fish-form-field
        [label]="'edit_form.limited_license_approval.form.justification_label' | translate"
        [placeholder]="'edit_form.limited_license_approval.form.justification_placeholder' | translate"
        type="textarea"
        [maxLength]="440"
        [showCharacterCount]="true"
        [control]="formGroup.controls.justificationForLimitedDurationNotice"
        data-testid="limited-license-approval-form-justification"
      ></fish-form-field>
    }
  </div>
</div>
