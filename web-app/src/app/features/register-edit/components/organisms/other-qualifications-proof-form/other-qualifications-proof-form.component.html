<div class="flex gap-4">
  <fish-form-field
    [control]="formGroup.controls.issuedOn"
    [errorMapping$]="issuedOnErrorMapping$"
    [label]="'edit_form.qualification_proof.issued_on' | translate"
    [placeholder]="'edit_form.qualification_proof.valid_from_placeholder' | translate"
    [type]="'date'"
    class="basis-1/2"
    data-testid="other-qualification-proof-form-issued-on"
  />

  <fish-form-field
    [control]="formGroup.controls.number"
    [label]="'edit_form.qualification_proof.number' | translate"
    [maxLength]="50"
    [placeholder]="'edit_form.qualification_proof.number_placeholder' | translate"
    class="basis-1/2"
    data-testid="other-qualification-proof-form-controls-number"
  />
</div>

<fish-form-field
  [control]="formGroup.controls.issuedBy"
  [label]="'edit_form.qualification_proof.issued_by_institution' | translate"
  [maxLength]="200"
  [placeholder]="'edit_form.qualification_proof.issued_by_placeholder' | translate"
  data-testid="other-qualification-proof-form-issued-by"
/>

<fish-form-field
  [control]="formGroup.controls.federalState"
  [label]="'edit_form.qualification_proof.place' | translate"
  [maxLength]="50"
  [options]="originPlaceOptions"
  [placeholder]="'edit_form.qualification_proof.place_placeholder' | translate"
  data-testid="other-qualification-proof-form-federal-state"
  type="combobox"
/>
