import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';

import { PaymentsStepFormGroup } from '@/app/features/register-edit/components/organisms/steps/payments-step/payments-step.models';

/**
 * Custom validator for the PaymentsStep form. It validates the tax selection
 * and payment method fields based on whether the tax is optional and whether
 * the fee is shown.
 *
 * @param isTaxOptional - Flag indicating if tax selection is optional.
 * @param showFee - Flag indicating if the fee section is shown.
 * @returns Validator function for form control validation.
 */
export function PaymentStepFormValidator(isTaxOptional: boolean, showFee: boolean): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    const form = control as PaymentsStepFormGroup;

    const taxSelection = form.controls.taxSelection?.value;
    const paymentMethod = form.controls.paymentMethod?.value;

    // Case: Tax is optional and fee is not displayed.
    if (isTaxOptional && !showFee) {
      if (taxSelection) {
        // Payment is required if tax is selected.
        return paymentMethod === null ? { paymentRequired: true } : null;
      }
      if (paymentMethod) {
        // Tax selection is required if payment method is chosen.
        return taxSelection === null ? { taxRequired: true } : null;
      }
      return null; // Form is valid, can proceed.
    }

    // Case: Tax is not optional, and tax selection is missing.
    if (!isTaxOptional && !taxSelection) {
      return { taxRequired: true }; // Form is invalid, cannot proceed.
    }

    // Case: Payment method is missing.
    return paymentMethod === null ? { paymentRequired: true } : null;
  };
}
