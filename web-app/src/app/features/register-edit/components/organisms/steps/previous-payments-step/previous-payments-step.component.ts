import { AsyncPipe } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, Component, EventEmitter, OnInit, Output, ViewChild, inject } from '@angular/core';

import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { KeycloakService } from 'keycloak-angular';
import { BehaviorSubject, Observable, map, switchMap } from 'rxjs';

import { EditFooterComponent } from '@/app/features/register-edit/components/organisms/edit-footer/edit-footer.component';
import { PreviousPaymentsFormComponent } from '@/app/features/register-edit/components/organisms/previous-payments-form/previous-payments-form.component';
import { PreviousPaymentsFormGroup } from '@/app/features/register-edit/components/organisms/previous-payments-form/previous-payments-form.models';
import { PreviousPaymentsLinkboxComponent } from '@/app/features/register-edit/components/organisms/previous-payments-linkbox/previous-payments-linkbox.component';
import { EditFormStep } from '@/app/features/register-edit/interfaces/edit-form-step';
import { FederalState } from '@/app/shared/models/federal-state';
import { DataCatalogService } from '@/app/shared/services/data-catalog.service';

@Component({
  selector: 'fish-previous-payments-step',
  imports: [PreviousPaymentsFormComponent, TranslateModule, AsyncPipe, EditFooterComponent, PreviousPaymentsLinkboxComponent],
  templateUrl: './previous-payments-step.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PreviousPaymentsStepComponent implements AfterViewInit, OnInit, EditFormStep<PreviousPaymentsFormGroup> {
  // Outputs
  @Output()
  public readonly backButtonClicked = new EventEmitter<void>();

  @Output()
  public readonly continueButtonClicked = new EventEmitter<void>();

  // Fields
  public formGroup!: PreviousPaymentsFormGroup;

  public canContinue$ = new BehaviorSubject<boolean>(false);

  @ViewChild(PreviousPaymentsFormComponent)
  protected previousPaymentsForm!: PreviousPaymentsFormComponent;

  protected federalStateId: string = '';

  protected readonly federalStateName$: Observable<string>;

  // Dependencies
  private readonly translate: TranslateService = inject(TranslateService);

  private readonly keycloakService: KeycloakService = inject(KeycloakService);

  private readonly dataCatalogService: DataCatalogService = inject(DataCatalogService);

  constructor() {
    this.federalStateName$ = this.getFederalStateName$();
  }

  protected get stepSubtext$(): Observable<string> {
    return this.federalStateName$.pipe(
      switchMap((federalStateName) =>
        this.translate.get('edit_form.previous_payments.subtext', {
          federalState: federalStateName,
        })
      )
    );
  }

  public ngOnInit(): void {
    this.federalStateId = this.keycloakService.getKeycloakInstance().tokenParsed?.['federalState'] || '';
  }

  public ngAfterViewInit(): void {
    this.formGroup = this.previousPaymentsForm.formGroup;
  }

  protected handleValidate(isValid: boolean): void {
    this.canContinue$.next(isValid);
  }

  protected onContinue(): void {
    this.previousPaymentsForm.validate();
    if (this.formGroup.valid) {
      this.continueButtonClicked.emit();
    }
  }

  private getFederalStateName$(): Observable<string> {
    return this.dataCatalogService.getFederalStates$().pipe(
      map((federalStates: FederalState[]) => {
        const federalState = federalStates.find((state) => state.id === this.federalStateId);
        if (federalState) {
          return federalState.name;
        } else {
          throw new Error('Invalid federal state');
        }
      })
    );
  }
}
