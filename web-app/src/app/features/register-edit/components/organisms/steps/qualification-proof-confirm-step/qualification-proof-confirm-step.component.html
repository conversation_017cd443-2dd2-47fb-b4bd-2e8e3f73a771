<div class="flex w-full flex-col justify-between gap-12">
  <div>
    <fish-table-header class="mt-5">
      <fish-table-cell class="flex-1">
        {{ 'edit_form.qualification_proof_confirm.name' | translate }}
      </fish-table-cell>
      <fish-table-cell class="w-[15rem]">
        {{ 'edit_form.qualification_proof_confirm.test_code' | translate }}
      </fish-table-cell>
      <fish-table-cell class="w-[8rem]">
        {{ 'edit_form.qualification_proof_confirm.passed_on' | translate }}
      </fish-table-cell>
      <fish-table-cell class="w-1/3">
        {{ 'edit_form.qualification_proof_confirm.test_institution' | translate }}
      </fish-table-cell>
    </fish-table-header>
    <fish-table-row>
      <fish-table-cell class="flex-1">
        <span [innerHTML]="citizen() | personFullname" class="overflow-ellipsis"></span>
      </fish-table-cell>
      <fish-table-cell class="w-[15rem]">
        <span [innerHTML]="fishingCertificate()?.fishingCertificateId | documentNumber"></span>
      </fish-table-cell>
      <fish-table-cell class="w-[8rem]">
        <span [innerHTML]="fishingCertificate()?.passedOn | date: 'dd.MM.yyyy'"></span>
      </fish-table-cell>
      <fish-table-cell class="w-1/3">
        <span [innerHTML]="fishingCertificate()?.issuedBy" class="overflow-ellipsis"></span>
      </fish-table-cell>
    </fish-table-row>
  </div>
  <!-- this step is by default always valid, so the user can always continue -->
  <fish-edit-footer (backed)="onBacked()" (continued)="onContinue()" [showBackButton]="true" />
</div>
