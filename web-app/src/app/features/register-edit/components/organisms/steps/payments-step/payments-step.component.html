<div class="flex flex-col items-stretch justify-between gap-12">
  <div class="grid grid-cols-1 gap-6 xl:grid-cols-[2fr_1fr]">
    <div class="flex-1">
      @if (isTaxReadonly()) {
        <fish-readonly-payment-box
          [licenseType]="licenseType()"
          [preventLeaving]="true"
          [feeCost]="feeCost()"
          [selectedValidityPeriod]="selectedValidityPeriod()"
          [showFee]="showFee()"
          class="block h-full"
        />
      } @else {
        <fish-payment-box
          [licenseType]="licenseType()"
          [preventLeaving]="true"
          [isTaxCheckboxDisabled]="isTaxCheckboxDisabled()"
          [feeCost]="feeCost()"
          [isTaxOptional]="isTaxOptional()"
          [previouslyPaidTax]="previouslyPaid()"
          [showFee]="showFee()"
          [showPreviouslyPayedTax]="showPreviouslyPaidTax()"
          [taxCostOptions]="taxCostOptions()"
          [licenseValidTo]="selectedValidityPeriod()?.validTo"
          class="block h-full"
        />
      }
    </div>
    <fish-payment-method-card
      [isRequired]="isPaymentMethodRequired()"
      [preventLeaving]="true"
      [enabledPaymentTypes]="enabledPaymentTypes()"
      class="block h-full"
    />
  </div>
  <fish-edit-footer
    (backed)="backButtonClicked.emit()"
    (continued)="onContinue()"
    [isLastStep]="isLastStep()"
    [continueButtonLabel]="continueButtonLabel()"
    [isLoading]="isLoading()"
    [showBackButton]="showBackButton()"
  />
</div>
