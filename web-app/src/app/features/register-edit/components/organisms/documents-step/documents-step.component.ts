import { ChangeDetectionStrategy, Component, inject, input } from '@angular/core';

import { TranslateModule, TranslateService } from '@ngx-translate/core';

import { IdentificationDocument, IdentificationDocumentsMailTemplateType, LicenseType } from '@digifischdok/ngx-register-sdk';

import { DocumentsBoxComponent } from '@/app/features/register-edit/components/organisms/documents-box/documents-box.component';
import { LicenseCardboxComponent } from '@/app/shared/atoms/license-cardbox/license-cardbox.component';
import { toComputed } from '@/app/shared/utils/rxJsInterop.utils';

@Component({
  selector: 'fish-documents-step',
  imports: [TranslateModule, LicenseCardboxComponent, DocumentsBoxComponent],
  templateUrl: './documents-step.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DocumentsStepComponent {
  public readonly registerEntryId = input.required<string>();

  public readonly documents = input.required<IdentificationDocument[]>();

  public readonly identificationDocumentsMailTemplateType = input<IdentificationDocumentsMailTemplateType>();

  public readonly showLicenseCardBox = input<boolean>(true);

  public readonly licenseCardboxLicenseType = input<typeof LicenseType.Regular | typeof LicenseType.Limited>(LicenseType.Regular);

  public readonly showSendMailSection = input<boolean>(true);

  public readonly showOnlineServiceInboxConfirmation = input<boolean>(false);

  protected readonly licenseCardboxImageSrc = toComputed(() => {
    switch (this.licenseCardboxLicenseType()) {
      case LicenseType.Regular:
        return this.translateService.get('edit_form.documents.license_cardbox.image.regular');
      case LicenseType.Limited:
        return this.translateService.get('edit_form.documents.license_cardbox.image.limited');
      default:
        throw new Error('Illegal License Type');
    }
  });

  protected readonly licenseCardBoxTitle = toComputed(() => {
    switch (this.licenseCardboxLicenseType()) {
      case LicenseType.Regular:
        return this.translateService.get('edit_form.documents.license_cardbox.title.regular');
      case LicenseType.Limited:
        return this.translateService.get('edit_form.documents.license_cardbox.title.limited');
      default:
        throw new Error('Illegal License Type');
    }
  });

  private readonly translateService = inject(TranslateService);
}
