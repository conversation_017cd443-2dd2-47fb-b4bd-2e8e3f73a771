import { Meta, StoryObj } from '@storybook/angular';

import { PaymentBoxComponent } from '@/app/features/register-edit/components/organisms/payment-box/payment-box.component';
import { ReadonlyPaymentBoxComponent } from '@/app/features/register-edit/components/organisms/readonly-payment-box/readonly-payment-box.component';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
const meta: Meta<ReadonlyPaymentBoxComponent> = {
  title: 'register-edit/ReadonlyPaymentBox',
  component: ReadonlyPaymentBoxComponent,
  args: {
    selectedValidityPeriod: {
      validFrom: '2022-01-01',
      validTo: '2023-01-01',
    },
    feeCost: 20,
  },
  argTypes: {
    selectedValidityPeriod: {
      control: 'object',
    },
    feeCost: {
      control: 'number',
    },
  },
};

export default meta;

type Story = StoryObj<PaymentBoxComponent>;

export const Default: Story = {};
