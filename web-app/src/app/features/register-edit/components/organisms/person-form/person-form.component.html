<div [formGroup]="formGroup" class="flex w-full flex-col items-stretch">
  <fish-form-field
    [control]="formGroup.controls.title"
    [label]="'edit_form.data.title' | translate"
    [maxLength]="80"
    [options]="titleOptions"
    [placeholder]="'edit_form.data.title_placeholder' | translate"
    data-testid="person-form-title"
    type="combobox"
  />

  <fish-form-field
    [control]="formGroup.controls.firstname"
    [label]="'edit_form.data.firstname' | translate"
    [maxLength]="80"
    [placeholder]="'edit_form.data.firstname_placeholder' | translate"
    data-testid="person-form-firstname"
  />

  <fish-form-field
    [control]="formGroup.controls.lastname"
    [label]="'edit_form.data.lastname' | translate"
    [maxLength]="120"
    [placeholder]="'edit_form.data.lastname_placeholder' | translate"
    data-testid="person-form-lastname"
  />

  <fish-form-field
    [control]="formGroup.controls.birthname"
    [label]="'edit_form.data.birthname' | translate"
    [maxLength]="120"
    [placeholder]="'edit_form.data.birthname_placeholder' | translate"
    [subLabel]="'edit_form.data.birthname_sub_label' | translate"
    data-testid="person-form-birth-name"
  />

  <fish-form-field
    [control]="formGroup.controls.birthdate"
    [errorMapping$]="birthdateErrorMapping$"
    [label]="'edit_form.data.birthdate' | translate"
    [maxLength]="10"
    [placeholder]="'edit_form.data.birthdate_placeholder' | translate"
    data-testid="person-form-birth-date"
  />

  <fish-form-field
    [control]="formGroup.controls.birthplace"
    [label]="'edit_form.data.birthplace' | translate"
    [maxLength]="120"
    [placeholder]="'edit_form.data.birthplace_placeholder' | translate"
    data-testid="person-form-birth-place"
  />

  <fish-form-field
    [control]="formGroup.controls.nationality"
    [label]="'edit_form.data.nationality' | translate"
    [options]="nationalityOptions"
    [placeholder]="'edit_form.data.nationality_placeholder' | translate"
    data-testid="person-form-nationality"
    type="combobox"
  />
</div>
