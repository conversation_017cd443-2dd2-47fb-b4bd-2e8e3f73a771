import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, inject, input, output } from '@angular/core';
import { FormControl } from '@angular/forms';

import { TranslateModule } from '@ngx-translate/core';
import { twMerge } from 'tailwind-merge';

import { TailwindBreakpointService } from '@/app/core/services/tailwind-breakpoint/tailwind-breakpoint.service';
import { ButtonComponent } from '@/app/shared/atoms/button/button.component';
import { IconArrowLeftComponent } from '@/app/shared/icons/arrow-left/arrow-left.component';
import { IconArrowRightComponent } from '@/app/shared/icons/arrow-right/arrow-right.component';
import { IconCheckComponent } from '@/app/shared/icons/check/check.component';
import { ConfirmBoxComponent } from '@/app/shared/molecules/confirm-box/confirm-box.component';

@Component({
  selector: 'fish-edit-footer',
  imports: [ButtonComponent, ConfirmBoxComponent, TranslateModule, CommonModule, IconArrowRightComponent, IconCheckComponent, IconArrowLeftComponent],
  templateUrl: './edit-footer.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EditFooterComponent {
  // Inputs
  public readonly showGdpr = input<boolean>(false);

  public readonly thirdPartySubmissionControl = input<FormControl>();

  public readonly showBackButton = input<boolean>(false);

  public readonly isLastStep = input<boolean>(false);

  public readonly isLoading = input<boolean>(false);

  public readonly continueButtonLabel = input<string>();

  // Outputs
  public readonly gdprChanged = output<void>();

  public readonly backed = output<void>();

  public readonly continued = output<void>();

  // Dependencies
  protected readonly breakpointService = inject(TailwindBreakpointService);

  // Fields
  protected readonly containerClasses = computed<string>(() => {
    // This ensures that the submit button is always on the right, regardless of what is shown on the left side
    return twMerge([
      'w-full flex items-center h-16 mt-10',
      this.showBackButton() || (this.thirdPartySubmissionControl() && this.showThirdPartySubmissionControlInsideFooter())
        ? 'justify-between'
        : 'justify-end',
      this.showThirdPartySubmissionControlInsideFooter() ? 'mt-0' : 'mt-4',
    ]);
  });

  protected readonly showThirdPartySubmissionControlInsideFooter = computed<boolean>(() => {
    return this.breakpointService.breakpoints.lg();
  });
}
