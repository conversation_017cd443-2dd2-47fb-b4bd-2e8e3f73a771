import { AfterViewInit, ChangeDetectionStrategy, Component, On<PERSON><PERSON>roy, ViewChild, WritableSignal, inject, isDevMode, signal } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';
import { catchError, combineLatest, distinctUntilChanged, finalize } from 'rxjs';

import {
  DigitizeFishingLicenseRequest,
  DigitizeFishingLicenseResponse,
  FishingLicenseService,
  IdentificationDocument,
  IdentificationDocumentsMailTemplateType,
  LicenseType,
} from '@digifischdok/ngx-register-sdk';

import { CitizenStore } from '@/app/core/stores/citizen.store';
import { ConsentStore } from '@/app/core/stores/consent.store';
import { ProfileHeaderStore } from '@/app/core/stores/profile-header.store';
import { DocumentsStepComponent } from '@/app/features/register-edit/components/organisms/documents-step/documents-step.component';
import { PaymentsStepComponent } from '@/app/features/register-edit/components/organisms/steps/payments-step/payments-step.component';
import { PersonalDataStepComponent } from '@/app/features/register-edit/components/organisms/steps/personal-data-step/personal-data-step.component';
import { PersonalDataStepFormGroup } from '@/app/features/register-edit/components/organisms/steps/personal-data-step/personal-data-step.models';
import { PreviousPaymentsStepComponent } from '@/app/features/register-edit/components/organisms/steps/previous-payments-step/previous-payments-step.component';
import { QualificationProofStepComponent } from '@/app/features/register-edit/components/organisms/steps/qualification-proof-step/qualification-proof-step.component';
import { EditFormStep } from '@/app/features/register-edit/interfaces/edit-form-step';
import { TabDisabledPipe } from '@/app/features/register-edit/pipes/tab-disabled.pipe';
import { RequestPaymentsBuilderService } from '@/app/features/register-edit/services/request-payments-builder.service';
import { RequestPersonBuilderService } from '@/app/features/register-edit/services/request-person-builder.service';
import { RequestProofBuilderService } from '@/app/features/register-edit/services/request-proof-builder.service';
import { TabComponent } from '@/app/shared/atoms/tab/tab.component';
import { TaxSelection } from '@/app/shared/models/tax-selection';
import { TabGroupComponent } from '@/app/shared/molecules/tab-group/tab-group.component';
import { ServerDialogService } from '@/app/shared/services/server-dialog.service';

@Component({
  selector: 'fish-digitize-tab-group',
  imports: [
    TabComponent,
    TabGroupComponent,
    PersonalDataStepComponent,
    TranslateModule,
    QualificationProofStepComponent,
    PreviousPaymentsStepComponent,
    PaymentsStepComponent,
    DocumentsStepComponent,
    TabDisabledPipe,
  ],
  templateUrl: './digitize-tab-group.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DigitizeTabGroupComponent implements AfterViewInit, OnDestroy {
  protected readonly isLoading: WritableSignal<boolean> = signal(false);

  protected readonly formSent: WritableSignal<boolean> = signal(false);

  protected readonly lastEditableIndex: WritableSignal<number> = signal(0);

  protected readonly previouslyPayedTax = signal<TaxSelection | null>(null);

  protected readonly registerEntryId = signal<null | string>(null);

  protected readonly documents = signal<IdentificationDocument[]>([]);

  protected readonly LicenseType = LicenseType;

  protected readonly IdentificationDocumentsMailTemplateType = IdentificationDocumentsMailTemplateType;

  // Fields
  @ViewChild(TabGroupComponent) private readonly tabGroup!: TabGroupComponent;

  @ViewChild(PersonalDataStepComponent) private readonly personalDataStep!: EditFormStep<PersonalDataStepFormGroup>;

  @ViewChild(QualificationProofStepComponent) private readonly qualificationProofStep!: QualificationProofStepComponent;

  @ViewChild(PreviousPaymentsStepComponent) private readonly previousPaymentsStep!: PreviousPaymentsStepComponent;

  @ViewChild(PaymentsStepComponent) private readonly paymentsStep!: PaymentsStepComponent;

  // Dependencies
  private readonly profileHeaderStore: ProfileHeaderStore = inject(ProfileHeaderStore);

  private readonly consentStore: ConsentStore = inject(ConsentStore);

  private readonly citizenStore: CitizenStore = inject(CitizenStore);

  private readonly licenseServiceApi: FishingLicenseService = inject(FishingLicenseService);

  private readonly personBuilder: RequestPersonBuilderService = inject(RequestPersonBuilderService);

  private readonly proofBuilder: RequestProofBuilderService = inject(RequestProofBuilderService);

  private readonly paymentsBuilder: RequestPaymentsBuilderService = inject(RequestPaymentsBuilderService);

  private readonly serverDialogService: ServerDialogService = inject(ServerDialogService);

  public ngAfterViewInit(): void {
    this.setLastEditableIndex();
    this.setPreviouslyPayedTaxesForPaymentStep();
  }

  public ngOnDestroy(): void {
    this.profileHeaderStore.setHomeButtonType('secondary');
  }

  protected handleSave(): void {
    this.isLoading.set(true);
    const request = this.buildDigitizeFishingLicenseRequest();

    this.licenseServiceApi
      .digitizeControllerDigitize(request)
      .pipe(
        catchError((err: unknown) => this.serverDialogService.handleServerError(err)),
        finalize(() => this.isLoading.set(false))
      )
      .subscribe((response) => this.handleSuccessfulResponse(response));
  }

  private buildDigitizeFishingLicenseRequest(): DigitizeFishingLicenseRequest {
    return {
      person: this.personBuilder.buildPerson(this.personalDataStep.formGroup.value),
      payedTaxes: this.paymentsBuilder.buildPreviousTaxes(this.previousPaymentsStep.formGroup.value),
      fees: this.paymentsBuilder.buildFees(LicenseType.Regular, this.paymentsStep.formGroup.value),
      taxes: this.paymentsBuilder.buildTaxes(this.paymentsStep.formGroup.value),
      qualificationsProofs: this.proofBuilder.buildQualificationsProof(this.qualificationProofStep.formGroup.value),
      consentInfo: this.consentStore.getConsentInfo(),
    };
  }

  private handleSuccessfulResponse(response: DigitizeFishingLicenseResponse): void {
    const registerEntryId = response.registerEntryId;
    if (!registerEntryId) {
      throw new Error('Inconsistent server state: registerEntryId is null');
    }
    const documents = response.documents;
    if (documents?.length) {
      this.documents.set(documents);
    }

    this.citizenStore.patchByResponse(response);
    this.registerEntryId.set(registerEntryId);
    this.profileHeaderStore.setHomeButtonType('primary');
    this.formSent.set(true);

    setTimeout(() => this.tabGroup.goToLastTab(), 1);
  }

  private setLastEditableIndex(): void {
    if (isDevMode()) {
      this.lastEditableIndex.set(3);
    } else {
      combineLatest([this.personalDataStep.canContinue$, this.qualificationProofStep.canContinue$, this.previousPaymentsStep.canContinue$]).subscribe(
        (arr) => {
          const latestValid: number = arr.lastIndexOf(true);
          const firstInvalid: number = arr.indexOf(false);
          const lastEditable: number = firstInvalid >= 0 ? firstInvalid : latestValid + 1;

          this.lastEditableIndex.set(lastEditable);
        }
      );
    }
  }

  private setPreviouslyPayedTaxesForPaymentStep(): void {
    this.previousPaymentsStep.formGroup.valueChanges.pipe(distinctUntilChanged()).subscribe(({ validTo, validFrom, previouslyPayedTaxChecked }) => {
      if (!previouslyPayedTaxChecked) {
        this.previouslyPayedTax.set(null);

        return;
      }

      this.previouslyPayedTax.set({
        yearFrom: validFrom ?? new Date().getFullYear(),
        yearTo: validTo ?? null,
        cost: 0,
      });
    });
  }
}
