<fish-tab-group>
  <fish-tab [disabled]="formSent()" [label]="'pay_tax_form.tab_titles.payments' | translate">
    @if (!formSent()) {
      <fish-payments-step
        (saveButtonClicked)="handleSave()"
        [isLoading]="isLoading()"
        [licenseType]="LicenseType.Regular"
        [showBackButton]="false"
        [showFee]="false"
        [isTaxCheckboxDisabled]="true"
        [isTaxOptional]="false"
      >
      </fish-payments-step>
    }
  </fish-tab>
  <fish-tab [disabled]="!formSent()" [label]="'pay_tax_form.tab_titles.documents' | translate" section="documents">
    <fish-documents-step
      [identificationDocumentsMailTemplateType]="IdentificationDocumentsMailTemplateType.FishingTaxCreated"
      [documents]="documents()"
      [registerEntryId]="registerEntryId()!"
      [showLicenseCardBox]="false"
    ></fish-documents-step>
  </fish-tab>
</fish-tab-group>
