<fish-tab-group #tabGroup data-testid="create-person-tab-group">
  <fish-tab [disabled]="0 | tabDisabled: lastEditableIndex() : formSent()" [label]="'person_create_form.tab_titles.personal_data' | translate">
    @if (!formSent()) {
      <fish-personal-base-data-step (continueButtonClicked)="tabGroup.goNext()" data-testid="create-person-tab-personal-data" />
    }
  </fish-tab>
  <fish-tab [disabled]="1 | tabDisabled: lastEditableIndex() : formSent()" [label]="'person_create_form.tab_titles.payments' | translate">
    @if (!formSent()) {
      <fish-payments-step
        (backButtonClicked)="tabGroup.goBack()"
        (saveButtonClicked)="handleSave()"
        [isLoading]="isLoading()"
        [licenseType]="LicenseType.Regular"
        [showFee]="false"
        [isTaxCheckboxDisabled]="true"
        [isTaxOptional]="false"
        data-testid="create-person-tab-payments"
      />
    }
  </fish-tab>
  <fish-tab [disabled]="!formSent()" [label]="'person_create_form.tab_titles.documents' | translate" section="documents">
    <fish-documents-step
      [identificationDocumentsMailTemplateType]="IdentificationDocumentsMailTemplateType.FishingTaxCreated"
      [documents]="documents()"
      [registerEntryId]="registerEntryId()!"
      [showLicenseCardBox]="false"
      data-testid="create-person-tab-documents"
    ></fish-documents-step>
  </fish-tab>
</fish-tab-group>
