import { ChangeDetectionStrategy, Component, OnInit, inject } from '@angular/core';
import { ActivatedRoute } from '@angular/router';

import { RegisterEntryResponse, RegisterEntryService } from '@digifischdok/ngx-register-sdk';

import { PageContentComponent } from '@/app/core/layout/page-content/page-content.component';
import { RouteParamService } from '@/app/core/services/route-param.service';
import { CitizenStore } from '@/app/core/stores/citizen.store';
import { MoveJurisdictionTabGroupComponent } from '@/app/features/register-edit/components/templates/move-jurisdiction-tab-group/move-jurisdiction-tab-group.component';

@Component({
  selector: 'fish-move-jurisdiction-page',
  imports: [PageContentComponent, MoveJurisdictionTabGroupComponent],
  templateUrl: './move-jurisdiction-page.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MoveJurisdictionPageComponent implements OnInit {
  protected registerEntryId: string | null;

  private readonly registerService: RegisterEntryService = inject(RegisterEntryService);

  private readonly citizenStore: CitizenStore = inject(CitizenStore);

  constructor(
    private readonly route: ActivatedRoute,
    private readonly routeParamService: RouteParamService
  ) {
    this.registerEntryId = routeParamService.getParamOrFail('registerEntryId', route);
  }

  public ngOnInit(): void {
    if (this.registerEntryId) {
      this.registerService.registerEntriesControllerGet(this.registerEntryId).subscribe((registerEntry: RegisterEntryResponse) => {
        this.citizenStore.setProfile(registerEntry);
      });
    }
  }
}
