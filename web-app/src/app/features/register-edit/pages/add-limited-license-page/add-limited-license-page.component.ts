import { ChangeDetectionStrategy, Component, OnInit, inject } from '@angular/core';
import { ActivatedRoute } from '@angular/router';

import { PageContentComponent } from '@/app/core/layout/page-content/page-content.component';
import { RouteParamService } from '@/app/core/services/route-param.service';
import { CitizenStore } from '@/app/core/stores/citizen.store';
import { CreateLimitedLicenseTabGroupComponent } from '@/app/features/register-edit/components/templates/create-limited-license-tab-group/create-limited-license-tab-group.component';

@Component({
  selector: 'fish-add-limited-license-page',
  imports: [CreateLimitedLicenseTabGroupComponent, PageContentComponent],
  templateUrl: './add-limited-license-page.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AddLimitedLicensePageComponent implements OnInit {
  // DEPENDENCIES
  private readonly citizenStore = inject(CitizenStore);

  private readonly route = inject(ActivatedRoute);

  private readonly routeParamService = inject(RouteParamService);

  public ngOnInit(): void {
    const registerEntryId: string = this.routeParamService.getParamOrFail('registerEntryId', this.route);
    this.citizenStore.fetchAndSetProfile(registerEntryId);
  }
}
