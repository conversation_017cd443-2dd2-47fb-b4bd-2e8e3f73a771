import { ChangeDetectionStrategy, Component, inject } from '@angular/core';

import { TranslateModule, TranslateService } from '@ngx-translate/core';

import { PageContentComponent } from '@/app/core/layout/page-content/page-content.component';
import { ProfileHeaderStore } from '@/app/core/stores/profile-header.store';
import { DigitizeTabGroupComponent } from '@/app/features/register-edit/components/templates/digitize-tab-group/digitize-tab-group.component';

@Component({
  selector: 'fish-digitize-license-page',
  imports: [PageContentComponent, DigitizeTabGroupComponent, TranslateModule],
  templateUrl: './digitize-license-page.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DigitizeLicensePageComponent {
  private readonly profileHeaderStore: ProfileHeaderStore = inject(ProfileHeaderStore);

  private readonly translate: TranslateService = inject(TranslateService);

  constructor() {
    this.translate.get('digitize.title').subscribe((text: string): void => this.profileHeaderStore.setText(text));
  }
}
