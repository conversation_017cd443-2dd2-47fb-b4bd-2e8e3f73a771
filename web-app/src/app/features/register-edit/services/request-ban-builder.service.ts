import { formatDate } from '@angular/common';
import { Injectable } from '@angular/core';

import { PermanentBan, TemporaryBan } from '@digifischdok/ngx-register-sdk';

import { BanPeriodFormValues } from '@/app/features/register-edit/components/organisms/ban-period-form/ban-period-form.models';
import { BanReasonFormValues } from '@/app/features/register-edit/components/organisms/ban-reason-form/ban-reason-form.models';

@Injectable({
  providedIn: 'root',
})
export class RequestBanBuilderService {
  public buildPermanentBan(banReasonFormValues: BanReasonFormValues, banPeriodFormValues: BanPeriodFormValues): PermanentBan {
    if (banPeriodFormValues.type !== 'permanent') {
      throw new Error(
        `A Permanent Ban request can only be created when the ban period type is "permanent". Current type: ${banPeriodFormValues.type}`
      );
    }
    return {
      from: formatDate(banPeriodFormValues.permanent?.from ?? '', 'dd.MM.yyyy', 'de'),
      fileNumber: banReasonFormValues.fileNumber ?? '',
      reportedBy: banReasonFormValues.reportedBy ?? '',
    };
  }

  public buildTemporaryBan(banReasonFormValues: BanReasonFormValues, banPeriodFormValues: BanPeriodFormValues): TemporaryBan {
    if (banPeriodFormValues.type !== 'temporary') {
      throw new Error(
        `A Temporary Ban request can only be created when the ban period type is "temporary". Current type: ${banPeriodFormValues.type}`
      );
    }
    return {
      from: formatDate(banPeriodFormValues.temporary?.from ?? '', 'dd.MM.yyyy', 'de'),
      to: formatDate(banPeriodFormValues.temporary?.to ?? '', 'dd.MM.yyyy', 'de'),
      fileNumber: banReasonFormValues.fileNumber ?? '',
      reportedBy: banReasonFormValues.reportedBy ?? '',
    };
  }
}
