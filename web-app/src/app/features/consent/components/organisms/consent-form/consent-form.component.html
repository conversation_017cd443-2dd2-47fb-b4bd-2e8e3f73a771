<div class="my-5 flex flex-col gap-4" data-testid="consent-form">
  <div class="flex w-full flex-row gap-4">
    <fish-confirm-box
      [class]="'grow'"
      [formControl]="formGroup.controls.gdpr"
      [label]="'consent.gdpr.label' | translate"
      [subLabel]="'consent.gdpr.sub_label' | translate"
      data-testid="consent-form-gdpr"
    >
    </fish-confirm-box>
    <fish-consent-linkbox></fish-consent-linkbox>
  </div>
  <div>
    <fish-confirm-box-large
      [formControl]="formGroup.controls.selfDisclosure"
      [label]="'consent.self_disclosure.label' | translate"
      [subLabel]="'consent.self_disclosure.sublabel' | translate"
      data-testid="consent-form-self-disclosure"
    >
      <fish-list class="flex-1 gap-4 pb-6 pl-8 pr-8 pt-4 shadow-glass-white-tint">
        <fish-list-item>
          {{ 'consent.self_disclosure.offenses._1' | translate }}
        </fish-list-item>
        <fish-list-item>
          {{ 'consent.self_disclosure.offenses._2' | translate }}
        </fish-list-item>
        <fish-list-item>
          {{ 'consent.self_disclosure.offenses._3' | translate }}
        </fish-list-item>
      </fish-list>
      <fish-list class="flex-1 gap-4 pb-6 pl-8 pr-8 pt-4 shadow-glass-white-tint">
        <fish-list-item>
          {{ 'consent.self_disclosure.offenses._4' | translate }}
        </fish-list-item>
      </fish-list>
      <fish-list class="flex-1 pb-6 pl-8 pr-8 pt-4 shadow-glass-white-tint">
        <fish-list-item>
          {{ 'consent.self_disclosure.offenses._5' | translate }}
        </fish-list-item>
      </fish-list>
    </fish-confirm-box-large>
  </div>
  @if (!isFormValid()) {
    <fish-consent-form-error-message />
  }
</div>
