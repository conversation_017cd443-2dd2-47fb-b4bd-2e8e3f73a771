import { Injectable } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';

import { TranslateService } from '@ngx-translate/core';
import { forkJoin } from 'rxjs';

import { RouterService } from '@/app/core/services/router.service';
import { CitizenStore } from '@/app/core/stores/citizen.store';
import { ProfileHeaderStore } from '@/app/core/stores/profile-header.store';
import { UuidExtractionService } from '@/app/shared/services/uuid-extraction.service';

@Injectable({
  providedIn: 'root',
})
export class ConsentRouteService {
  constructor(
    private readonly router: Router,
    private readonly translate: TranslateService,
    private readonly uuidExtractionService: UuidExtractionService,
    private readonly routerService: RouterService,
    private readonly title: Title,
    private readonly profileHeaderStore: ProfileHeaderStore,
    private readonly citizenStore: CitizenStore
  ) {}

  public setupRouteInfo(route: ActivatedRoute): string {
    const callbackUrl = route.snapshot.url.join('/');
    if (!callbackUrl) {
      this.handleMissingCallbackUrl();
    }

    const registerEntryId = this.uuidExtractionService.extractRegisterEntryId(callbackUrl);
    if (registerEntryId) {
      this.citizenStore.fetchAndSetProfile(registerEntryId);
    }

    const callbackPageTitleKey = this.getCallbackPageTitleKey(route);
    const consentPageTitleKey = this.getConsentPageTitleKey(route);

    if (!callbackPageTitleKey) {
      this.handleInvalidCallbackPage();
    }

    if (!consentPageTitleKey) {
      this.handleMissingConsentPageTitle();
    }

    this.updatePageTitle(consentPageTitleKey!, callbackPageTitleKey!);

    return callbackUrl;
  }

  private handleMissingCallbackUrl(): void {
    this.router.navigate(['/not-found']).catch((err) => {
      console.error('Navigation error:', err);
    });
    throw new Error('No Callback URL for Consent provided.');
  }

  private handleInvalidCallbackPage(): void {
    this.router.navigate(['/not-found']).catch((err) => {
      console.error('Navigation error:', err);
    });
    throw new Error('No Proper Callback Page given');
  }

  private handleMissingConsentPageTitle(): void {
    throw new Error('the Consent Page titleKey is not properly set');
  }

  private getCallbackPageTitleKey(route: ActivatedRoute): string | undefined {
    const callbackRouteData = this.routerService.getRouteData(route.snapshot.url.join('/'));
    return callbackRouteData ? callbackRouteData['titleKey'] : undefined;
  }

  private getConsentPageTitleKey(route: ActivatedRoute): string | undefined {
    return route.snapshot.data?.['titleKey'];
  }

  private updatePageTitle(consentPageTitleKey: string, callbackPageTitleKey: string): void {
    forkJoin([this.translate.get('application.prefix'), this.translate.get(consentPageTitleKey), this.translate.get(callbackPageTitleKey)]).subscribe(
      ([applicationPrefixTitle$, consentPageTitle$, callbackPageTitle$]) => {
        const pageContentTitle = `${consentPageTitle$} - ${callbackPageTitle$}`;
        const pageTitle = `${applicationPrefixTitle$} - ${callbackPageTitle$}`;
        this.profileHeaderStore.setText(pageContentTitle);
        this.title.setTitle(pageTitle);
      }
    );
  }
}
