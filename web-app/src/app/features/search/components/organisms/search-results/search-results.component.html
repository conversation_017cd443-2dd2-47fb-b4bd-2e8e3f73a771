<div aria-live="polite" aria-atomic="true" class="float-right mb-2 flex w-fit gap-2 rounded-full border-2 border-border-divider px-4 text-s">
  <span [innerText]="'search.results.count' | translate"></span>
  <span class="font-bold" [innerText]="results().length"></span>
</div>
<div class="flex max-h-[60vh] w-full flex-col justify-stretch gap-1 overflow-auto" data-testid="search-results" aria-live="polite">
  <fish-table-header>
    <fish-table-cell class="flex-1">
      {{ 'profile.info.name' | translate }}
    </fish-table-cell>
    <fish-table-cell class="w-[140px]">
      {{ 'profile.info.birthdate' | translate }}
    </fish-table-cell>
    <fish-table-cell class="flex-1">
      {{ 'profile.info.birthplace' | translate }}
    </fish-table-cell>
    <fish-table-cell class="w-[363px]">
      {{ 'profile.info.identification' | translate }}
    </fish-table-cell>
  </fish-table-header>

  @for (entry of results(); track entry.registerId) {
    <fish-search-table-row [entry]="entry" [searchQuery]="searchQuery()" data-testid="search-results-row"></fish-search-table-row>
  }
</div>
