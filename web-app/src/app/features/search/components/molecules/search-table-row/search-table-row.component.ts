import { ChangeDetectionStrategy, Component, Input } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';

import { SearchItem } from '@digifischdok/ngx-register-sdk';

import { HighlightSearchResultPipe } from '@/app/features/search/components/molecules/search-table-row/pipes/highlight-search-result.pipe';
import { SearchResultTableRow } from '@/app/features/search/components/organisms/search-results/search-results.models';
import { InspectionLinkComponent } from '@/app/shared/atoms/inspection-link/inspection-link.component';
import { TableCellComponent } from '@/app/shared/atoms/table-cell/table-cell.component';
import { TableRowComponent } from '@/app/shared/atoms/table-row/table-row.component';
import { DocumentNumberPipe } from '@/app/shared/pipes/document-number.pipe';
import { PersonFormatterService } from '@/app/shared/services/person-formatter.service';

@Component({
  selector: 'fish-search-table-row',
  imports: [TableCellComponent, InspectionLinkComponent, TableRowComponent, HighlightSearchResultPipe, TranslateModule, DocumentNumberPipe],
  templateUrl: './search-table-row.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SearchTableRowComponent {
  @Input({ required: true }) public searchQuery!: string;

  @Input({ required: true }) public entry!: SearchItem;

  constructor(private readonly personFormatter: PersonFormatterService) {}

  protected get searchResultTableRow(): SearchResultTableRow {
    return {
      birthdate: this.entry?.person?.birthdate ?? '',
      name: this.personFormatter.formatName(this.entry.person),
      birthplace: this.entry?.person?.birthplace ?? '',
      identificationNumbers: this.getIdentificationNumbers(this.entry),
    };
  }

  private getIdentificationNumbers(entry: SearchItem): string[] {
    const identificationNumbers = [];
    if (entry.fishingLicenses) {
      identificationNumbers.push(...entry.fishingLicenses.map((license) => license.number));
    }
    if (entry.qualificationsProofs) {
      identificationNumbers.push(...entry.qualificationsProofs.map((qualificationProof) => qualificationProof.fishingCertificateId));
    }
    return identificationNumbers;
  }
}
