import { As<PERSON><PERSON><PERSON><PERSON>, NgIf } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { FormBuilder, FormControl } from '@angular/forms';

import { TranslateModule } from '@ngx-translate/core';
import { Observable, distinctUntilChanged, map, startWith } from 'rxjs';

import { ButtonComponent } from '@/app/shared/atoms/button/button.component';
import { ButtonType } from '@/app/shared/atoms/button/button.models';
import { InputComponent } from '@/app/shared/atoms/input/input.component';
import { IconCancelComponent } from '@/app/shared/icons/cancel/cancel.component';
import { IconSearchComponent } from '@/app/shared/icons/search/search.component';

@Component({
  selector: 'fish-search-bar',
  templateUrl: './search-bar.component.html',
  imports: [ButtonComponent, InputComponent, IconSearchComponent, TranslateModule, IconCancelComponent, NgIf, AsyncPipe],
})
export class SearchBarComponent implements OnInit {
  @Input() public defaultValue: string = '';

  @Input() public searchButtonType: ButtonType = 'primary';

  @Output() public searched = new EventEmitter<string>();

  @ViewChild(InputComponent) protected readonly inputComponent!: InputComponent;

  protected readonly inputControl: FormControl<string | null>;

  protected showClearButton$!: Observable<boolean>;

  /**
   *
   */
  constructor(formBuilder: FormBuilder) {
    this.inputControl = formBuilder.control('');
  }

  public ngOnInit(): void {
    this.inputControl.setValue(this.defaultValue);
    this.showClearButton$ = this.inputControl.valueChanges.pipe(
      startWith(this.defaultValue),
      map((value) => !!value?.length),
      distinctUntilChanged()
    );
  }

  protected handleSearch(): void {
    const searchValue = this.inputControl.value;
    this.searched.emit(searchValue ?? '');
  }

  protected handleClear(): void {
    this.inputControl.setValue('');
    this.inputComponent.getInputNativeElement().focus();
  }
}
