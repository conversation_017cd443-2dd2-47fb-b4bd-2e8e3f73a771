<div class="inline-flex w-full flex-wrap justify-between">
  <div class="inline-flex items-center gap-1">
    <fish-icon-fishing-tax class="text-font-primary" size="32" />
    <span [innerText]="userFederalState() | displayFederalState | async" class="font-bold text-font-secondary"></span>
  </div>
  <div class="inline-flex items-center gap-1">
    @if (sortedTaxes().length === 0) {
      <fish-badge type="warning">
        <span [innerText]="'service_overview.tax_card.none' | translate"></span>
      </fish-badge>
    } @else {
      @for (tax of sortedTaxes(); let index = $index; track index) {
        <fish-badge type="primary">
          <span [innerText]="tax | displayTaxPeriodPipe"></span>
        </fish-badge>
      }
    }
  </div>
</div>
