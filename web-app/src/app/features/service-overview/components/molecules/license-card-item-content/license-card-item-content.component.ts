import { DatePipe, NgClass } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, input } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';

import { LicenseType, ValidityPeriod } from '@digifischdok/ngx-register-sdk';

import { LicenseCardItemType } from '@/app/features/service-overview/components/organisms/license-service-card/license-service-card.models';
import { IconNumberedListOne } from '@/app/shared/icons/numbered-list-one/numbered-list-one.component';
import { IconNumberedListTwo } from '@/app/shared/icons/numbered-list-two/numbered-list-two.component';

@Component({
  selector: 'fish-license-card-item-content',
  imports: [DatePipe, TranslateModule, NgClass, IconNumberedListOne, IconNumberedListTwo],
  templateUrl: './license-card-item-content.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LicenseCardItemContentComponent {
  public readonly type = input.required<LicenseCardItemType>();

  // In case this displays a vacation license (it might not), this is used to display multiple periods
  public readonly validityPeriods = input<ValidityPeriod[]>([]);

  public readonly validFrom = input.required<string>();

  public readonly validTo = input<string | undefined>();

  /**
   * The first 2 validity periods or the only first, with dates parsed.
   * @protected
   */
  protected readonly vacationValidityPeriods = computed<(ValidityPeriod | null)[]>(() => {
    const periods: (ValidityPeriod | null)[] = this.validityPeriods().slice(0, 2);

    // There should always be the second option, only with no period information
    if (periods.length === 1) {
      periods.push(null);
    }

    return periods;
  });

  /**
   * Helper function to highlight the last defined validTo date.
   */
  protected shouldValidToBeBold(index: number): boolean {
    return index === this.validityPeriods().length - 1;
  }

  protected readonly LicenseType = LicenseType;
}
