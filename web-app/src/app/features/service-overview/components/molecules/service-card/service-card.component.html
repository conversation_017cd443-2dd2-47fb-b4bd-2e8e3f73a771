<fish-focus-ring class="h-full w-full">
  <div
    [ngClass]="{ 'pointer-events-none cursor-not-allowed text-action-text-disabled shadow-none [&_svg]:opacity-80 [&_svg]:grayscale': disabled() }"
    class="group flex h-full flex-col items-stretch rounded-lg border border-border-divider bg-background-glass-primary text-action-primary shadow-glass-white-tint backdrop-blur-xl transition-all duration-240 hover:border-action-border hover:bg-action-secondary-hover hover:shadow-glass-blue-tint focus:bg-action-secondary-hover focus:shadow-glass-blue-tint active:bg-action-secondary-pressed active:shadow-glass-blue-tint-pressed"
  >
    <fieldset [disabled]="disabled()" class="flex flex-grow flex-col items-stretch">
      <ng-content select="fish-service-card-header" />
      <div
        [ngClass]="{ 'blur-3xl': menuIsOpen() }"
        class="flex h-0 shrink flex-grow flex-col items-stretch overflow-y-auto transition-all duration-240"
      >
        <ng-content></ng-content>
      </div>
      <div class="h-20">
        <ng-content select="fish-service-card-footer"></ng-content>
      </div>
    </fieldset>
  </div>
</fish-focus-ring>
