<div
  [ngClass]="{ 'blur-3xl': menuIsOpen() }"
  class="flex h-20 shrink grow items-start justify-end border-t border-border-divider bg-background-glass-secondary px-2 py-4 backdrop-blur-3xl transition-all duration-240"
>
  <div class="inline-flex h-12 w-full items-center justify-end">
    @if (!creationMenuButtonOnly()) {
      <ng-container *ngIf="buttonOverride; else defaultButton">
        <ng-container *ngTemplateOutlet="buttonOverride.templateRef"></ng-container>
      </ng-container>
      <ng-template #defaultButton>
        <fish-button (clicked)="created.emit()" class="w-full" type="secondary" data-testid="service-card-create-button">
          <fish-icon-plus icon size="48" />
        </fish-button>
      </ng-template>
    }
  </div>
</div>
@if (showCreationMenu()) {
  <div class="absolute bottom-3 left-2 z-50" [ngClass]="{ 'right-2': creationMenuButtonOnly() }">
    <fish-button
      type="secondary"
      cdkOverlayOrigin
      #overlayOrigin="cdkOverlayOrigin"
      data-testid="service-card-create-button"
      (clicked)="menuIsOpen() ? closeMenu() : openMenu()"
      [ngClass]="{ 'w-full': creationMenuButtonOnly() }"
    >
      <fish-icon-plus icon size="48" [ngClass]="{ 'rotate-[135deg]': menuIsOpen() }" class="transition-all duration-240" />
    </fish-button>
  </div>
  <ng-template
    cdkConnectedOverlay
    [cdkConnectedOverlayOrigin]="overlayOrigin"
    [cdkConnectedOverlayWidth]="overlayOrigin?.elementRef?.nativeElement?.offsetWidth"
    [cdkConnectedOverlayHeight]="overlayOrigin?.elementRef?.nativeElement?.offsetHeight"
    [cdkConnectedOverlayOpen]="menuIsOpen()"
    [cdkConnectedOverlayPositions]="[
      {
        originX: 'start',
        originY: 'top',
        overlayX: 'start',
        overlayY: 'bottom',
      },
    ]"
    [cdkConnectedOverlayHasBackdrop]="true"
    (backdropClick)="closeMenu()"
    cdkConnectedOverlayBackdropClass="bg-transparent"
    [cdkConnectedOverlayScrollStrategy]="scrollStrategy"
  >
    <div @overlayAnimation class="flex h-full w-full flex-col justify-end gap-4 px-1 py-4">
      <div class="flex flex-col items-start justify-start gap-2">
        <ng-content select="[menu-items]" />
      </div>
    </div>
  </ng-template>
}
