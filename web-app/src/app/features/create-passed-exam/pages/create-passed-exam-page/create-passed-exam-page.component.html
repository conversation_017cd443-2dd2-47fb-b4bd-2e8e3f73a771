<fish-loading-overlay />
<fish-page-content data-testid="create-passed-exam-page">
  @if (activeTabSignal() === 'feedback') {
    <fish-create-passed-exam-feedback [data]="createPassedExamService.feedbackValues()" (restartButtonClicked)="restartProcess()" />
  } @else {
    <div class="mb-12 flex flex-col gap-12 lg:flex-row">
      <fish-create-passed-exam-aside class="w-full lg:w-1/4 lg:min-w-[285px]" />

      @if (activeTabSignal() === 'summary') {
        <fish-create-passed-exam-summary
          [data]="createPassedExamService.formGroupValues()"
          class="w-full lg:min-w-24 lg:flex-grow"
          data-testid="exam-page-summary"
        />
      } @else {
        <fish-create-passed-exam-form
          [preventLeaving]="true"
          class="w-full lg:min-w-24 lg:flex-grow"
          data-testid="exam-page-form"
          [defaultValues]="createPassedExamService.formGroupValues()"
          (statusChanged)="handleStatusChanged()"
        />
      }
    </div>
    <fish-create-passed-exam-footer [activeTab]="activeTabSignal()" (activeTabChange)="onContinue($event)" (send)="sendPassedExam()" />
  }
</fish-page-content>
