import { FormControl, FormGroup } from '@angular/forms';

export type CreatePassedExamKeys = 'title' | 'firstname' | 'lastname' | 'birthname' | 'birthdate' | 'birthplace' | 'passedOn';

export type CreatePassedExamValues = Partial<Record<CreatePassedExamKeys, string | null>>;

export type CreatePassedExam = Record<CreatePassedExamKeys, FormControl<string | null>>;

export type CreatePassedExamFormGroup = FormGroup<CreatePassedExam>;

export type CreatePassedExamFormValues = CreatePassedExamFormGroup['value'];

export type CreatePassedExamTabs = 'form' | 'summary' | 'feedback';

export type CreatePassedExamFeedbackValues = Pick<CreatePassedExamValues, 'firstname' | 'lastname' | 'passedOn'> & {
  examinationFacility: string;
  examCode: string;
};
