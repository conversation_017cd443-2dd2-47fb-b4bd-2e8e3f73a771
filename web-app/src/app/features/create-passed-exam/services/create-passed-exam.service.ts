import { DatePipe } from '@angular/common';
import { Injectable, Signal, WritableSignal, computed, inject, signal } from '@angular/core';

import { KeycloakService } from 'keycloak-angular';
import { Observable } from 'rxjs';

import { PreliminaryRegisterEntryService } from '@digifischdok/ngx-register-sdk';

import {
  CreatePassedExamFeedbackValues,
  CreatePassedExamFormGroup,
  CreatePassedExamKeys,
  CreatePassedExamValues,
} from '@/app/features/create-passed-exam/models/create-passed-exam.models';

@Injectable()
export class CreatePassedExamService {
  public readonly formGroupValues: WritableSignal<CreatePassedExamValues> = signal({});

  public readonly feedbackValues: Signal<CreatePassedExamFeedbackValues> = computed<CreatePassedExamFeedbackValues>(() => {
    const values = this.formGroupValues();
    return {
      firstname: values.firstname,
      lastname: values.lastname,
      passedOn: values.passedOn,
      examinationFacility: this.keycloakService.getKeycloakInstance().idTokenParsed?.['examination']?.issuer ?? '',
      examCode: this.examCode(),
    };
  });

  public readonly examCode: WritableSignal<string> = signal<string>('');

  private readonly preliminaryRegisterEntryService: PreliminaryRegisterEntryService = inject(PreliminaryRegisterEntryService);

  private readonly keycloakService: KeycloakService = inject(KeycloakService);

  private readonly datePipe: DatePipe = inject(DatePipe);

  public send$(): Observable<Record<'fishingCertificateId', string>> {
    const formValues: Partial<Record<CreatePassedExamKeys, string | null>> = this.formGroupValues();

    return this.preliminaryRegisterEntryService.preliminaryRegisterEntriesControllerCreate({
      person: {
        title: formValues.title ?? '',
        firstname: formValues.firstname ?? '',
        lastname: formValues.lastname ?? '',
        birthdate: formValues.birthdate ?? '',
        birthname: (formValues.birthname === '' ? formValues.lastname : formValues.birthname) ?? '',
        birthplace: formValues.birthplace ?? '',
      },
      certificate: {
        passedOn: this.datePipe.transform(formValues.passedOn, 'dd.MM.yyyy') ?? '',
      },
    });
  }

  public updateFormGroupValues(formGroup: CreatePassedExamFormGroup): void {
    this.formGroupValues.update((formGroupValues: CreatePassedExamValues) => {
      return { ...formGroupValues, ...formGroup.value };
    });
  }
}
