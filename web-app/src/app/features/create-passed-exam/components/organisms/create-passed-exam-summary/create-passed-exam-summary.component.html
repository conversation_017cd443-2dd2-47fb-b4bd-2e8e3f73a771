<div class="flex flex-col gap-8 pl-6 pr-12 pt-3">
  <div class="relative h-9"></div>
  <p [innerText]="'passed_exam_create.summary.header' | translate" class="font-sans text-l font-thin"></p>
  <table class="w-full table-fixed">
    <caption></caption>
    <thead>
      <tr>
        <th scope="col"></th>
        <th scope="col"></th>
      </tr>
    </thead>
    <tbody>
      @for (entry of entriesGenerator; track entry.key) {
        <tr [attr.data-testid]="'exam-summary-field' + entry.key">
          <td class="whitespace-nowrap py-2 font-bold text-font-secondary">
            <p [innerText]="'passed_exam_create.summary.' + entry.key | translate"></p>
          </td>
          <td class="whitespace-nowrap py-2">
            <p
              class="font-bold"
              [class]="entry.key === 'birthname' && entry.value === '' ? 'text-action-text-disabled' : 'text-action-primary'"
              [innerText]="
                entry.key === 'passedOn'
                  ? (entry.value?.toString() | date: 'dd.MM.yyyy')
                  : (entry | replacePassedExamValuesWith: data : 'birthname' : 'lastname')
              "
            ></p>
          </td>
        </tr>
      }
      <tr>
        <td class="whitespace-nowrap py-2 font-bold">
          <p
            [innerText]="'passed_exam_create.summary.examination_facility' | translate"
            class="w-fit rounded bg-background-glass-secondary px-4 py-1 font-bold text-action-text-disabled shadow-glass-white-tint"
          ></p>
        </td>
        <td class="bg-gray-200 text-gray-700 whitespace-nowrap rounded py-2">
          <p
            [innerText]="examinationFacility()"
            class="w-fit rounded bg-background-glass-secondary px-4 py-1 font-bold text-action-text-disabled shadow-glass-white-tint"
          ></p>
        </td>
      </tr>
    </tbody>
  </table>
</div>
