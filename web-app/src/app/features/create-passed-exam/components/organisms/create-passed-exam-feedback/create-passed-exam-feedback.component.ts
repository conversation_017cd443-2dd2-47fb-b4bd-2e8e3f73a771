import { DatePipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, InputSignal, OutputEmitterRef, computed, inject, input, output } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';
import { catchError } from 'rxjs';

import { PreliminaryRegisterEntryService } from '@digifischdok/ngx-register-sdk';

import { PageContentComponent } from '@/app/core/layout/page-content/page-content.component';
import { CreatePassedExamFeedbackValues } from '@/app/features/create-passed-exam/models/create-passed-exam.models';
import { CreatePassedExamService } from '@/app/features/create-passed-exam/services/create-passed-exam.service';
import { ButtonComponent } from '@/app/shared/atoms/button/button.component';
import { TableCellComponent } from '@/app/shared/atoms/table-cell/table-cell.component';
import { TableHeaderComponent } from '@/app/shared/atoms/table-header/table-header.component';
import { TableRowComponent } from '@/app/shared/atoms/table-row/table-row.component';
import { IconCertificateComponent } from '@/app/shared/icons/certificate/certificate.component';
import { IconPlusComponent } from '@/app/shared/icons/plus/plus.component';
import { IconPrintComponent } from '@/app/shared/icons/print/print.component';
import { DocumentNumberPipe } from '@/app/shared/pipes/document-number.pipe';
import { ServerDialogService } from '@/app/shared/services/server-dialog.service';

@Component({
  selector: 'fish-create-passed-exam-feedback',
  imports: [
    PageContentComponent,
    IconCertificateComponent,
    TableHeaderComponent,
    TableCellComponent,
    TableRowComponent,
    ButtonComponent,
    IconPrintComponent,
    IconPlusComponent,
    DatePipe,
    TranslateModule,
    DocumentNumberPipe,
  ],
  templateUrl: './create-passed-exam-feedback.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CreatePassedExamFeedbackComponent {
  public readonly data: InputSignal<CreatePassedExamFeedbackValues> = input.required<CreatePassedExamFeedbackValues>();

  public readonly restartButtonClicked: OutputEmitterRef<void> = output<void>();

  private readonly preliminaryRegisterEntryService: PreliminaryRegisterEntryService = inject(PreliminaryRegisterEntryService);

  private readonly createPassedExamService: CreatePassedExamService = inject(CreatePassedExamService);

  private readonly serverDialogService: ServerDialogService = inject(ServerDialogService);

  protected readonly name = computed<string>(() => {
    return `${this.data().firstname} ${this.data().lastname}`;
  });

  protected handlePrintButtonPressed(): void {
    const reqisterEntryId = this.createPassedExamService.examCode();
    this.preliminaryRegisterEntryService
      .fishingCertificateControllerGetPDF(reqisterEntryId)
      .pipe(catchError((err: unknown) => this.serverDialogService.handleServerError(err)))
      .subscribe((response) => {
        const blob = new Blob([response], { type: 'application/pdf' });
        const url = window.URL.createObjectURL(blob);
        window.open(url);
      });
  }

  protected handleRestartButtonClicked(): void {
    this.restartButtonClicked.emit();
  }
}
