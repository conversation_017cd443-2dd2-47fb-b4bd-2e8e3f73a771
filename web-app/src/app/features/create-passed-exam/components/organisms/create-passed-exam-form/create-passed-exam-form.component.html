<div [formGroup]="formGroup" class="pl-6 pr-12 pt-3">
  <fish-form-field
    [control]="formGroup.controls.title"
    [label]="'passed_exam_create.form.title.label' | translate"
    [maxLength]="80"
    [options]="titleOptions()"
    [placeholder]="'passed_exam_create.form.title.placeholder' | translate"
    class="inline-block w-1/2"
    data-testid="exam-page-field-title"
    type="combobox"
  />

  <fish-form-field
    [control]="formGroup.controls.firstname"
    [label]="'passed_exam_create.form.firstname.label' | translate"
    [maxLength]="80"
    [placeholder]="'passed_exam_create.form.firstname.placeholder' | translate"
    [showOptionalLabel]="false"
    data-testid="exam-page-field-firstname"
  />

  <fish-form-field
    [control]="formGroup.controls.lastname"
    [label]="'passed_exam_create.form.lastname.label' | translate"
    [maxLength]="120"
    [placeholder]="'passed_exam_create.form.lastname.placeholder' | translate"
    [showOptionalLabel]="false"
    data-testid="exam-page-field-lastname"
  />

  <fish-form-field
    [control]="formGroup.controls.birthname"
    [label]="'passed_exam_create.form.birthname.label' | translate"
    [maxLength]="120"
    [placeholder]="'passed_exam_create.form.birthname.placeholder' | translate"
    [subLabel]="'passed_exam_create.form.birthname.sublabel' | translate"
    data-testid="exam-page-field-birthname"
  />

  <fish-form-field
    [control]="formGroup.controls.birthdate"
    [errorMapping$]="birthdateErrorMapping$"
    [label]="'passed_exam_create.form.birthdate.label' | translate"
    [placeholder]="'passed_exam_create.form.birthdate.placeholder' | translate"
    [showOptionalLabel]="false"
    data-testid="exam-page-field-birthdate"
  />

  <fish-form-field
    [control]="formGroup.controls.birthplace"
    [label]="'passed_exam_create.form.birthplace.label' | translate"
    [maxLength]="120"
    [placeholder]="'passed_exam_create.form.birthplace.placeholder' | translate"
    [showOptionalLabel]="false"
    data-testid="exam-page-field-birthplace"
  />

  <fish-form-field
    [control]="formGroup.controls.passedOn"
    [errorMapping$]="passedOnErrorMapping$"
    [label]="'passed_exam_create.form.passedOn.label' | translate"
    [showOptionalLabel]="false"
    data-testid="exam-page-field-passedOn"
    type="date"
  />
</div>
