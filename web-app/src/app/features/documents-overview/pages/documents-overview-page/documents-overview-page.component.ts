import { ChangeDetectionStrategy, Component, OnInit, ViewChild, computed, inject, signal } from '@angular/core';
import { ActivatedRoute } from '@angular/router';

import { TranslateModule } from '@ngx-translate/core';
import { catchError, finalize, retry } from 'rxjs';

import {
  IdentificationDocument,
  IdentificationDocumentsMailTemplateType,
  IdentificationDocumentsService,
  LicenseType,
} from '@digifischdok/ngx-register-sdk';

import { PageContentComponent } from '@/app/core/layout/page-content/page-content.component';
import { RouteParamService } from '@/app/core/services/route-param.service';
import { CitizenStore } from '@/app/core/stores/citizen.store';
import { DocumentOverviewHeaderComponent } from '@/app/features/documents-overview/components/molecules/document-overview-header/document-overview-header.component';
import { IconFishingTaxComponent } from '@/app/shared/icons/fisching-tax/fishing-tax.component';
import { IconLicenseCardComponent } from '@/app/shared/icons/license-card/license-card.component';
import { IdentificationDocumentWithFederalState } from '@/app/shared/models/identification-document-with-federal-state';
import { DocumentItemComponent } from '@/app/shared/molecules/document-item/document-item.component';
import { SendDocumentsDialogComponent } from '@/app/shared/organisms/send-documents-dialog/send-documents-dialog.component';
import { SendDocumentsSuccessFeedbackComponent } from '@/app/shared/organisms/send-documents-success-feedback/send-documents-success-feedback.component';
import { DocumentOpenerService } from '@/app/shared/services/document-opener.service';
import { ServerDialogService } from '@/app/shared/services/server-dialog.service';
import { isLimitedLicenseApprovalDocument, isRelevantTaxDocument, isVisibleLicensePDFDocument } from '@/app/shared/utils/document.utils';

@Component({
  selector: 'fish-documents-overview-page',
  imports: [
    PageContentComponent,
    IconLicenseCardComponent,
    IconFishingTaxComponent,
    DocumentItemComponent,
    TranslateModule,
    DocumentOverviewHeaderComponent,
    SendDocumentsDialogComponent,
    SendDocumentsSuccessFeedbackComponent,
  ],
  templateUrl: './documents-overview-page.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DocumentsOverviewPageComponent implements OnInit {
  // Fields
  protected isSendingDocument = signal<boolean>(false);

  protected documentToFetch = signal<IdentificationDocument | undefined>(undefined);

  protected readonly fishingLicenseDocuments = computed<IdentificationDocument[]>(() => {
    return this.citizenStore.profile()?.identificationDocuments.filter(isVisibleLicensePDFDocument).sort(this.sortByValidFrom) ?? [];
  });

  protected readonly taxDocuments = computed<IdentificationDocument[]>(() => {
    return this.citizenStore.profile()?.identificationDocuments.filter(isRelevantTaxDocument).sort(this.sortByValidFrom) ?? [];
  });

  protected readonly limitedLicenseApprovalDocuments = computed<IdentificationDocumentWithFederalState[]>(() => {
    const fishingLicenses = this.fishingLicenseDocuments();

    const mapWithFederalState = (approval: IdentificationDocument): IdentificationDocumentWithFederalState => {
      const match = fishingLicenses.find(
        (license: IdentificationDocument) =>
          license.fishingLicense?.limitedLicenseApproval?.limitedLicenseApprovalId === approval.limitedLicenseApproval?.limitedLicenseApprovalId
      );
      return {
        ...approval,
        federalState: match?.fishingLicense?.issuingFederalState ?? '',
      };
    };

    return (
      this.citizenStore
        .profile()
        ?.identificationDocuments.filter(isLimitedLicenseApprovalDocument)
        .sort(this.sortByValidFrom)
        .map(mapWithFederalState) ?? []
    );
  });

  private readonly sortByValidFrom = (a: IdentificationDocument, b: IdentificationDocument): number => {
    const dateA = new Date(a.validFrom).getTime();
    const dateB = new Date(b.validFrom).getTime();
    return dateA - dateB; // Ascending order
  };

  private registerEntryId: string = '';

  private documentToSend?: IdentificationDocument;

  @ViewChild(SendDocumentsDialogComponent)
  private readonly sendDocumentsDialog!: SendDocumentsDialogComponent;

  @ViewChild(SendDocumentsSuccessFeedbackComponent)
  private readonly sendDocumentsSuccessFeedback!: SendDocumentsSuccessFeedbackComponent;

  // Dependencies
  private readonly route: ActivatedRoute = inject(ActivatedRoute);

  private readonly routeParamService: RouteParamService = inject(RouteParamService);

  private readonly citizenStore: CitizenStore = inject(CitizenStore);

  private readonly identificationDocumentsServiceApi: IdentificationDocumentsService = inject(IdentificationDocumentsService);

  private readonly serverDialogService: ServerDialogService = inject(ServerDialogService);

  private readonly documentOpener: DocumentOpenerService = inject(DocumentOpenerService);

  public ngOnInit(): void {
    this.registerEntryId = this.routeParamService.getParamOrFail('registerEntryId', this.route);
    this.citizenStore.fetchAndSetProfile(this.registerEntryId);
  }

  protected startDocumentSend(document: IdentificationDocument): void {
    this.documentToSend = document;
    this.sendDocumentsDialog.open();
  }

  protected sendDocument(email: string): void {
    const documentToSend = this.documentToSend;
    if (!documentToSend) {
      throw new Error('Tried sending a document but none was selected');
    }
    this.isSendingDocument.set(true);
    const templateType = this.getTemplateType(documentToSend);
    this.identificationDocumentsServiceApi
      .identificationDocumentsControllerSendMail(this.registerEntryId, [documentToSend.documentId], email, templateType)
      .pipe(
        retry({ count: 2, delay: 200 }),
        catchError((err: unknown) => this.serverDialogService.handleServerError(err)),
        finalize(() => {
          this.isSendingDocument.set(false);
        })
      )
      .subscribe(() => {
        this.sendDocumentsDialog.close();
        this.sendDocumentsSuccessFeedback.open();
      });
  }

  protected fetchDocument(document: IdentificationDocument): void {
    if (!document) {
      throw new Error('Tried fetching a document but none was selected');
    }
    this.documentToFetch.set(document);
    this.documentOpener.openDocument$(this.registerEntryId, document).subscribe({
      complete: () => this.documentToFetch.set(undefined),
    });
  }

  private getTemplateType(documentToSend: IdentificationDocument): IdentificationDocumentsMailTemplateType {
    if (documentToSend?.tax) {
      return IdentificationDocumentsMailTemplateType.FishingTaxInfo;
    }

    if (documentToSend?.fishingLicense) {
      switch (documentToSend.fishingLicense.type) {
        case LicenseType.Regular:
          return IdentificationDocumentsMailTemplateType.RegularFishingLicenseInfo;
        case LicenseType.Vacation:
          return IdentificationDocumentsMailTemplateType.VacationFishingLicenseInfo;
        case LicenseType.Limited:
          return IdentificationDocumentsMailTemplateType.LimitedFishingLicenseInfo;
        default:
          throw new Error('Unsupported fishing license type');
      }
    }

    throw new Error('Document does not contain tax or fishing license information');
  }

  protected readonly LicenseType = LicenseType;
}
