import { Meta, StoryObj, argsToTemplate, moduleMetadata } from '@storybook/angular';

import { ButtonComponent } from '@/app/shared/atoms/button/button.component';
import { NoticeActionsComponent } from '@/app/shared/atoms/notice-actions/notice-actions.component';
import { NoticeContentComponent } from '@/app/shared/atoms/notice-content/notice-content.component';
import { IconEditComponent } from '@/app/shared/icons/edit/edit.component';
import { IconMoveAuthorityComponent } from '@/app/shared/icons/move-authority/move-authority.component';
import { IconPhoneCallComponent } from '@/app/shared/icons/phone-call/phone-call.component';
import { IconUnlockComponent } from '@/app/shared/icons/unlock/unlock.component';
import { NoticeComponent } from '@/app/shared/molecules/notice/notice.component';

const meta: Meta<NoticeComponent> = {
  title: 'Notice',
  component: NoticeComponent,
  args: {
    type: 'info',
  },
  argTypes: {
    type: {
      control: 'radio',
      options: ['info', 'warning', 'banned'],
    },
  },
  render: (args) => ({
    props: {
      ...args,
    },
    template: `
     <fish-notice ${argsToTemplate(args)}>
        <fish-notice-content>
          <div class="">
            <div class="text-s">
              Soll der Umzug <b>fälschlicherweise</b> erfolgt sein, kontaktieren Sie unseren:
            </div>
            <div class="text-base font-thin">
              Helpdesk
            </div>
          </div>
        </fish-notice-content>

        <fish-notice-actions>
          <div class="flex items-center">
            <fish-icon-phone-call></fish-icon-phone-call>
            <div class="text-base mr-5 whitespace-nowrap">
              XXXX XXXX XXXX XXXX
            </div>
          </div>
        </fish-notice-actions>
     </fish-notice>
    `,
  }),
  decorators: [
    moduleMetadata({
      imports: [IconPhoneCallComponent, NoticeContentComponent, NoticeActionsComponent],
    }),
  ],
};

export default meta;

type Story = StoryObj<NoticeComponent>;

export const Default: Story = {};

export const Info: Story = {
  render: () => ({
    template: `
     <fish-notice type="info">
        <fish-notice-content>
          <div class="text-s">
            Aktuelle <b>Zuständigkeit</b> für <b>Dr. Florian Flunder</b> liegt in:
          </div>
          <div class="text-base font-thin">
            Sachsen
          </div>
        </fish-notice-content>
        <fish-notice-actions>
          <div class="flex items-center">
            <fish-button type="secondary">
                <fish-icon-move-authority size="s" icon></fish-icon-move-authority>
                Zuständigkeit Umziehen
            </fish-button>
          </div>
        </fish-notice-actions>
     </fish-notice>
    `,
  }),
  decorators: [
    moduleMetadata({
      imports: [IconMoveAuthorityComponent, ButtonComponent, NoticeContentComponent, NoticeActionsComponent],
    }),
  ],
};

export const Warning: Story = {
  render: () => ({
    template: `
     <fish-notice type="warning">
        <fish-notice-content>
          <div class="text-s py-1">
            <div class="font-bold">
              Diese Aktion kann nicht rückgängig gemacht werden.
            </div>
            <div>
              Wenden Sie sich im Falle eines Fehlers an einen Administrator
            </div>
          </div>
        </fish-notice-content>
     </fish-notice>
    `,
  }),
  decorators: [
    moduleMetadata({
      imports: [NoticeContentComponent, NoticeActionsComponent],
    }),
  ],
};

export const Banned: Story = {
  render: () => ({
    template: `
     <fish-notice type="banned">
        <fish-notice-content>
          <div class="text-s">
            <div class="font-bold">
              8 Feb 2024 — 8 Feb 2025
            </div>
            <div>
             2200-SH/8 obere Fischereibehörde Schleswig-Holstein
            </div>
          </div>
        </fish-notice-content>
        <fish-notice-actions>
          <div class="flex items-center gap-2 px-1">
            <fish-button type="secondary" classes="!text-feedback-text-error">
                <fish-icon-edit icon size="32"></fish-icon-edit>
                Bearbeiten
            </fish-button>
            <fish-button type="secondary" classes="!text-feedback-text-error">
                <fish-icon-unlock icon></fish-icon-unlock>
                Entsperren
            </fish-button>
          </div>
        </fish-notice-actions>
     </fish-notice>
    `,
  }),
  decorators: [
    moduleMetadata({
      imports: [ButtonComponent, IconEditComponent, IconUnlockComponent, NoticeContentComponent, NoticeActionsComponent],
    }),
  ],
};

export const TypeAreaFixedWidth: Story = {
  render: () => ({
    template: `
     <div class="flex flex-col gap-5">
       <fish-notice type="info" typeAreaClass="!w-[13.5rem]">
          <fish-notice-content>
            <div class="text-s py-1">
              <div class="font-bold">
                Das ist eine Info Notice
              </div>
              <div>
                hier gibt es wichtige infos
              </div>
            </div>
          </fish-notice-content>
       </fish-notice>

       <fish-notice type="warning" typeAreaClass="!w-[13.5rem]">
          <fish-notice-content>
            <div class="text-s py-1">
              <div class="font-bold">
                Das ist eine Warnung Notice
              </div>
              <div>
                hier gibt es was besorgniserregend
              </div>
            </div>
          </fish-notice-content>
       </fish-notice>
    </div>
    `,
  }),
  decorators: [
    moduleMetadata({
      imports: [ButtonComponent, IconEditComponent, IconUnlockComponent, NoticeContentComponent, NoticeActionsComponent],
    }),
  ],
};
