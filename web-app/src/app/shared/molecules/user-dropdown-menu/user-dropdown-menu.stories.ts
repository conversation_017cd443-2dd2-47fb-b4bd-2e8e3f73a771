import { type Meta, type StoryObj } from '@storybook/angular';

import { ProfileIconComponent } from '@/app/shared/atoms/profile-icon/profile-icon.component';
import { UserDropdownMenuComponent } from '@/app/shared/molecules/user-dropdown-menu/user-dropdown-menu.component';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
const meta: Meta<UserDropdownMenuComponent> = {
  title: 'UserDropdownMenu',
  component: UserDropdownMenuComponent,
  render: (args) => ({
    props: args,
    template: `
      <fish-user-dropdown-menu>
      </fish-user-dropdown-menu>
    `,
    moduleMetadata: {
      imports: [ProfileIconComponent],
    },
  }),
};

export default meta;
type Story = StoryObj<UserDropdownMenuComponent>;

export const UserDropdownMenu: Story = {};
