import { ChangeDetectionStrategy, Component, EventEmitter, Output } from '@angular/core';

import { ButtonComponent } from '@/app/shared/atoms/button/button.component';
import { IconCancelComponent } from '@/app/shared/icons/cancel/cancel.component';

@Component({
  selector: 'fish-dialog-header',
  imports: [ButtonComponent, IconCancelComponent],
  templateUrl: './dialog-header.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DialogHeaderComponent {
  @Output() public readonly closed = new EventEmitter<void>();
}
