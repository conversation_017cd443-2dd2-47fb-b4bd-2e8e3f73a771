import { type Meta, type StoryObj, argsToTemplate, moduleMetadata } from '@storybook/angular';

import { ListItemComponent } from '@/app/shared/atoms/list-item/list-item.component';
import { ListComponent } from '@/app/shared/atoms/list/list.component';
import { ConfirmBoxLargeComponent } from '@/app/shared/molecules/confirm-box-large/confirm-box-large.component';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
const meta: Meta<ConfirmBoxLargeComponent> = {
  title: 'ConfirmBoxLarge',
  component: ConfirmBoxLargeComponent,
  decorators: [
    moduleMetadata({
      imports: [ListItemComponent, ListComponent], // Add any other modules your component depends on
    }),
  ],
  args: {
    label: 'Selbstauskunft',
    subLabel: 'In den letzten fünf Jahren vor Abgabe der Erkärung nicht rechtskräftig verurteilt oder mit einem Bußgeld belegt worden, wegen',
  },
};

export default meta;
type Story = StoryObj<ConfirmBoxLargeComponent>;

export const Title: Story = {
  render: (args) => ({
    props: args,
    template: `<fish-confirm-box-large ${argsToTemplate(args)}>
				<span class="flex-1 pl-8 pt-4 pb-6 pr-8 gap-4 shadow-glass-white-tint">
          <fish-list>
              <fish-list-item>
                Fischwilderei
              </fish-list-item>
              <fish-list-item>
                Diebstahl von Fischen, Fischereigeräten
              </fish-list-item>
              <fish-list-item>
                Vorsätliche Beschädigung von Anlagen, Fahrzeugen, Geräten, Vorrichtungen, die der Fischerei oder der Fischzucht dienen, oder von Wasserbauten
              </fish-list-item>
          </fish-list>
     </span>
      <span class="flex-1 pl-8 pt-4 pb-6 pr-8 gap-4 shadow-glass-white-tint">
          <fish-list>
            <fish-list-item>
                Fälschung einer Fischereischeines oder einer sonstigen zur Ausübung der Fischerei erforderlichen Bescheinigung
              </fish-list-item>
          </fish-list>
        </span>
      <span class="flex-1 pl-8 pt-4 pb-6 pr-8 shadow-glass-white-tint">
          <fish-list>
            <fish-list-item>
                Verstoßes gegen fischerei.....
              </fish-list-item>
          </fish-list>
        </span>
		</fish-confirm-box-large>`,
  }),
};
