import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, OnInit, computed, input, signal } from '@angular/core';
import { FormControl } from '@angular/forms';

import { twMerge } from 'tailwind-merge';

import { CheckboxComponent } from '@/app/shared/atoms/checkbox/checkbox.component';
import { FocusRingComponent } from '@/app/shared/atoms/focus-ring/focus-ring.component';
import { confirmBoxLargeHeaderStyles, confirmBoxLargeStyles } from '@/app/shared/molecules/confirm-box-large/confirm-box-large.component.styles';

@Component({
  selector: 'fish-confirm-box-large',
  imports: [CheckboxComponent, CommonModule, FocusRingComponent],
  templateUrl: './confirm-box-large.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ConfirmBoxLargeComponent implements OnInit {
  // Inputs
  public readonly label = input.required<string>();

  public readonly subLabel = input.required<string>();

  public readonly formControl = input.required<FormControl>();

  // Fields
  private readonly isValid = signal<boolean>(true);

  public ngOnInit(): void {
    this.formControl().parent?.statusChanges.subscribe(() => {
      this.isValid.set(this.formControl().valid || !this.formControl().touched);
    });
    this.formControl().statusChanges.subscribe(() => {
      this.isValid.set(this.formControl().valid || !this.formControl().touched);
    });
  }

  protected classes = computed<string>(() => {
    return twMerge(confirmBoxLargeStyles({ variant: this.isValid() ? 'primary' : 'error' }));
  });

  protected headerClasses = computed<string>(() => {
    return twMerge(confirmBoxLargeHeaderStyles({ variant: this.isValid() ? 'primary' : 'error' }));
  });
}
