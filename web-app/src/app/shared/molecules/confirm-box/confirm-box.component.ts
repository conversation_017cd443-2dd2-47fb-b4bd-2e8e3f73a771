import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, OnInit, computed, input, signal } from '@angular/core';
import { FormControl } from '@angular/forms';

import { twMerge } from 'tailwind-merge';

import { CheckboxComponent } from '@/app/shared/atoms/checkbox/checkbox.component';
import { FocusRingComponent } from '@/app/shared/atoms/focus-ring/focus-ring.component';
import { confirmBoxStyles } from '@/app/shared/molecules/confirm-box/confirm-box.component.styles';

@Component({
  selector: 'fish-confirm-box',
  imports: [CheckboxComponent, CommonModule, FocusRingComponent],
  templateUrl: './confirm-box.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ConfirmBoxComponent implements OnInit {
  // Inputs
  public readonly label = input.required<string>();

  public readonly subLabel = input<string>();

  public readonly formControl = input.required<FormControl>();

  // Fields
  private readonly isValid = signal<boolean>(true);

  public ngOnInit(): void {
    this.formControl().parent?.statusChanges.subscribe(() => {
      this.isValid.set(this.formControl().valid || !this.formControl().touched);
    });
    this.formControl().statusChanges.subscribe(() => {
      this.isValid.set(this.formControl().valid || !this.formControl().touched);
    });
  }

  protected classes = computed<string>(() => {
    return twMerge(confirmBoxStyles({ variant: this.isValid() ? 'primary' : 'error' }));
  });
}
