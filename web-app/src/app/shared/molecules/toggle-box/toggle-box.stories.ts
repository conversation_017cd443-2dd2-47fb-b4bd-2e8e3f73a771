import { type Meta, type StoryObj, argsToTemplate } from '@storybook/angular';

import { SliderButtonComponent } from '@/app/shared/atoms/slider-button/slider-button.component';
import { ToggleBoxContentComponent } from '@/app/shared/atoms/toggle-box-content/toggle-box-content.component';
import { ToggleBoxTabComponent } from '@/app/shared/atoms/toggle-box-tab/toggle-box-tab.component';
import { SliderButtonGroupComponent } from '@/app/shared/molecules/slider-button-group/slider-button-group.component';
import { ToggleBoxHeaderComponent } from '@/app/shared/molecules/toggle-box-header/toggle-box-header.component';
import { ToggleBoxComponent } from '@/app/shared/molecules/toggle-box/toggle-box.component';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
const meta: Meta<ToggleBoxComponent> = {
  title: 'ToggleBox',
  component: ToggleBoxComponent,
  args: {
    direction: 'horizontal',
  },
  argTypes: {
    direction: {
      control: 'radio',
      options: ['vertical', 'horizontal'],
    },
  },
  render: (args) => ({
    props: { ...args },
    template: `<fish-toggle-box ${argsToTemplate(args)} class="block h-[400px]">
			<fish-toggle-box-header>
				<fish-slider-button-group>
					<fish-slider-button>
						Option 1
					</fish-slider-button>
					<fish-slider-button>
						Option 2
					</fish-slider-button>
					<fish-slider-button>
						Option 3
					</fish-slider-button>
				</fish-slider-button-group>
			</fish-toggle-box-header>

			<fish-toggle-box-content>
				<fish-toggle-box-tab>
					Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. 
				</fish-toggle-box-tab>
				<fish-toggle-box-tab>
					Duis autem vel eum iriure dolor in hendrerit in vulputate velit esse molestie consequat, vel illum dolore eu feugiat nulla facilisis at vero eros et accumsan et iusto odio dignissim qui blandit praesent luptatum zzril delenit augue duis dolore te feugait nulla facilisi. Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam nonummy nibh euismod tincidunt ut laoreet dolore magna aliquam erat volutpat. 
				</fish-toggle-box-tab>
				<fish-toggle-box-tab>
					Nam liber tempor cum soluta nobis eleifend option congue nihil imperdiet doming id quod mazim placerat facer possim assum. Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam nonummy nibh euismod tincidunt ut laoreet dolore magna aliquam erat volutpat. Ut wisi enim ad minim veniam, quis nostrud exerci tation ullamcorper suscipit lobortis nisl ut aliquip ex ea commodo consequat. 
				</fish-toggle-box-tab>
			</fish-toggle-box-content>
		</fish-toggle-box>`,
    moduleMetadata: {
      imports: [ToggleBoxHeaderComponent, SliderButtonGroupComponent, SliderButtonComponent, ToggleBoxContentComponent, ToggleBoxTabComponent],
    },
  }),
};

export default meta;
type Story = StoryObj<ToggleBoxComponent>;

export const Horizontal: Story = {
  args: {
    direction: 'horizontal',
  },
};

export const Vertical: Story = {
  args: {
    direction: 'vertical',
  },
};
