<ng-template #dialogContent>
  <div
    class="fixed inset-0 z-10 overflow-y-auto transition-all duration-240"
    [@openClose]="!isOpen() ? 'close' : 'open'"
    (@openClose.done)="onAnimationDone($event)"
  >
    <div class="sm:block flex min-h-screen items-center justify-center" data-test-id="dialog">
      <div class="w-[800px] overflow-hidden rounded">
        <ng-content></ng-content>
      </div>
    </div>
  </div>
</ng-template>
