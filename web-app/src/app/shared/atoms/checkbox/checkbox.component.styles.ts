import { cva } from 'class-variance-authority';

export const checkboxVariants = cva(
  [
    'absolute',
    'rounded',
    'inset-0',
    'border',
    'outline-none',
    'peer-focus-visible:outline-2',
    'cursor-pointer',
    'transition-all',
    'duration-240',
    'ease-out',
    'backdrop-blur-xl',
    // 'shadow',

    // Default
    'border-checkbox-border',
    'bg-tint-white',
    'shadow-glass-white-tint',
    'peer-checked:rounded-full',
    'peer-checked:border-action-primary',
    'overflow-hidden',

    // Hover + Focus
    'hover:bg-transparent',
    'hover:shadow-glass-blue-tint',
    'peer-focus-visible:bg-transparent',
    'peer-focus-visible:shadow-glass-blue-tint',

    // Active
    'active:bg-tint-blue',
    'active:shadow-glass-blue-tint-pressed',

    // Disabled
    'peer-disabled:cursor-auto',
    'peer-disabled:bg-tint-white',
    'peer-disabled:shadow-glass-white-tint',
    'peer-disabled:border-action-border-disabled',
  ],
  {
    variants: {
      invalid: {
        true: 'border-feedback-text-error bg-feedback-background-warning-1',
        false: 'has-[:checked]:bg-tint-blue has-[:checked]:shadow-glass-blue-tint',
      },
    },
    defaultVariants: {
      invalid: false,
    },
  }
);
