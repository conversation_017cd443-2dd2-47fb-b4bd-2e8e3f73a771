import { cva } from 'class-variance-authority';

export const linkVariants = cva(
  [
    'text-action-primary',
    'font-bold',
    'border-b-2',
    'border-action-primary',
    'hover:text-action-primary-hover',
    'hover:border-action-primary-hover',
    'focus:text-action-primary-hover',
    'focus:border-action-primary-hover',
    'active:text-action-primary-pressed',
    'active:border-action-primary-pressed',
    'outline-none',
    'inline-flex',
    'items-center',
    'gap-1',
  ],
  {
    variants: {
      type: {
        default: [],
        ghost: ['border-b-0', 'border-0', 'hover:border-0', 'focus:border-0', 'active:border-0'],
      },
      iconPosition: {
        left: ['flex-row'],
        right: ['flex-row-reverse'],
      },
    },
    defaultVariants: {
      type: 'default',
      iconPosition: 'left',
    },
  }
);
