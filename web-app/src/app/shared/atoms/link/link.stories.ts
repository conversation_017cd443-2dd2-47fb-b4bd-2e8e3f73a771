import { Meta, StoryObj, argsToTemplate } from '@storybook/angular';

import { LinkComponent } from '@/app/shared/atoms/link/link.component';

const meta: Meta<LinkComponent> = {
  title: 'Link',
  component: LinkComponent,
  render: (args) => ({
    template: `<fish-link ${argsToTemplate(args)}><fish-icon-continue-with icon />Hier klicken</fish-link>`,
  }),
  args: {
    href: 'https://www.adesso.de',
    iconPosition: 'left',
    type: 'default',
  },
  argTypes: {
    iconPosition: {
      control: 'select',
      options: ['left', 'right'],
      description: 'Position des Icons',
    },
    type: {
      control: 'select',
      options: ['default', 'ghost'],
      description: 'Typ des Links',
    },
  },
};

export default meta;

type Story = StoryObj<LinkComponent>;

export const Link: Story = {};

export const LinkWithRightIcon: Story = {
  args: {
    iconPosition: 'right',
  },
};

export const GhostLink: Story = {
  args: {
    type: 'ghost',
  },
};

export const GhostLinkWithRightIcon: Story = {
  args: {
    type: 'ghost',
    iconPosition: 'right',
  },
};
