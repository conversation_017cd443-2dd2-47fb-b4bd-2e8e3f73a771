import { ChangeDetectionStrategy, Component, signal } from '@angular/core';

import { EnvironmentService } from '@/app/core/environment/environment.service';

@Component({
  selector: 'fish-environment-ribbon',
  imports: [],
  templateUrl: './environment-ribbon.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EnvironmentRibbonComponent {
  protected readonly environment = signal<string | undefined>(undefined);

  constructor(private readonly environmentService: EnvironmentService) {
    this.environmentService.environment$.subscribe((value) => this.environment.set(value?.toUpperCase()));
  }
}
