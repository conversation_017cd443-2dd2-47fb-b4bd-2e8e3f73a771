import { NgClass } from '@angular/common';
import { ChangeDetectionStrategy, Component, EventEmitter, Output, input } from '@angular/core';

import { twMerge } from 'tailwind-merge';

import { FocusRingComponent } from '@/app/shared/atoms/focus-ring/focus-ring.component';
import { tabTitleVariants } from '@/app/shared/atoms/tab-title/tab-title.component.styles';

@Component({
  selector: 'fish-tab-title',
  templateUrl: './tab-title.component.html',
  imports: [FocusRingComponent, NgClass],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TabTitleComponent {
  public readonly disabled = input(false);

  public readonly active = input(false);

  public readonly dataTestId = input<string>();

  @Output() public readonly clicked = new EventEmitter<void>();

  protected get titleClass(): string {
    let titleState: 'active' | 'disabled' | 'DEFAULT' = 'DEFAULT';

    if (this.disabled()) {
      titleState = 'disabled';
    } else if (this.active()) {
      titleState = 'active';
    }

    return twMerge(
      tabTitleVariants({
        state: titleState,
      })
    );
  }
}
