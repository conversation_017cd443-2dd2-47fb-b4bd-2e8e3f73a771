import { cva } from 'class-variance-authority';

export const tabTitleVariants = cva(
  [
    'flex',
    'justify-center',
    'w-full',
    'border-b-2',
    'border-tabs-indicator',
    'duration-240',
    'transition-all',
    'ease-out',
    'text-base',
    'backdrop-blur-xl',
    'rounded-t-md',
    'py-2',
    'cursor-pointer',
    'hover:bg-action-secondary-hover',
    'active:bg-action-secondary-pressed',
    'outline-none',
    'font-thin',
  ],
  {
    variants: {
      state: {
        DEFAULT: ['hover:border-action-border active:border-action-border'],
        active: ['border-action-primary', 'text-action-primary-glass', 'title-shadow', 'font-bold'],
        disabled: ['cursor-auto', 'text-action-text-disabled', 'hover:bg-transparent', 'active:bg-transparent'],
      },
    },
  }
);
