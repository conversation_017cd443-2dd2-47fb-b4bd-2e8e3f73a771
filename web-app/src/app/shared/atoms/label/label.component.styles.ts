import { cva } from 'class-variance-authority';

export const labelVariants = cva(['font-bold'], {
  variants: {
    disabled: {
      false: [],
      true: ['text-action-text-disabled'],
    },
    invalid: {
      false: [],
      true: ['text-feedback-text-warning'],
    },
  },
});

export const optionalLabelVariants = cva(['px-2', 'border-l', 'font-normal', 'ml-2', 'border-font-secondary', 'text-font-secondary'], {
  variants: {
    disabled: {
      false: [],
      true: ['border-action-border-disabled', 'text-action-text-disabled'],
    },
    invalid: {
      false: [],
      true: ['border-feedback-border-warning', 'text-feedback-text-warning'],
    },
  },
});
