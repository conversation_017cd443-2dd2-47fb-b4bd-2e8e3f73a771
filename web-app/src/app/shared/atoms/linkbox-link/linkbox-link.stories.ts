import { Meta, StoryObj, argsToTemplate, moduleMetadata } from '@storybook/angular';

import { LinkboxLinkComponent } from '@/app/shared/atoms/linkbox-link/linkbox-link.component';
import { IconPrintComponent } from '@/app/shared/icons/print/print.component';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
const meta: Meta<LinkboxLinkComponent> = {
  title: 'LinkboxLink',
  component: LinkboxLinkComponent,
  render: (args) => ({
    props: { ...args },
    template: `
      <fish-linkbox-link ${argsToTemplate(args)}>
        <fish-icon-print icon size="48" />
        PDF drucken
      </fish-linkbox-link>
    `,
  }),
  args: {
    href: '/',
    disabled: false,
  },
  decorators: [
    moduleMetadata({
      imports: [IconPrintComponent],
    }),
  ],
};

export default meta;

type Story = StoryObj<LinkboxLinkComponent>;

export const Normal: Story = {};

export const Disabled: Story = {
  render: (args) => ({
    props: { ...args },
    template: `
      <fish-linkbox-link ${argsToTemplate(args)}>
        <fish-icon-print icon size="48" />
        PDF drucken
      </fish-linkbox-link>
    `,
  }),
  args: {
    disabled: true,
  },
};
