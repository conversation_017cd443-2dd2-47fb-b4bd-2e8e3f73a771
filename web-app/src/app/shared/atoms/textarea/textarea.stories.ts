import { ChangeDetectionStrategy, Component } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';

import { type Meta, type StoryObj, componentWrapperDecorator, moduleMetadata } from '@storybook/angular';

import { TextareaComponent } from '@/app/shared/atoms/textarea/textarea.component';

// In this Story, Storybook attempts to use JSON.stringify on the formControl object.
// Due to circular dependencies in formControl, this will cause a crash.
// This is a common known issue (see: https://github.com/storybookjs/storybook/discussions/15602).
// To prevent this, we override the toJSON method.
// For that there is no other option but to use 'any'
// eslint-disable-next-line @typescript-eslint/no-explicit-any
(FormControl.prototype as any).toJSON = () => null;

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
const meta: Meta<TextareaComponent> = {
  title: 'Textarea',
  component: TextareaComponent,
  args: {
    placeholder: 'Enter your text here...',
    class: 'w-[300px]',
    id: 'textarea-default',
  },
  decorators: [
    moduleMetadata({
      imports: [ReactiveFormsModule],
    }),
  ],
};

export default meta;
type Story = StoryObj<TextareaComponent>;

@Component({
  standalone: true,
  template: '<fish-textarea [control]="control" [id]="\'textarea-default\'" class="w-[300px]" [placeholder]="placeholder"/>',
  selector: 'fish-default-decorator',
  imports: [TextareaComponent],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
class DefaultDecoratorComponent {
  protected control: FormControl = new FormControl('');

  protected placeholder: string = 'Enter your text here...';
}

export const Default: Story = {
  decorators: [moduleMetadata({ imports: [DefaultDecoratorComponent] }), componentWrapperDecorator(DefaultDecoratorComponent)],
};

@Component({
  standalone: true,
  template: '<fish-textarea [control]="control" [id]="\'textarea-disabled\'" class="w-[300px]"/>',
  selector: 'fish-disabled-decorator',
  imports: [TextareaComponent],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
class DisabledDecoratorComponent {
  protected control: FormControl = new FormControl({ value: 'This textarea is disabled', disabled: true });
}

export const Disabled: Story = {
  decorators: [moduleMetadata({ imports: [DisabledDecoratorComponent] }), componentWrapperDecorator(DisabledDecoratorComponent)],
};

@Component({
  standalone: true,
  template: '<fish-textarea [control]="control" [id]="\'textarea-invalid\'" class="w-[300px]"/>',
  selector: 'fish-invalid-decorator',
  imports: [TextareaComponent],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
class InvalidDecoratorComponent {
  protected control: FormControl = new FormControl('', {
    validators: [
      () => {
        return { always: true };
      },
    ],
  });
}

export const Invalid: Story = {
  decorators: [moduleMetadata({ imports: [InvalidDecoratorComponent] }), componentWrapperDecorator(InvalidDecoratorComponent)],
};

@Component({
  standalone: true,
  template: '<fish-textarea [control]="control" [id]="\'textarea-with-content\'" class="w-[300px]"/>',
  selector: 'fish-with-content-decorator',
  imports: [TextareaComponent],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
class WithContentDecoratorComponent {
  protected control: FormControl = new FormControl('This is some example text that demonstrates how the textarea looks with content.');
}

export const WithContent: Story = {
  decorators: [moduleMetadata({ imports: [WithContentDecoratorComponent] }), componentWrapperDecorator(WithContentDecoratorComponent)],
};
