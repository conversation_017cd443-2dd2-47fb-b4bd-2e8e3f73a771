import { ChangeDetectionStrategy, Component, input } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'fish-error-badge',
  standalone: true,
  imports: [TranslateModule],
  templateUrl: './error-code-badge.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ErrorCodeBadgeComponent {
  public readonly errorCode = input.required<number>();
}
