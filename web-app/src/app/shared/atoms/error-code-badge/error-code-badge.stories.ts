import { type Meta, type StoryObj, moduleMetadata } from '@storybook/angular';

import { ErrorCodeBadgeComponent } from './error-code-badge.component';

const meta: Meta<ErrorCodeBadgeComponent> = {
  title: 'ErrorCodeBadge',
  component: ErrorCodeBadgeComponent,
  argTypes: {
    errorCode: {
      control: 'number',
    },
  },
  decorators: [
    moduleMetadata({
      imports: [],
    }),
  ],
  render: (args) => ({
    props: {
      ...args,
    },
    template: `<fish-error-badge [errorCode]="errorCode"></fish-error-badge>`,
  }),
};

export default meta;
type Story = StoryObj<ErrorCodeBadgeComponent>;

export const Default: Story = {
  args: {
    errorCode: 404,
  },
};
