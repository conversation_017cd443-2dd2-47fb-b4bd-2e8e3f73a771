import { ChangeDetectionStrategy, Component } from '@angular/core';
import { FormControl } from '@angular/forms';

import { type Meta, type StoryObj, componentWrapperDecorator, moduleMetadata } from '@storybook/angular';

import { InputComponent } from '@/app/shared/atoms/input/input.component';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
const meta: Meta<InputComponent> = {
  title: 'Input',
  component: InputComponent,
  args: {
    placeholder: 'Placeholder',
    class: 'w-[300px]',
    type: 'text',
    variant: 'default',
    size: 'medium',
  },
  argTypes: {
    variant: {
      control: 'radio',
      options: ['default', 'searchBar'],
    },
    size: {
      control: 'radio',
      options: ['medium', 'large'],
    },
  },
};

export default meta;
type Story = StoryObj<InputComponent>;

export const Input: Story = {};

@Component({
  standalone: true,
  template: '<fish-input [control]="control" class="w-[300px]"/>',
  selector: 'fish-disabled-decorator',
  imports: [InputComponent],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
class DisabledDecoratorComponent {
  protected control: FormControl = new FormControl({ value: 'Disabled', disabled: true });
}

export const Disabled: Story = {
  decorators: [moduleMetadata({ imports: [DisabledDecoratorComponent] }), componentWrapperDecorator(DisabledDecoratorComponent)],
};

@Component({
  standalone: true,
  template: '<fish-input [control]="control" class="w-[300px]"/>',
  selector: 'fish-invalid-decorator',
  imports: [InputComponent],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
class InvalidDecoratorComponent {
  protected control: FormControl = new FormControl('', {
    validators: [
      () => {
        return { always: true };
      },
    ],
  });
}

export const Invalid: Story = {
  decorators: [moduleMetadata({ imports: [InvalidDecoratorComponent] }), componentWrapperDecorator(InvalidDecoratorComponent)],
};

export const SearchBar: Story = {
  args: {
    variant: 'searchBar',
  },
};
