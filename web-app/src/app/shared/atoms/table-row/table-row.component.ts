import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, ContentChildren, HostBinding, QueryList } from '@angular/core';

import { TableCellComponent } from '@/app/shared/atoms/table-cell/table-cell.component';

/**
 * Row of a table.
 * Note that the width of the table cells has to be manually set in order to align.
 */
@Component({
  selector: 'fish-table-row',
  imports: [CommonModule],
  templateUrl: './table-row.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TableRowComponent {
  @ContentChildren(TableCellComponent, { descendants: true })
  protected cells!: QueryList<TableCellComponent>;

  @HostBinding('class')
  protected get hostClass(): string {
    return 'block w-full';
  }
}
