import { Meta, StoryObj, moduleMetadata } from '@storybook/angular';

import { ListItemComponent } from '@/app/shared/atoms/list-item/list-item.component';
import { ListComponent } from '@/app/shared/atoms/list/list.component';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
const meta: Meta<ListComponent> = {
  title: 'List',
  component: ListComponent,
  decorators: [
    moduleMetadata({
      imports: [ListItemComponent], // Add any other modules your component depends on
    }),
  ],
};

export default meta;

type Story = StoryObj<ListComponent>;

export const Title: Story = {
  render: () => ({
    template: `<fish-list>
        <fish-list-item>
          Fischwilderei
        </fish-list-item>
        <fish-list-item>
          <PERSON><PERSON><PERSON><PERSON> von <PERSON>, Fischereigeräten
        </fish-list-item>
        <fish-list-item>
          Vorsätliche Beschädigung von Anlagen, Fahrzeugen, Geräten, Vorrichtungen, die der Fischerei oder der Fischzucht dienen, o<PERSON> <PERSON>
        </fish-list-item>
    </fish-list>`,
  }),
};
