import { cva } from 'class-variance-authority';

export const badgeVariants = cva(['flex h-6 flex-col items-start justify-start self-stretch px-2 py-1 text-xs font-bold text-action-primary-text'], {
  variants: {
    type: {
      primary: 'bg-action-primary-glass shadow-glass-white-tint/16',
      secondary: 'shadow-glass-white-tint text-action-primary font-normal',
      warning: 'bg-feedback-text-fail shadow-glass-red-tint/8',
      info: 'bg-font-secondary',
    },
  },
  defaultVariants: {
    type: 'primary',
  },
});
