<div
  class="flex h-full flex-row items-center divide-x-2 divide-y-0 divide-border-divider overflow-hidden rounded-lg border-2 border-border-divider lg:flex-col lg:divide-x-0 lg:divide-y-2"
>
  <div>
    <div class="flex min-h-[150px] min-w-[250px] items-center justify-center bg-background-glass-primary lg:w-fit lg:px-7 lg:pb-7 lg:pt-2">
      <img
        [src]="imageSrc()"
        alt="license-card"
        width="400"
        height="400"
        style="
          transform: perspective(10em) rotate3D(1.5, 0, -1, 10deg) scale(0.8);
          filter: drop-shadow(0px 5px 11px rgba(0, 0, 0, 0.1)) drop-shadow(0px 19px 19px rgba(0, 0, 0, 0.09))
            drop-shadow(0px 43px 26px rgba(0, 0, 0, 0.05)) drop-shadow(0px 77px 31px rgba(0, 0, 0, 0.01)) drop-shadow(0px 120px 34px rgba(0, 0, 0, 0));
        "
      />
    </div>
  </div>
  <div class="flex grow flex-col bg-background px-8 pb-8 pt-6">
    <span class="self-stretch text-center text-base font-thin" [innerText]="title()"></span>
    <div class="flex-col gap-4 self-stretch pt-4 text-s">
      <div class="space-y-2" [innerHTML]="text()"></div>
    </div>
  </div>
</div>
