import { NavigationStart, Event as RouterEvent } from '@angular/router';

import { Subscription, lastValueFrom, throttleTime } from 'rxjs';

import { BeforeUnloadAndCanDeactivateBase } from '@/app/shared/decorators/before-unload-and-can-deactivate/before-unload-and-can-deactivate.class';

import { IBeforeUnloadAndCanDeactivate } from './before-unload-and-can-deactivate.interface';
import {
  AngularComponentConstructor,
  FishBeforeUnloadAndCanDeactivateDecoratorFn,
  FishBeforeUnloadAndCanDeactivateDecoratorParams,
} from './before-unload-and-can-deactivate.model';

// Flag to indicate if the window.confirm is already being shown
let isConfirming = false;

export const FishBeforeUnloadAndCanDeactivate = <T extends IBeforeUnloadAndCanDeactivate>(
  { active, onBeforeUnloadCallback }: FishBeforeUnloadAndCanDeactivateDecoratorParams = {
    active: true,
    onBeforeUnloadCallback: (): void => {},
  }
): FishBeforeUnloadAndCanDeactivateDecoratorFn<T> => {
  const getTranslation = async (component: IBeforeUnloadAndCanDeactivate): Promise<string> =>
    lastValueFrom(component.translateService.get('alert.confirm'));
  const eventCbFn = (component: IBeforeUnloadAndCanDeactivate): ((event: BeforeUnloadEvent) => void) => {
      component.setHasUnsavedChanges(false);

      return async (event: Event) => {
        if (component.hasUnsavedChanges()) {
          if (event instanceof BeforeUnloadEvent) {
            event.preventDefault();
            event.stopPropagation();
          } else if (event instanceof PopStateEvent) {
            if (window.confirm(await getTranslation(component))) {
              component.setHasUnsavedChanges(false);
              await component.router.navigateByUrl(document.referrer).catch(console.error);
            } else {
              window.history.pushState(null, '', window.location.href);
            }
          }
        }

        return onBeforeUnloadCallback(event);
      };
    },
    apply = (original: (...args: unknown[]) => void, that: unknown, args: unknown[]): void => {
      if (original) {
        original.apply(that, args);
      }
    };

  const handleRouterEvent = async (event: RouterEvent, self: BeforeUnloadAndCanDeactivateBase) => {
    if (!isConfirming) {
      isConfirming = true;
      if (event instanceof NavigationStart && self.hasUnsavedChanges()) {
        if (window.confirm(await getTranslation(self))) {
          self.setHasUnsavedChanges(false);
        } else {
          await self.router.navigateByUrl(self.router.url).catch(console.error);
        }
      }
      isConfirming = false;
    }
  };

  return (target: AngularComponentConstructor<T>): void => {
    let navigationSubscription: Subscription;
    const originalNgOnInit: (...args: unknown[]) => void = target.prototype.ngOnInit;
    const originalOnDestroy: (...args: unknown[]) => void = target.prototype.ngOnDestroy;

    if (!active) {
      return;
    }

    if (!target.ɵcmp) {
      throw new Error(`The ${target.name} class is not an Angular component.`);
    }

    target.prototype.ngOnInit = function (...args: unknown[]): void {
      window.addEventListener('beforeunload', eventCbFn(this));
      window.addEventListener('popstate', eventCbFn(this));

      this.setHasUnsavedChanges(false);

      navigationSubscription = this.router.events
        .pipe(throttleTime(300))
        .subscribe(async (event: RouterEvent) => await handleRouterEvent(event, this));

      apply(originalNgOnInit, this, args);
    };

    target.prototype.ngOnDestroy = function (...args: unknown[]): void {
      window.removeEventListener('beforeunload', eventCbFn(this));
      window.removeEventListener('popstate', eventCbFn(this));

      if (navigationSubscription) navigationSubscription.unsubscribe();

      apply(originalOnDestroy, this, args);
    };
  };
};
