import { formatDate } from '@angular/common';
import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';

import { Subscription } from 'rxjs';

export class DateValidators {
  public static validateFormat(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value) {
        return null;
      }

      const controlValue = control.value as string;

      if (!controlValue.match(/\d{2}.\d{2}.\d{4}$/)) {
        return {
          format: true,
        };
      }

      return null;
    };
  }

  public static partialDateExists(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const controlValue = control.value as string;

      const [day, month, year] = controlValue.split('.', 3).map((value) => Number(value));

      if (day > 31 || month > 12) {
        return {
          partialDateExists: true,
        };
      }

      // Partial date is always valid
      if (day === 0 || month === 0) {
        return null;
      }

      // Validate if the full date is valid
      if (!this.isValidDate(year, month, day)) {
        return {
          partialDateExists: true,
        };
      }

      return null;
    };
  }

  private static isValidDate(year: number, month: number, day: number) {
    const realDate = new Date(year, month - 1, day);
    const formattedDate = formatDate(realDate, 'dd.MM.yyyy', 'de');
    return `${day.toString().padStart(2, '0')}.${month.toString().padStart(2, '0')}.${year}` === formattedDate;
  }

  /**
   * Allows for comparision of any date-type value. Partial dates are also allowed (e.g. 00.00.2004)
   *
   * If a partial date is submitted in the form control, only the defined fields will be compared.
   * @param minDate The minimum date.
   */
  public static minimum(minDate: Date): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const controlDate = getDateValueAsGermanString(control);
      if (controlDate == null) {
        return null;
      }

      if (!isPartialDateBefore(controlDate, minDate)) {
        return null;
      }

      return {
        dateMinimum: {
          dateMinimum: formatDate(minDate, 'dd.MM.yyyy', 'de'),
          actual: controlDate,
        },
      };
    };
  }

  /**
   * Allows for comparision of any date-type value. Partial dates are also allowed (e.g. 00.00.2004).
   *
   * If a PARTIAL date is submitted in the form control, only the defined fields will be compared.
   * @param maxDate The maximum date.
   */
  public static maximum(maxDate: Date): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const controlDate = getDateValueAsGermanString(control);
      if (controlDate == null) {
        return null;
      }

      if (!isPartialDateAfter(controlDate, maxDate)) {
        return null;
      }

      return {
        dateMaximum: {
          dateMaximum: formatDate(maxDate, 'dd.MM.yyyy', 'de'),
          actual: controlDate,
        },
      };
    };
  }

  /**
   * Allows for comparison of any date-type value. Partial dates are also allowed (e.g. 00.00.2004)
   *
   * If a partial date is submitted in the form control, only the defined fields will be compared.
   * @param includeTodayAsPast If true, today is also considered as in the past.
   */
  public static notInPast(includeTodayAsPast: boolean = false): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const controlDate = getDateValueAsGermanString(control);
      if (controlDate == null) {
        return null;
      }

      const today = new Date();

      if (!includeTodayAsPast) {
        today.setDate(today.getDate() - 1);
      }

      if (!isPartialDateBefore(controlDate, today)) {
        return null;
      }

      return { notInPast: true };
    };
  }

  /**
   * Allows for comparision of any date-type value. Partial dates are also allowed (e.g. 00.00.2004)
   *
   * If a partial date is submitted in the form control, only the defined fields will be compared.
   * @param control
   */
  public static notInFuture(control: AbstractControl): ValidationErrors | null {
    const controlDate = getDateValueAsGermanString(control);
    if (controlDate == null) {
      return null;
    }

    const today = new Date();

    if (!isPartialDateAfter(controlDate, today)) {
      return null;
    }

    return { notInFuture: true };
  }

  /**
   * Validator expects two Validators that except only valid Date formats. Partial dates cannot be validated.
   * @param otherControl
   */
  public static notBefore(otherControl: AbstractControl): ValidatorFn {
    let subscription: Subscription;

    return (control: AbstractControl): ValidationErrors | null => {
      const controlDate = getDateValueAsGermanString(control);
      if (controlDate == null) {
        return null;
      }

      if (!otherControl) {
        return null;
      }

      if (!subscription) {
        subscription = otherControl.valueChanges.subscribe(() => control.updateValueAndValidity());
      }

      const otherDate = new Date(otherControl.value);

      if (isNaN(otherDate.getTime())) {
        return null;
      }

      if (!isPartialDateBefore(controlDate, otherDate)) {
        return null;
      }

      return { notBefore: true };
    };
  }
}

function isPartialDateAfter(partialDate: string, date: Date) {
  const [day, month, year] = partialDate.split('.').map((value) => Number(value));

  if (month === 0) {
    return year >= date.getFullYear();
  }

  if (day === 0) {
    if (year === date.getFullYear()) {
      return month >= date.getMonth() + 1;
    }
    return year > date.getFullYear();
  }

  date.setHours(0, 0, 0); // to ensure comparing ignoring any possible time input

  return new Date(year, month - 1, day) > date;
}

/**
 * Dates might be partial, i.e. year or month is 0, in which case only the remaining attributes should be compared.
 * @param partialDate
 * @param date
 */
function isPartialDateBefore(partialDate: string, date: Date) {
  const [day, month, year] = partialDate.split('.').map((value) => Number(value));

  if (month === 0) {
    return year <= date.getFullYear();
  }

  if (day === 0) {
    if (year === date.getFullYear()) {
      return month <= date.getMonth() + 1;
    }
    return year < date.getFullYear();
  }

  date.setHours(0, 0, 0); // to ensure comparing ignoring any possible time input

  return new Date(year, month - 1, day) < date;
}

function getDateValueAsGermanString(control: AbstractControl): string | null {
  if (control.value == null) {
    return null;
  }

  const controlString = control.value as string;

  if (controlString.match(/\d{2}.\d{2}.\d{4}/u)) {
    return control.value;
  }

  const controlDate = new Date(controlString);

  if (isNaN(controlDate.getTime())) {
    return null;
  }

  return formatDate(controlDate, 'dd.MM.yyyy', 'en');
}
