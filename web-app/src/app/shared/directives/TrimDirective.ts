import { Directive, ElementRef, HostListener, Renderer2, Self } from '@angular/core';
import { NgControl } from '@angular/forms';

@Directive({
  selector: '[fishTrim]',
  standalone: true,
})
export class TrimDirective {
  constructor(
    private readonly renderer: Renderer2,
    private readonly elementRef: ElementRef,
    @Self() private readonly ngControl: NgControl
  ) {}

  @HostListener('blur')
  protected onBlur(): void {
    let value = this.elementRef.nativeElement.value;

    if (value) {
      value = value.trim();
      this.renderer.setProperty(this.elementRef.nativeElement, 'value', value);
    } else {
      this.renderer.setProperty(this.elementRef.nativeElement, 'value', null);
    }

    // Safely check if ngControl.control is not null before using it
    if (this.ngControl?.control) {
      this.ngControl.control.patchValue(value);
    }
  }
}
