import { Injectable } from '@angular/core';
import { AbstractControl, FormGroup } from '@angular/forms';

@Injectable({
  providedIn: 'root',
})
export class FormToggleService {
  /**
   * Enables a specific nested FormGroup while disabling all others within the parent FormGroup
   * @param parentFormGroup The parent FormGroup containing multiple nested FormGroups
   * @param selectedControlName The name of the FormGroup to enable
   * @param excludedControls Array of control names to exclude from being disabled (optional)
   */
  public updateFormValidation<T extends { [K in keyof T]: AbstractControl<unknown, unknown> }>(
    parentFormGroup: FormGroup<T>,
    selectedControlName: keyof T,
    excludedControls: (keyof T)[] = []
  ): void {
    const selectedControl = parentFormGroup.get(String(selectedControlName));
    if (!(selectedControl instanceof FormGroup)) {
      throw new Error(`Selected control '${String(selectedControlName)}' is not a FormGroup`);
    }

    // Enable selected form
    this.enableForm(selectedControl);

    // Disable all other forms
    Object.keys(parentFormGroup.controls).forEach((controlName) => {
      const control = parentFormGroup.get(controlName);

      if (control instanceof FormGroup && controlName !== selectedControlName && !excludedControls.includes(controlName as keyof T)) {
        this.disableForm(control);
        control.updateValueAndValidity({ emitEvent: false });
      }
    });
  }

  /**
   * Enables a FormGroup and restores its validators
   * @param formGroup The FormGroup to enable
   */
  private enableForm(formGroup: FormGroup): void {
    formGroup.enable();
    formGroup.setValidators(formGroup.validator);
  }

  /**
   * Disables a FormGroup and removes its validators
   * @param formGroup The FormGroup to disable
   */
  private disableForm(formGroup: FormGroup): void {
    formGroup.disable();
    formGroup.clearValidators();
    formGroup.setErrors(null);
  }
}
