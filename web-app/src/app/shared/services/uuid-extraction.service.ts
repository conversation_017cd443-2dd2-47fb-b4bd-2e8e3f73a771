import { HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class UuidExtractionService {
  public extractRegisterEntryId(headers: HttpHeaders): string | null; // Method signature for headers
  public extractRegisterEntryId(path: string): string | null; // Method signature for path
  public extractRegisterEntryId(input: HttpHeaders | string): string | null {
    const uuidRegex: RegExp = /register-entries\/([0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12})/i; // otherwise the regex doesn't get resetted

    if (input instanceof HttpHeaders) {
      const locationHeader = input.get('Location');
      if (!locationHeader) return null;

      return uuidRegex.exec(locationHeader.toString())?.[1] ?? null;
    } else {
      const match = input.match(uuidRegex);
      return match ? match[1] : null; // Return the captured UUID
    }
  }
}
