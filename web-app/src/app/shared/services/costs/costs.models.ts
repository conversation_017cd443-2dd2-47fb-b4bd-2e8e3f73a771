import { LicenseDurationType } from '@digifischdok/ngx-register-sdk';

export interface FeeCostOption {
  cost: number;
  duration: LicenseDuration;
}

export interface FeeCostOptions {
  standard: FeeCostOption;
  vacation: FeeCostOption;
  special: FeeCostOption;
}

export interface StepperCostOption {
  optionType: 'stepper';
  durationLabel: string;
  minYears: number;
  maxYears: number;
}

export interface StaticTaxCostOption {
  optionType: 'static';
  durationLabel: string;
  years: number;
}

export type TaxCostOption = StaticTaxCostOption | StepperCostOption;

export type LicenseDuration = {
  amount?: number;
  durationType: LicenseDurationType;
};
