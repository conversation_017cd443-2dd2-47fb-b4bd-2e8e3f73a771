import { Injectable } from '@angular/core';

import { Observable, map } from 'rxjs';

import { ExportType, IdentificationDocument, IdentificationDocumentsService } from '@digifischdok/ngx-register-sdk';

@Injectable({
  providedIn: 'root',
})
export class DocumentOpenerService {
  constructor(private readonly documentsApi: IdentificationDocumentsService) {}

  public openDocument$(registerEntryId: string, document: IdentificationDocument, openInNewTab = true): Observable<void> {
    const exportType: ExportType = this.getExportType(document);

    return this.documentsApi
      .identificationDocumentsControllerExport(registerEntryId, document.documentId, exportType, 'body', false, {
        httpHeaderAccept: 'application/pdf',
      })
      .pipe(
        map((pdf) => {
          const fileUrl = window.URL.createObjectURL(pdf);
          const target = openInNewTab ? '_blank' : '_self';

          window.open(fileUrl, target);
        })
      );
  }

  private getExportType(document: IdentificationDocument): ExportType {
    if (document.tax) {
      return 'FISHING_TAXES';
    }

    if (document.fishingLicense) {
      return 'FISHING_LICENSE';
    }

    if (document.limitedLicenseApproval) {
      return 'LIMITED_LICENSE_APPROVAL';
    }

    throw new Error('Unsupported document type');
  }
}
