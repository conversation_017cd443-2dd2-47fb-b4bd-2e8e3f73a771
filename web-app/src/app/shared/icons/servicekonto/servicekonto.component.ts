import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconBaseComponent } from '@/app/shared/icons/icon-base/icon-base.component';

@Component({
  selector: 'fish-icon-servicekonto',
  imports: [],
  templateUrl: './servicekonto.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class IconServicekontoComponent  extends IconBaseComponent<'32'|'48'> {

}
