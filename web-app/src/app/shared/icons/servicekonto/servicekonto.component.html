@if (size === "32") {
  <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fill-rule="evenodd" clip-rule="evenodd"
          d="M20 12C20 14.2091 18.2091 16 16 16C13.7909 16 12 14.2091 12 12C12 9.79086 13.7909 8 16 8C18.2091 8 20 9.79086 20 12ZM22 12C22 15.3137 19.3137 18 16 18C12.6863 18 10 15.3137 10 12C10 8.68629 12.6863 6 16 6C19.3137 6 22 8.68629 22 12ZM10.1623 22L11.4903 21.5573C14.4176 20.5816 17.5824 20.5816 20.5097 21.5573L21.8377 22C23.129 22.4304 24 23.6389 24 25H26C26 22.778 24.5782 20.8053 22.4702 20.1026L21.1422 19.66C17.8043 18.5474 14.1957 18.5474 10.8578 19.66L9.52982 20.1026C7.42185 20.8053 6 22.778 6 25H8C8 23.6389 8.87099 22.4304 10.1623 22Z"
          fill="currentColor"/>
  </svg>
} @else if (size === "48") {
  <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fill-rule="evenodd" clip-rule="evenodd"
          d="M18 17C18 13.6863 20.6863 11 24 11C27.3137 11 30 13.6863 30 17C30 20.3137 27.3137 23 24 23C20.6863 23 18 20.3137 18 17ZM24 9C19.5817 9 16 12.5817 16 17C16 21.4183 19.5817 25 24 25C28.4183 25 32 21.4183 32 17C32 12.5817 28.4183 9 24 9ZM38 36.8585C38 33.2087 35.816 29.9132 32.4546 28.4911C27.0501 26.2046 20.9499 26.2046 15.5454 28.4911C12.184 29.9132 10 33.2087 10 36.8585V38H12V36.8585C12 34.0122 13.7033 31.4421 16.3246 30.3331C21.231 28.2573 26.769 28.2573 31.6754 30.3331C34.2967 31.4421 36 34.0122 36 36.8585V38H38V36.8585Z"
          fill="currentColor"/>
  </svg>

}
