@if (size === "48") {
  <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fill-rule="evenodd" clip-rule="evenodd"
          d="M12 10C12 8.89543 12.8954 8 14 8H27.3333C27.876 8 28.3954 8.22054 28.7723 8.61101L35.439 15.5177C35.7989 15.8905 36 16.3885 36 16.9067V38C36 39.1046 35.1046 40 34 40H14C12.8954 40 12 39.1046 12 38V10ZM34 16.9067L27.3333 10H27V17H34V16.9067ZM16 10H25V18C25 18.5523 25.4477 19 26 19H34V36V38H32H16H14V36V12V10H16ZM19 25H29V32H19V25ZM17 25C17 23.8954 17.8954 23 19 23H29C30.1046 23 31 23.8954 31 25V32C31 33.1046 30.1046 34 29 34H19C17.8954 34 17 33.1046 17 32V25ZM20 26H23V29H20V26ZM28 30H20V31H28V30Z"
          fill="currentColor" />
  </svg>

} @else if (size === "32") {
  <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fill-rule="evenodd" clip-rule="evenodd"
          d="M21 24V13.874C21.3196 13.9562 21.6547 14 22 14C22.3453 14 22.6804 13.9562 23 13.874V24V26H21H11H9V24V8V6H11H18L19.1716 7.17157C18.6825 7.66061 18.3195 8.2757 18.1355 8.96391L18 8.82843V10V11H18.126C18.3284 11.7862 18.7638 12.4789 19.3542 13H17H16V12V8H11V24H21ZM12 16H16V14H12V16ZM20 19H12V17H20V19ZM12 20V22H20V20H12ZM22 12C23.1046 12 24 11.1046 24 10C24 8.89543 23.1046 8 22 8C20.8954 8 20 8.89543 20 10C20 11.1046 20.8954 12 22 12Z"
          fill="currentColor" />
  </svg>

}
