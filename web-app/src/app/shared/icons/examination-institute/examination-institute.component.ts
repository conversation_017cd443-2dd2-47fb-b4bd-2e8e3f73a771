import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconBaseComponent } from '@/app/shared/icons/icon-base/icon-base.component';

@Component({
  selector: 'fish-icon-examination-institute',
  standalone: true,
  imports: [],
  templateUrl: './examination-institute.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class IconExaminationInstituteComponent extends IconBaseComponent<'32'> {

}
