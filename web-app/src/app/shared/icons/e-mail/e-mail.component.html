@if (size === "32") {
  <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fill-rule="evenodd" clip-rule="evenodd" d="M13 12.4142V13H11V10C11 9.44772 11.4477 9 12 9H25C25.5523 9 26 9.44772 26 10V22C26 22.5523 25.5523 23 25 23H12C11.4477 23 11 22.5523 11 22V20H13V21H24V12.4142L19.2071 17.2071L18.5 17.9142L17.7929 17.2071L13 12.4142ZM22.5858 11H14.4142L18.5 15.0858L22.5858 11ZM6 16H13V14H6V16ZM15 19V17H7V19H15Z" fill="currentColor"/>
  </svg>
} @else if (size === "48") {
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="48"
    height="48"
    fill="currentColor"
    viewBox="0 0 48 48"
  >
    <path
      fill-rule="evenodd"
      d="M18 14h20v20H18v-2h-2v2a2 2 0 0 0 2 2h20a2 2 0 0 0 2-2V14a2 2 0 0 0-2-2H18a2 2 0 0 0-2 2v2h2v-2Zm0 6h-2v4h2v-4Zm15.816 4.272c0 .49-.059.976-.176 1.456-.117.48-.293.917-.528 1.312-.224.395-.507.71-.848.944-.341.224-.741.336-1.2.336a1.95 1.95 0 0 1-1.008-.272 1.85 1.85 0 0 1-.672-.736h-.112a3.114 3.114 0 0 1-.816.704 2.16 2.16 0 0 1-1.152.304c-.907 0-1.6-.283-2.08-.848-.47-.576-.704-1.328-.704-2.256 0-.715.144-1.344.432-1.888a3.11 3.11 0 0 1 1.248-1.296c.544-.32 1.2-.48 1.968-.48.48 0 .965.048 1.456.144.49.085.885.181 1.184.288l-.144 3.28-.032.336v.208c0 .437.043.73.128.88.096.139.208.208.336.208.203 0 .373-.117.512-.352.139-.245.245-.565.32-.96a7.34 7.34 0 0 0 .112-1.328c0-.896-.16-1.653-.48-2.272a3.284 3.284 0 0 0-1.328-1.44c-.576-.33-1.259-.496-2.048-.496-.768 0-1.445.133-2.032.4a4.04 4.04 0 0 0-1.472 1.12 4.96 4.96 0 0 0-.896 1.696 7.124 7.124 0 0 0-.304 2.128c0 .96.155 1.776.464 2.448.32.661.784 1.163 1.392 1.504.608.341 1.36.512 2.256.512.565 0 1.179-.07 1.84-.208.661-.139 1.27-.31 1.824-.512v1.504a8.486 8.486 0 0 1-1.664.512 9.221 9.221 0 0 1-1.904.192c-1.259 0-2.341-.235-3.248-.704a4.887 4.887 0 0 1-2.064-2.016c-.47-.885-.704-1.947-.704-3.184 0-.97.15-1.872.448-2.704a6.594 6.594 0 0 1 1.312-2.176 5.782 5.782 0 0 1 2.032-1.456c.8-.352 1.696-.528 2.688-.528.832 0 1.595.133 2.288.4a4.91 4.91 0 0 1 1.792 1.136c.512.49.901 1.088 1.168 1.792.277.693.416 1.483.416 2.368Zm-7.424.976c0 .587.101 1.008.304 1.264.213.256.485.384.816.384.459 0 .779-.192.96-.576.192-.395.31-.907.352-1.536l.08-1.728a3.331 3.331 0 0 0-.32-.048 1.8 1.8 0 0 0-.352-.032c-.437 0-.795.112-1.072.336a1.881 1.881 0 0 0-.592.848 3.324 3.324 0 0 0-.176 1.088ZM9 17a1 1 0 1 0 0 2h12a1 1 0 1 0 0-2H9Zm4 8a1 1 0 1 0 0 2h5a1 1 0 1 0 0-2h-5Zm-3 5a1 1 0 0 1 1-1h9a1 1 0 1 1 0 2h-9a1 1 0 0 1-1-1Z"
      clip-rule="evenodd"
    />
  </svg>
} @else if (size === "96") {
  <svg width="96" height="96" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g filter="url(#filter0_bddddi_722_1433)">
      <g clip-path="url(#clip0_722_1433)">
        <path d="M20 20C20 18.8954 20.8954 18 22 18L88 18C89.1046 18 90 18.8954 90 20V76C90 77.1046 89.1046 78 88 78H22C20.8954 78 20 77.1046 20 76V20Z" fill="white" fill-opacity="0.24"/>
        <g filter="url(#filter1_bddddi_722_1433)">
          <rect width="28" height="60" transform="matrix(-1 0 0 1 46 18)" fill="#163BBF" fill-opacity="0.8"/>
        </g>
        <g filter="url(#filter2_bddddi_722_1433)">
          <path d="M67.1705 63.7045C64.7538 63.7045 62.6023 63.375 60.7159 62.7159C58.8295 62.0644 57.2348 61.1061 55.9318 59.8409C54.6288 58.5833 53.6364 57.0455 52.9545 55.2273C52.2727 53.4091 51.9318 51.3409 51.9318 49.0227C51.9318 46.7348 52.2727 44.6591 52.9545 42.7955C53.6439 40.9318 54.6477 39.3333 55.9659 38C57.2841 36.6591 58.8939 35.6288 60.7955 34.9091C62.7045 34.1894 64.8788 33.8295 67.3182 33.8295C69.5758 33.8295 71.5909 34.1591 73.3636 34.8182C75.1439 35.4773 76.6515 36.4167 77.8864 37.6364C79.1288 38.8485 80.072 40.2917 80.7159 41.9659C81.3674 43.6326 81.6894 45.4773 81.6818 47.5C81.6894 48.8939 81.5682 50.1742 81.3182 51.3409C81.0682 52.5076 80.6742 53.5265 80.1364 54.3977C79.6061 55.2614 78.9091 55.947 78.0455 56.4545C77.1818 56.9545 76.1364 57.2386 74.9091 57.3068C74.0303 57.375 73.3182 57.3106 72.7727 57.1136C72.2273 56.9167 71.8106 56.625 71.5227 56.2386C71.2424 55.8447 71.0606 55.3864 70.9773 54.8636H70.8409C70.6591 55.3182 70.303 55.7311 69.7727 56.1023C69.2424 56.4659 68.5947 56.75 67.8295 56.9545C67.072 57.1515 66.2652 57.2235 65.4091 57.1705C64.5152 57.1174 63.6667 56.9091 62.8636 56.5455C62.0682 56.1818 61.3598 55.6553 60.7386 54.9659C60.125 54.2765 59.6402 53.4242 59.2841 52.4091C58.9356 51.3939 58.7576 50.2197 58.75 48.8864C58.7576 47.5682 58.9432 46.4205 59.3068 45.4432C59.678 44.4659 60.1667 43.6439 60.7727 42.9773C61.3864 42.3106 62.0682 41.7879 62.8182 41.4091C63.5682 41.0303 64.322 40.7879 65.0795 40.6818C65.9356 40.553 66.7462 40.553 67.5114 40.6818C68.2765 40.8106 68.9205 41.0189 69.4432 41.3068C69.9735 41.5947 70.303 41.9053 70.4318 42.2386H70.5909V40.9659H74.1477V52.0568C74.1553 52.5795 74.2727 52.9848 74.5 53.2727C74.7273 53.5606 75.0341 53.7045 75.4205 53.7045C75.9432 53.7045 76.3788 53.4735 76.7273 53.0114C77.0833 52.5492 77.3485 51.8409 77.5227 50.8864C77.7045 49.9318 77.7955 48.7159 77.7955 47.2386C77.7955 45.8144 77.6061 44.5644 77.2273 43.4886C76.8561 42.4053 76.3333 41.4848 75.6591 40.7273C74.9924 39.9621 74.2159 39.3409 73.3295 38.8636C72.4432 38.3864 71.4811 38.0379 70.4432 37.8182C69.4129 37.5985 68.3523 37.4886 67.2614 37.4886C65.375 37.4886 63.7273 37.7765 62.3182 38.3523C60.9091 38.9205 59.7348 39.7197 58.7955 40.75C57.8561 41.7803 57.1515 42.9886 56.6818 44.375C56.2197 45.7538 55.9848 47.2576 55.9773 48.8864C55.9848 50.6818 56.2348 52.2727 56.7273 53.6591C57.2273 55.0379 57.9621 56.197 58.9318 57.1364C59.9015 58.0758 61.0985 58.7879 62.5227 59.2727C63.947 59.7576 65.5833 60 67.4318 60C68.303 60 69.1553 59.9356 69.9886 59.8068C70.822 59.6856 71.5758 59.5341 72.25 59.3523C72.9242 59.178 73.4659 59.0076 73.875 58.8409L75 62.1364C74.5227 62.4091 73.875 62.6629 73.0568 62.8977C72.2462 63.1402 71.3295 63.3333 70.3068 63.4773C69.2917 63.6288 68.2462 63.7045 67.1705 63.7045ZM66.6477 53.4318C67.572 53.4318 68.3068 53.2538 68.8523 52.8977C69.4053 52.5417 69.7992 52.0152 70.0341 51.3182C70.2765 50.6136 70.3902 49.7462 70.375 48.7159C70.3674 47.8068 70.25 47.0379 70.0227 46.4091C69.803 45.7727 69.4205 45.2917 68.875 44.9659C68.3371 44.6326 67.5871 44.4659 66.625 44.4659C65.7841 44.4659 65.0682 44.6439 64.4773 45C63.8939 45.3561 63.447 45.8561 63.1364 46.5C62.8333 47.1364 62.678 47.8864 62.6705 48.75C62.678 49.553 62.8068 50.3144 63.0568 51.0341C63.3068 51.7462 63.7197 52.3258 64.2955 52.7727C64.8712 53.2121 65.6553 53.4318 66.6477 53.4318Z" fill="#163BBF" fill-opacity="0.8"/>
        </g>
      </g>
    </g>
    <g filter="url(#filter3_bddddi_722_1433)">
      <rect x="10" y="62" width="15" height="4" rx="2" fill="white" fill-opacity="0.24"/>
    </g>
    <g filter="url(#filter4_bddddi_722_1433)">
      <rect x="14" y="56" width="19" height="4" rx="2" fill="white" fill-opacity="0.24"/>
    </g>
    <g filter="url(#filter5_bddddi_722_1433)">
      <rect x="10" y="46" width="12" height="4" rx="2" fill="white" fill-opacity="0.24"/>
    </g>
    <g filter="url(#filter6_bddddi_722_1433)">
      <rect x="10" y="39" width="28" height="4" rx="2" fill="white" fill-opacity="0.24"/>
    </g>
    <g filter="url(#filter7_bddddi_722_1433)">
      <rect x="25" y="46" width="23" height="4" rx="2" fill="white" fill-opacity="0.24"/>
    </g>
    <defs>
      <filter id="filter0_bddddi_722_1433" x="-4" y="-6" width="118" height="118" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
        <feGaussianBlur in="BackgroundImageFix" stdDeviation="12"/>
        <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_722_1433"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
        <feOffset dy="2"/>
        <feGaussianBlur stdDeviation="1.5"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.08 0"/>
        <feBlend mode="normal" in2="effect1_backgroundBlur_722_1433" result="effect2_dropShadow_722_1433"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
        <feOffset dy="6"/>
        <feGaussianBlur stdDeviation="3"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.07 0"/>
        <feBlend mode="normal" in2="effect2_dropShadow_722_1433" result="effect3_dropShadow_722_1433"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
        <feOffset dy="14"/>
        <feGaussianBlur stdDeviation="4"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.04 0"/>
        <feBlend mode="normal" in2="effect3_dropShadow_722_1433" result="effect4_dropShadow_722_1433"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
        <feOffset dy="24"/>
        <feGaussianBlur stdDeviation="5"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.01 0"/>
        <feBlend mode="normal" in2="effect4_dropShadow_722_1433" result="effect5_dropShadow_722_1433"/>
        <feBlend mode="normal" in="SourceGraphic" in2="effect5_dropShadow_722_1433" result="shape"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
        <feOffset dy="2"/>
        <feGaussianBlur stdDeviation="2"/>
        <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.12 0"/>
        <feBlend mode="normal" in2="shape" result="effect6_innerShadow_722_1433"/>
      </filter>
      <filter id="filter1_bddddi_722_1433" x="-6" y="-6" width="76" height="119" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
        <feGaussianBlur in="BackgroundImageFix" stdDeviation="12"/>
        <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_722_1433"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
        <feOffset dy="2"/>
        <feGaussianBlur stdDeviation="1.5"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0.803922 0 0 0 0 0.823529 0 0 0 0 0.854902 0 0 0 0.16 0"/>
        <feBlend mode="normal" in2="effect1_backgroundBlur_722_1433" result="effect2_dropShadow_722_1433"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
        <feOffset dy="6"/>
        <feGaussianBlur stdDeviation="3"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0.803922 0 0 0 0 0.823529 0 0 0 0 0.854902 0 0 0 0.14 0"/>
        <feBlend mode="normal" in2="effect2_dropShadow_722_1433" result="effect3_dropShadow_722_1433"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
        <feOffset dy="14"/>
        <feGaussianBlur stdDeviation="4"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0.803922 0 0 0 0 0.823529 0 0 0 0 0.854902 0 0 0 0.08 0"/>
        <feBlend mode="normal" in2="effect3_dropShadow_722_1433" result="effect4_dropShadow_722_1433"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
        <feOffset dy="25"/>
        <feGaussianBlur stdDeviation="5"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0.803922 0 0 0 0 0.823529 0 0 0 0 0.854902 0 0 0 0.02 0"/>
        <feBlend mode="normal" in2="effect4_dropShadow_722_1433" result="effect5_dropShadow_722_1433"/>
        <feBlend mode="normal" in="SourceGraphic" in2="effect5_dropShadow_722_1433" result="shape"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
        <feOffset dy="2"/>
        <feGaussianBlur stdDeviation="2"/>
        <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0.321569 0 0 0 0 0.376471 0 0 0 0 0.443137 0 0 0 0.12 0"/>
        <feBlend mode="normal" in2="shape" result="effect6_innerShadow_722_1433"/>
      </filter>
      <filter id="filter2_bddddi_722_1433" x="27.9316" y="9.82959" width="77.7617" height="87.875" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
        <feGaussianBlur in="BackgroundImageFix" stdDeviation="12"/>
        <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_722_1433"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
        <feOffset dy="2"/>
        <feGaussianBlur stdDeviation="1.5"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.08 0"/>
        <feBlend mode="normal" in2="effect1_backgroundBlur_722_1433" result="effect2_dropShadow_722_1433"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
        <feOffset dy="6"/>
        <feGaussianBlur stdDeviation="3"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.07 0"/>
        <feBlend mode="normal" in2="effect2_dropShadow_722_1433" result="effect3_dropShadow_722_1433"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
        <feOffset dy="14"/>
        <feGaussianBlur stdDeviation="4"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.04 0"/>
        <feBlend mode="normal" in2="effect3_dropShadow_722_1433" result="effect4_dropShadow_722_1433"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
        <feOffset dy="24"/>
        <feGaussianBlur stdDeviation="5"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.01 0"/>
        <feBlend mode="normal" in2="effect4_dropShadow_722_1433" result="effect5_dropShadow_722_1433"/>
        <feBlend mode="normal" in="SourceGraphic" in2="effect5_dropShadow_722_1433" result="shape"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
        <feOffset dy="2"/>
        <feGaussianBlur stdDeviation="2"/>
        <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.12 0"/>
        <feBlend mode="normal" in2="shape" result="effect6_innerShadow_722_1433"/>
      </filter>
      <filter id="filter3_bddddi_722_1433" x="-14" y="38" width="63" height="62" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
        <feGaussianBlur in="BackgroundImageFix" stdDeviation="12"/>
        <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_722_1433"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
        <feOffset dy="2"/>
        <feGaussianBlur stdDeviation="1.5"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.08 0"/>
        <feBlend mode="normal" in2="effect1_backgroundBlur_722_1433" result="effect2_dropShadow_722_1433"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
        <feOffset dy="6"/>
        <feGaussianBlur stdDeviation="3"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.07 0"/>
        <feBlend mode="normal" in2="effect2_dropShadow_722_1433" result="effect3_dropShadow_722_1433"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
        <feOffset dy="14"/>
        <feGaussianBlur stdDeviation="4"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.04 0"/>
        <feBlend mode="normal" in2="effect3_dropShadow_722_1433" result="effect4_dropShadow_722_1433"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
        <feOffset dy="24"/>
        <feGaussianBlur stdDeviation="5"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.01 0"/>
        <feBlend mode="normal" in2="effect4_dropShadow_722_1433" result="effect5_dropShadow_722_1433"/>
        <feBlend mode="normal" in="SourceGraphic" in2="effect5_dropShadow_722_1433" result="shape"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
        <feOffset dy="2"/>
        <feGaussianBlur stdDeviation="2"/>
        <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.12 0"/>
        <feBlend mode="normal" in2="shape" result="effect6_innerShadow_722_1433"/>
      </filter>
      <filter id="filter4_bddddi_722_1433" x="-10" y="32" width="67" height="62" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
        <feGaussianBlur in="BackgroundImageFix" stdDeviation="12"/>
        <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_722_1433"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
        <feOffset dy="2"/>
        <feGaussianBlur stdDeviation="1.5"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.08 0"/>
        <feBlend mode="normal" in2="effect1_backgroundBlur_722_1433" result="effect2_dropShadow_722_1433"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
        <feOffset dy="6"/>
        <feGaussianBlur stdDeviation="3"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.07 0"/>
        <feBlend mode="normal" in2="effect2_dropShadow_722_1433" result="effect3_dropShadow_722_1433"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
        <feOffset dy="14"/>
        <feGaussianBlur stdDeviation="4"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.04 0"/>
        <feBlend mode="normal" in2="effect3_dropShadow_722_1433" result="effect4_dropShadow_722_1433"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
        <feOffset dy="24"/>
        <feGaussianBlur stdDeviation="5"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.01 0"/>
        <feBlend mode="normal" in2="effect4_dropShadow_722_1433" result="effect5_dropShadow_722_1433"/>
        <feBlend mode="normal" in="SourceGraphic" in2="effect5_dropShadow_722_1433" result="shape"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
        <feOffset dy="2"/>
        <feGaussianBlur stdDeviation="2"/>
        <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.12 0"/>
        <feBlend mode="normal" in2="shape" result="effect6_innerShadow_722_1433"/>
      </filter>
      <filter id="filter5_bddddi_722_1433" x="-14" y="22" width="60" height="62" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
        <feGaussianBlur in="BackgroundImageFix" stdDeviation="12"/>
        <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_722_1433"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
        <feOffset dy="2"/>
        <feGaussianBlur stdDeviation="1.5"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.08 0"/>
        <feBlend mode="normal" in2="effect1_backgroundBlur_722_1433" result="effect2_dropShadow_722_1433"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
        <feOffset dy="6"/>
        <feGaussianBlur stdDeviation="3"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.07 0"/>
        <feBlend mode="normal" in2="effect2_dropShadow_722_1433" result="effect3_dropShadow_722_1433"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
        <feOffset dy="14"/>
        <feGaussianBlur stdDeviation="4"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.04 0"/>
        <feBlend mode="normal" in2="effect3_dropShadow_722_1433" result="effect4_dropShadow_722_1433"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
        <feOffset dy="24"/>
        <feGaussianBlur stdDeviation="5"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.01 0"/>
        <feBlend mode="normal" in2="effect4_dropShadow_722_1433" result="effect5_dropShadow_722_1433"/>
        <feBlend mode="normal" in="SourceGraphic" in2="effect5_dropShadow_722_1433" result="shape"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
        <feOffset dy="2"/>
        <feGaussianBlur stdDeviation="2"/>
        <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.12 0"/>
        <feBlend mode="normal" in2="shape" result="effect6_innerShadow_722_1433"/>
      </filter>
      <filter id="filter6_bddddi_722_1433" x="-14" y="15" width="76" height="62" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
        <feGaussianBlur in="BackgroundImageFix" stdDeviation="12"/>
        <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_722_1433"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
        <feOffset dy="2"/>
        <feGaussianBlur stdDeviation="1.5"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.08 0"/>
        <feBlend mode="normal" in2="effect1_backgroundBlur_722_1433" result="effect2_dropShadow_722_1433"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
        <feOffset dy="6"/>
        <feGaussianBlur stdDeviation="3"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.07 0"/>
        <feBlend mode="normal" in2="effect2_dropShadow_722_1433" result="effect3_dropShadow_722_1433"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
        <feOffset dy="14"/>
        <feGaussianBlur stdDeviation="4"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.04 0"/>
        <feBlend mode="normal" in2="effect3_dropShadow_722_1433" result="effect4_dropShadow_722_1433"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
        <feOffset dy="24"/>
        <feGaussianBlur stdDeviation="5"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.01 0"/>
        <feBlend mode="normal" in2="effect4_dropShadow_722_1433" result="effect5_dropShadow_722_1433"/>
        <feBlend mode="normal" in="SourceGraphic" in2="effect5_dropShadow_722_1433" result="shape"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
        <feOffset dy="2"/>
        <feGaussianBlur stdDeviation="2"/>
        <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.12 0"/>
        <feBlend mode="normal" in2="shape" result="effect6_innerShadow_722_1433"/>
      </filter>
      <filter id="filter7_bddddi_722_1433" x="1" y="22" width="71" height="62" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
        <feGaussianBlur in="BackgroundImageFix" stdDeviation="12"/>
        <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_722_1433"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
        <feOffset dy="2"/>
        <feGaussianBlur stdDeviation="1.5"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.08 0"/>
        <feBlend mode="normal" in2="effect1_backgroundBlur_722_1433" result="effect2_dropShadow_722_1433"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
        <feOffset dy="6"/>
        <feGaussianBlur stdDeviation="3"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.07 0"/>
        <feBlend mode="normal" in2="effect2_dropShadow_722_1433" result="effect3_dropShadow_722_1433"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
        <feOffset dy="14"/>
        <feGaussianBlur stdDeviation="4"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.04 0"/>
        <feBlend mode="normal" in2="effect3_dropShadow_722_1433" result="effect4_dropShadow_722_1433"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
        <feOffset dy="24"/>
        <feGaussianBlur stdDeviation="5"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.01 0"/>
        <feBlend mode="normal" in2="effect4_dropShadow_722_1433" result="effect5_dropShadow_722_1433"/>
        <feBlend mode="normal" in="SourceGraphic" in2="effect5_dropShadow_722_1433" result="shape"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
        <feOffset dy="2"/>
        <feGaussianBlur stdDeviation="2"/>
        <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.12 0"/>
        <feBlend mode="normal" in2="shape" result="effect6_innerShadow_722_1433"/>
      </filter>
      <clipPath id="clip0_722_1433">
        <path d="M20 20C20 18.8954 20.8954 18 22 18L88 18C89.1046 18 90 18.8954 90 20V76C90 77.1046 89.1046 78 88 78H22C20.8954 78 20 77.1046 20 76V20Z" fill="white"/>
      </clipPath>
    </defs>
  </svg>
}
