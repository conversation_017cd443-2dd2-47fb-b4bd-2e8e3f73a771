import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconBaseComponent } from '@/app/shared/icons/icon-base/icon-base.component';

@Component({
  selector: 'fish-icon-statistic',
  standalone: true,
  imports: [],
  templateUrl: './statistic.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class IconStatisticComponent extends IconBaseComponent<'48'|'32'>{

}
