@if (size === "32") {
  <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g id="icon-right">
      <path id="Union" fill-rule="evenodd" clip-rule="evenodd"
            d="M12 7C11.4477 7 11 7.44772 11 8V11C11 11.5523 11.4477 12 12 12C12.5523 12 13 11.5523 13 11V8C13 7.44772 12.5523 7 12 7ZM9 9H10V11H9V13H23V11H22V9H23C24.1046 9 25 9.89543 25 11V13V15V23C25 24.1046 24.1046 25 23 25H9C7.89543 25 7 24.1046 7 23V15V13V11C7 10.931 7.0035 10.8627 7.01033 10.7955C7.11275 9.787 7.96447 9 9 9ZM14 9V11H18V9H14ZM23 15V23H9L9 15H23ZM19 8C19 7.44772 19.4477 7 20 7C20.5523 7 21 7.44772 21 8V11C21 11.5523 20.5523 12 20 12C19.4477 12 19 11.5523 19 11V8ZM20 22C21.1046 22 22 21.1046 22 20C22 18.8954 21.1046 18 20 18C18.8954 18 18 18.8954 18 20C18 21.1046 18.8954 22 20 22Z"
            fill="currentColor"/>
    </g>
  </svg>

} @else if (size === "48") {
  <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" fill="currentColor" viewBox="0 0 48 48">
    <path fill-rule="evenodd"
          d="M15 10a1 1 0 0 0-1 1v4a1 1 0 1 0 2 0v-4a1 1 0 0 0-1-1Zm9 0a1 1 0 0 0-1 1v4a1 1 0 1 0 2 0v-4a1 1 0 0 0-1-1Zm8 1a1 1 0 1 1 2 0v4a1 1 0 1 1-2 0v-4Zm-20 1h1v2h-1v4h24v-4h-1v-2h1a2 2 0 0 1 2 2v22a2 2 0 0 1-2 2H12a2 2 0 0 1-2-2V14a2 2 0 0 1 2-2Zm10 2h-5v-2h5v2Zm4 0v-2h5v2h-5Zm10 6v16H12V20h24Zm-6 13a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z"
          clip-rule="evenodd"/>
  </svg>
}
