import { formatNumber } from '@angular/common';
import { Pipe, PipeTransform, inject } from '@angular/core';

import { TranslateService } from '@ngx-translate/core';

import { AmountType } from '@/app/shared/models/amount-type';

@Pipe({
  name: 'amount',
  standalone: true,
})
export class AmountPipe implements PipeTransform {
  private readonly translate = inject(TranslateService);

  public transform(value: number | undefined, type: AmountType = 'unit'): string {
    if (value == null) return '';

    const formattedValue = formatNumber(value, 'de-DE', '1.0-2');

    if (type === 'money') {
      return `${formattedValue} ${this.translate.instant('common.amount.euro')}`;
    } else if (type === 'unit') {
      return `${formattedValue} ${this.translate.instant('common.amount.unit')}`;
    }
    return '';
  }
}
