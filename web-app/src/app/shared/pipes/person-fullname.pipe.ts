import { Pipe, PipeTransform } from '@angular/core';

import { Person } from '@digifischdok/ngx-register-sdk';

import { PersonFormatterService } from '@/app/shared/services/person-formatter.service';

@Pipe({
  name: 'personFullname',
  standalone: true,
})
export class PersonFullnamePipe implements PipeTransform {
  constructor(private readonly personFormatterService: PersonFormatterService) {}

  public transform(person: Person | undefined): string {
    if (!person) {
      return '';
    }
    return this.personFormatterService.formatName(person);
  }
}
