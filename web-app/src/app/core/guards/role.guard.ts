import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';

import { KeycloakService } from 'keycloak-angular';

import { UserRole } from '@/app/core/services/user/user.constants';

export const roleGuard: CanActivateFn = async (route) => {
  const keycloakService = inject(KeycloakService);
  const router = inject(Router);
  const requiredRoles: UserRole[] | undefined = route.data['roles'];
  if (!requiredRoles || requiredRoles.length <= 0) {
    console.error(`Role guard was used without specifying any role, making ${route.url} inaccessible.`);
    return false;
  }
  const hasPermission = requiredRoles.some((role) => keycloakService.isUserInRole(role));

  if (hasPermission) {
    return true;
  }

  await router.navigate(['/not-found']);
  return false;
};
