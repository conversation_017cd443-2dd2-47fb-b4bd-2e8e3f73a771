import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';

import { RouterService } from '@/app/core/services/router.service';

export const validCallbackRouteGuard: CanActivateFn = (state) => {
  const router = inject(Router);
  const routerService = inject(RouterService);

  return routerService.isValidRoute(state.url.join('/')) || router.navigate(['/not-found']);
};
