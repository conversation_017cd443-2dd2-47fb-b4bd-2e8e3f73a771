import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';

import { ConsentStore } from '@/app/core/stores/consent.store';

export const hasGivenConsentGuard: CanActivateFn = (_route, state) => {
  const router = inject(Router);
  const consentStore = inject(ConsentStore);
  return consentStore.hasGivenConsent() ? true : router.navigate(['consent/' + state.url]);
};
