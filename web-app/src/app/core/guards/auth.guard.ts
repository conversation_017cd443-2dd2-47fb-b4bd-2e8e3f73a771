import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router, RouterStateSnapshot, UrlTree } from '@angular/router';

import { KeycloakAuthGuard, KeycloakService } from 'keycloak-angular';

@Injectable({
  providedIn: 'root',
})
export class AuthGuard extends KeycloakAuthGuard {
  private static readonly INTENDED_URL_KEY = 'intended_url_after_login';

  constructor(
    protected override readonly router: Router,
    protected readonly keycloak: KeycloakService
  ) {
    super(router, keycloak);
  }

  public override async isAccessAllowed(_route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Promise<boolean | UrlTree> {
    if (state.url.toLowerCase().trim() === '/login') {
      if (this.authenticated) {
        // Check if there's an intended URL to redirect to
        const intendedUrl = sessionStorage.getItem(AuthGuard.INTENDED_URL_KEY);
        if (intendedUrl) {
          sessionStorage.removeItem(AuthGuard.INTENDED_URL_KEY);
          return !(await this.router.navigateByUrl(intendedUrl));
        }
        return !(await this.router.navigate(['/']));
      }

      return true;
    }

    if (state.url.toLowerCase().trim() === '/keycloak/login') {
      if (!this.authenticated) {
        this.keycloak?.login({
          redirectUri: window.location.href,
        });

        return false;
      } else {
        // Check if there's an intended URL to redirect to
        const intendedUrl = sessionStorage.getItem(AuthGuard.INTENDED_URL_KEY);
        if (intendedUrl) {
          sessionStorage.removeItem(AuthGuard.INTENDED_URL_KEY);
          return !(await this.router.navigateByUrl(intendedUrl));
        }
        return !(await this.router.navigate(['/']));
      }
    }

    if (!this.keycloak.isLoggedIn()) {
      // Store the intended URL before redirecting to login
      if (state.url !== '/' && state.url !== '/login' && state.url !== '/keycloak/login') {
        sessionStorage.setItem(AuthGuard.INTENDED_URL_KEY, state.url);
      }
      return !(await this.router.navigate(['/login']));
    }

    return true;
  }
}
