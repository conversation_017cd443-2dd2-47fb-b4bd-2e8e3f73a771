import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';

import { ConsentStore } from '@/app/core/stores/consent.store';

export const hasGivenTaxConsentGuard: CanActivateFn = (_route, state) => {
  const router = inject(Router);
  const consentStore = inject(ConsentStore);
  return consentStore.hasGivenTaxConsent() ? true : router.navigate(['tax-consent/' + state.url]);
};
