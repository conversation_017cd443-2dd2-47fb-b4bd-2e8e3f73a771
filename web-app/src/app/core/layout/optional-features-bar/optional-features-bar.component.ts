import { ChangeDetectionStrategy, Component, OnInit, computed, inject } from '@angular/core';
import { Router } from '@angular/router';

import { TranslateModule } from '@ngx-translate/core';
import { KeycloakService } from 'keycloak-angular';

import { PostboxService } from '@/app/core/services/postbox.service';
import { UserRole } from '@/app/core/services/user/user.constants';
import { HeaderToggleButtonComponent } from '@/app/shared/atoms/header-toggle-button/header-toggle-button.component';
import { IconPostboxComponent } from '@/app/shared/icons/postbox/postbox.component';
import { IconStatisticComponent } from '@/app/shared/icons/statistic/statistic.component';

@Component({
  selector: 'fish-optional-features-bar',
  imports: [HeaderToggleButtonComponent, IconPostboxComponent, TranslateModule, IconStatisticComponent],
  templateUrl: './optional-features-bar.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class OptionalFeaturesBarComponent implements OnInit {
  // Constants
  private readonly URL_LIMITED_LICENSE_APPLICATIONS = 'postbox/limited-license-applications';

  private readonly URL_REPORTINGS = 'reportings';

  // Dependencies
  private readonly keycloakService = inject(KeycloakService);

  private readonly postboxService = inject(PostboxService);

  private readonly router = inject(Router);

  // Fields
  protected readonly hasLicenseApplicationsFeature = computed(() => this.keycloakService.isUserInRole(UserRole.LimitedLicenseCreator));

  protected readonly applicationsCount = computed(() => {
    if (this.hasLicenseApplicationsFeature()) {
      return this.postboxService.pendingApplicationsCount();
    } else {
      return null;
    }
  });

  protected readonly isOnReportingsPage = computed(() => {
    return this.router.url.includes(this.URL_REPORTINGS);
  });

  protected reportingsButtonRoute = computed(() => {
    if (this.isOnReportingsPage()) {
      return ['/']; // Return to home if user is on reportings-page
    } else {
      return ['/' + this.URL_REPORTINGS];
    }
  });

  protected readonly isOnApplicationsPage = computed(() => {
    return this.router.url.includes(this.URL_LIMITED_LICENSE_APPLICATIONS);
  });

  protected applicationsButtonRoute = computed(() => {
    if (this.isOnApplicationsPage()) {
      return ['/']; // Return to home if user is on applications-page
    } else {
      return ['/' + this.URL_LIMITED_LICENSE_APPLICATIONS];
    }
  });

  public ngOnInit(): void {
    // Fetch on initialize if user has permission
    if (this.keycloakService.isUserInRole(UserRole.LimitedLicenseCreator)) {
      this.postboxService.getNewLicenseApplications$().subscribe();
    }
  }
}
