import { ChangeDetectionStrategy, Component, Signal, computed, inject } from '@angular/core';
import { ActivatedRoute, RouterOutlet } from '@angular/router';

import { TranslateModule } from '@ngx-translate/core';

import { BackgroundGradientComponent } from '@/app/core/layout/background-gradient/background-gradient.component';
import { BackgroundGraphicComponent } from '@/app/core/layout/background-graphic/background-graphic.component';
import { HeaderComponent } from '@/app/core/layout/header/header.component';
import { ProfileBarComponent } from '@/app/core/layout/profile-bar/profile-bar.component';
import { ProfileHeaderComponent } from '@/app/core/layout/profile-header/profile-header.component';
import { CitizenStore } from '@/app/core/stores/citizen.store';

@Component({
  selector: 'fish-with-citizen-info-layout',
  imports: [
    TranslateModule,
    RouterOutlet,
    BackgroundGradientComponent,
    BackgroundGraphicComponent,
    HeaderComponent,
    ProfileHeaderComponent,
    ProfileBarComponent,
  ],
  templateUrl: './with-citizen-info-layout.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class WithCitizenInfoLayoutComponent {
  // Dependencies
  private readonly route: ActivatedRoute = inject(ActivatedRoute);

  private readonly citizenStore: CitizenStore = inject(CitizenStore);

  // Fields
  protected readonly isProfileBarShown: Signal<boolean> = computed(() => {
    // if there is a registerEntryId in the url path, a profile should be present
    return !!this.route.snapshot.paramMap.get('registerEntryId') || this.citizenStore.hasProfile();
  });
}
