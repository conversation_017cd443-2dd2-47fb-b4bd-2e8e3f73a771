import { computed } from '@angular/core';

import { patchState, signalStore, withComputed, withMethods, withState } from '@ngrx/signals';

export interface SearchHistoryState {
  lastSearchQuery: string | null;
  cameFromSearch: boolean;
}

const initialState: SearchHistoryState = {
  lastSearchQuery: null,
  cameFromSearch: false,
};

export const SearchHistoryStore = signalStore(
  { providedIn: 'root' },
  withState(initialState),
  withComputed((store) => ({
    isFromSearchPage: computed(() => store.cameFromSearch() && !!store.lastSearchQuery()),
  })),
  withMethods((store) => ({
    setLastSearchQuery(query: string) {
      patchState(store, { lastSearchQuery: query });
    },
    setFromSearchPage(value: boolean) {
      patchState(store, { cameFromSearch: value });
    },
  }))
);

export type SearchHistoryStore = InstanceType<typeof SearchHistoryStore>; // so can it be via constructor injected
