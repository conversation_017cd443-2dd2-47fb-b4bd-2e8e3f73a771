import { Injectable } from '@angular/core';
import { Data, Route, Router, Routes } from '@angular/router';

@Injectable({
  providedIn: 'root',
})
export class RouterService {
  constructor(private readonly router: Router) {}

  /**
   * Checks if the given URL corresponds to a valid route in the router configuration.
   *
   * @param {string} url - The URL to validate.
   * @returns {boolean} - Returns `true` if the route is valid, otherwise `false`.
   */
  public isValidRoute(url: string): boolean {
    return this.findMatchingRoute(url, this.router.config) !== null;
  }

  /**
   * Retrieves the data associated with a route that matches the provided URL.
   *
   * @param {string} url - The URL for which to retrieve the route data.
   * @returns {Data | undefined} - Returns the `data` object if a matching route is found and has data, otherwise `undefined`.
   */
  public getRouteData(url: string): Data | undefined {
    const matchedRoute = this.findMatchingRoute(url, this.router.config);
    return matchedRoute && matchedRoute.data ? matchedRoute.data : undefined;
  }

  // Function to find a matching route based on the provided URL
  private findMatchingRoute(path: string, routes: Routes): Route | null {
    const allRoutes = this.getAllRoutes(routes);
    return allRoutes.find((route) => this.matchPath(route.path ?? '', path)) || null;
  }

  // Function to get all possible routes from the router configuration
  private getAllRoutes(routes: Routes, parentPath: string = ''): Route[] {
    let allRoutes: Route[] = [];

    for (const route of routes) {
      const fullPath = `${parentPath}${route.path}`.replace(/\/+/g, '/');
      const clonedRoute = { ...route, path: fullPath };
      allRoutes.push(clonedRoute);

      if (route.children) {
        allRoutes = allRoutes.concat(this.getAllRoutes(route.children, `${fullPath}/`));
      }
    }

    return allRoutes;
  }

  // Function to match a given route pattern to the URL
  private matchPath(pattern: string, path: string): boolean {
    const patternSegments = pattern.split('/');
    const pathSegments = path.split('/');

    // Check if the number of segments match
    if (patternSegments.length !== pathSegments.length) {
      return false;
    }

    // Use reduce to check if each segment matches
    return patternSegments.reduce((isMatch, segment, index) => {
      if (!isMatch) return false;

      return segment === pathSegments[index] || segment.startsWith(':');
    }, true);
  }
}
