<svg width="257" height="257" viewBox="0 0 257 257" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g clip-path="url(#clip0_1352_22712)">
        <mask id="mask0_1352_22712" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="257" height="257">
            <rect x="0.5" y="0.5" width="256" height="256" fill="url(#paint0_radial_1352_22712)"/>
        </mask>
        <g mask="url(#mask0_1352_22712)">
            <g opacity="0.72">
                <g opacity="0.72">
                    <path d="M-12.5 24L5.34314 6.15686C8.46734 3.03266 13.5327 3.03266 16.6569 6.15685L28.8431 18.3431C31.9673 21.4673 37.0327 21.4673 40.1569 18.3431L52.3431 6.15686C55.4673 3.03266 60.5327 3.03266 63.6569 6.15685L75.8431 18.3431C78.9673 21.4673 84.0327 21.4673 87.1569 18.3431L99.3431 6.15686C102.467 3.03266 107.533 3.03266 110.657 6.15685L122.843 18.3431C125.967 21.4673 131.033 21.4673 134.157 18.3431L146.343 6.15686C149.467 3.03266 154.533 3.03266 157.657 6.15685L169.843 18.3431C172.967 21.4673 178.033 21.4673 181.157 18.3431L193.343 6.15686C196.467 3.03266 201.533 3.03266 204.657 6.15685L216.843 18.3431C219.967 21.4673 225.033 21.4673 228.157 18.3431L240.343 6.15686C243.467 3.03266 248.533 3.03266 251.657 6.15685L269.5 24" stroke="#F1F3FB" stroke-width="2"/>
                    <path opacity="0.64" d="M-12.5 43.5L5.34314 25.6569C8.46734 22.5327 13.5327 22.5327 16.6569 25.6569L28.8431 37.8431C31.9673 40.9673 37.0327 40.9673 40.1569 37.8431L52.3431 25.6569C55.4673 22.5327 60.5327 22.5327 63.6569 25.6569L75.8431 37.8431C78.9673 40.9673 84.0327 40.9673 87.1569 37.8431L99.3431 25.6569C102.467 22.5327 107.533 22.5327 110.657 25.6569L122.843 37.8431C125.967 40.9673 131.033 40.9673 134.157 37.8431L146.343 25.6569C149.467 22.5327 154.533 22.5327 157.657 25.6569L169.843 37.8431C172.967 40.9673 178.033 40.9673 181.157 37.8431L193.343 25.6569C196.467 22.5327 201.533 22.5327 204.657 25.6569L216.843 37.8431C219.967 40.9673 225.033 40.9673 228.157 37.8431L240.343 25.6569C243.467 22.5327 248.533 22.5327 251.657 25.6569L269.5 43.5" stroke="#F1F3FB" stroke-width="2"/>
                    <path d="M-12.5 63L5.34314 45.1569C8.46734 42.0327 13.5327 42.0327 16.6569 45.1569L28.8431 57.3431C31.9673 60.4673 37.0327 60.4673 40.1569 57.3431L52.3431 45.1569C55.4673 42.0327 60.5327 42.0327 63.6569 45.1569L75.8431 57.3431C78.9673 60.4673 84.0327 60.4673 87.1569 57.3431L99.3431 45.1569C102.467 42.0327 107.533 42.0327 110.657 45.1569L122.843 57.3431C125.967 60.4673 131.033 60.4673 134.157 57.3431L146.343 45.1569C149.467 42.0327 154.533 42.0327 157.657 45.1569L169.843 57.3431C172.967 60.4673 178.033 60.4673 181.157 57.3431L193.343 45.1569C196.467 42.0327 201.533 42.0327 204.657 45.1569L216.843 57.3431C219.967 60.4673 225.033 60.4673 228.157 57.3431L240.343 45.1569C243.467 42.0327 248.533 42.0327 251.657 45.1569L269.5 63" stroke="#F1F3FB" stroke-width="2"/>
                    <path opacity="0.64" d="M-12.5 82.5L5.34314 64.6569C8.46734 61.5327 13.5327 61.5327 16.6569 64.6569L28.8431 76.8431C31.9673 79.9673 37.0327 79.9673 40.1569 76.8431L52.3431 64.6569C55.4673 61.5327 60.5327 61.5327 63.6569 64.6569L75.8431 76.8431C78.9673 79.9673 84.0327 79.9673 87.1569 76.8431L99.3431 64.6569C102.467 61.5327 107.533 61.5327 110.657 64.6569L122.843 76.8431C125.967 79.9673 131.033 79.9673 134.157 76.8431L146.343 64.6569C149.467 61.5327 154.533 61.5327 157.657 64.6569L169.843 76.8431C172.967 79.9673 178.033 79.9673 181.157 76.8431L193.343 64.6569C196.467 61.5327 201.533 61.5327 204.657 64.6569L216.843 76.8431C219.967 79.9673 225.033 79.9673 228.157 76.8431L240.343 64.6569C243.467 61.5327 248.533 61.5327 251.657 64.6569L269.5 82.5" stroke="#F1F3FB" stroke-width="2"/>
                    <path d="M-12.5 102L5.34314 84.1569C8.46734 81.0327 13.5327 81.0327 16.6569 84.1569L28.8431 96.3431C31.9673 99.4673 37.0327 99.4673 40.1569 96.3431L52.3431 84.1569C55.4673 81.0327 60.5327 81.0327 63.6569 84.1569L75.8431 96.3431C78.9673 99.4673 84.0327 99.4673 87.1569 96.3431L99.3431 84.1569C102.467 81.0327 107.533 81.0327 110.657 84.1569L122.843 96.3431C125.967 99.4673 131.033 99.4673 134.157 96.3431L146.343 84.1569C149.467 81.0327 154.533 81.0327 157.657 84.1569L169.843 96.3431C172.967 99.4673 178.033 99.4673 181.157 96.3431L193.343 84.1569C196.467 81.0327 201.533 81.0327 204.657 84.1569L216.843 96.3431C219.967 99.4673 225.033 99.4673 228.157 96.3431L240.343 84.1569C243.467 81.0327 248.533 81.0327 251.657 84.1569L269.5 102" stroke="#F1F3FB" stroke-width="2"/>
                    <path opacity="0.64" d="M-12.5 121.5L5.34314 103.657C8.46734 100.533 13.5327 100.533 16.6569 103.657L28.8431 115.843C31.9673 118.967 37.0327 118.967 40.1569 115.843L52.3431 103.657C55.4673 100.533 60.5327 100.533 63.6569 103.657L75.8431 115.843C78.9673 118.967 84.0327 118.967 87.1569 115.843L99.3431 103.657C102.467 100.533 107.533 100.533 110.657 103.657L122.843 115.843C125.967 118.967 131.033 118.967 134.157 115.843L146.343 103.657C149.467 100.533 154.533 100.533 157.657 103.657L169.843 115.843C172.967 118.967 178.033 118.967 181.157 115.843L193.343 103.657C196.467 100.533 201.533 100.533 204.657 103.657L216.843 115.843C219.967 118.967 225.033 118.967 228.157 115.843L240.343 103.657C243.467 100.533 248.533 100.533 251.657 103.657L269.5 121.5" stroke="#F1F3FB" stroke-width="2"/>
                    <path d="M-12.5 141L5.34314 123.157C8.46734 120.033 13.5327 120.033 16.6569 123.157L28.8431 135.343C31.9673 138.467 37.0327 138.467 40.1569 135.343L52.3431 123.157C55.4673 120.033 60.5327 120.033 63.6569 123.157L75.8431 135.343C78.9673 138.467 84.0327 138.467 87.1569 135.343L99.3431 123.157C102.467 120.033 107.533 120.033 110.657 123.157L122.843 135.343C125.967 138.467 131.033 138.467 134.157 135.343L146.343 123.157C149.467 120.033 154.533 120.033 157.657 123.157L169.843 135.343C172.967 138.467 178.033 138.467 181.157 135.343L193.343 123.157C196.467 120.033 201.533 120.033 204.657 123.157L216.843 135.343C219.967 138.467 225.033 138.467 228.157 135.343L240.343 123.157C243.467 120.033 248.533 120.033 251.657 123.157L269.5 141" stroke="#F1F3FB" stroke-width="2"/>
                    <path opacity="0.64" d="M-12.5 160.5L5.34314 142.657C8.46734 139.533 13.5327 139.533 16.6569 142.657L28.8431 154.843C31.9673 157.967 37.0327 157.967 40.1569 154.843L52.3431 142.657C55.4673 139.533 60.5327 139.533 63.6569 142.657L75.8431 154.843C78.9673 157.967 84.0327 157.967 87.1569 154.843L99.3431 142.657C102.467 139.533 107.533 139.533 110.657 142.657L122.843 154.843C125.967 157.967 131.033 157.967 134.157 154.843L146.343 142.657C149.467 139.533 154.533 139.533 157.657 142.657L169.843 154.843C172.967 157.967 178.033 157.967 181.157 154.843L193.343 142.657C196.467 139.533 201.533 139.533 204.657 142.657L216.843 154.843C219.967 157.967 225.033 157.967 228.157 154.843L240.343 142.657C243.467 139.533 248.533 139.533 251.657 142.657L269.5 160.5" stroke="#F1F3FB" stroke-width="2"/>
                    <path d="M-12.5 180L5.34314 162.157C8.46734 159.033 13.5327 159.033 16.6569 162.157L28.8431 174.343C31.9673 177.467 37.0327 177.467 40.1569 174.343L52.3431 162.157C55.4673 159.033 60.5327 159.033 63.6569 162.157L75.8431 174.343C78.9673 177.467 84.0327 177.467 87.1569 174.343L99.3431 162.157C102.467 159.033 107.533 159.033 110.657 162.157L122.843 174.343C125.967 177.467 131.033 177.467 134.157 174.343L146.343 162.157C149.467 159.033 154.533 159.033 157.657 162.157L169.843 174.343C172.967 177.467 178.033 177.467 181.157 174.343L193.343 162.157C196.467 159.033 201.533 159.033 204.657 162.157L216.843 174.343C219.967 177.467 225.033 177.467 228.157 174.343L240.343 162.157C243.467 159.033 248.533 159.033 251.657 162.157L269.5 180" stroke="#F1F3FB" stroke-width="2"/>
                    <path opacity="0.64" d="M-12.5 199.5L5.34314 181.657C8.46734 178.533 13.5327 178.533 16.6569 181.657L28.8431 193.843C31.9673 196.967 37.0327 196.967 40.1569 193.843L52.3431 181.657C55.4673 178.533 60.5327 178.533 63.6569 181.657L75.8431 193.843C78.9673 196.967 84.0327 196.967 87.1569 193.843L99.3431 181.657C102.467 178.533 107.533 178.533 110.657 181.657L122.843 193.843C125.967 196.967 131.033 196.967 134.157 193.843L146.343 181.657C149.467 178.533 154.533 178.533 157.657 181.657L169.843 193.843C172.967 196.967 178.033 196.967 181.157 193.843L193.343 181.657C196.467 178.533 201.533 178.533 204.657 181.657L216.843 193.843C219.967 196.967 225.033 196.967 228.157 193.843L240.343 181.657C243.467 178.533 248.533 178.533 251.657 181.657L269.5 199.5" stroke="#F1F3FB" stroke-width="2"/>
                    <path d="M-12.5 219L5.34314 201.157C8.46734 198.033 13.5327 198.033 16.6569 201.157L28.8431 213.343C31.9673 216.467 37.0327 216.467 40.1569 213.343L52.3431 201.157C55.4673 198.033 60.5327 198.033 63.6569 201.157L75.8431 213.343C78.9673 216.467 84.0327 216.467 87.1569 213.343L99.3431 201.157C102.467 198.033 107.533 198.033 110.657 201.157L122.843 213.343C125.967 216.467 131.033 216.467 134.157 213.343L146.343 201.157C149.467 198.033 154.533 198.033 157.657 201.157L169.843 213.343C172.967 216.467 178.033 216.467 181.157 213.343L193.343 201.157C196.467 198.033 201.533 198.033 204.657 201.157L216.843 213.343C219.967 216.467 225.033 216.467 228.157 213.343L240.343 201.157C243.467 198.033 248.533 198.033 251.657 201.157L269.5 219" stroke="#F1F3FB" stroke-width="2"/>
                    <path opacity="0.64" d="M-12.5 238.5L5.34314 220.657C8.46734 217.533 13.5327 217.533 16.6569 220.657L28.8431 232.843C31.9673 235.967 37.0327 235.967 40.1569 232.843L52.3431 220.657C55.4673 217.533 60.5327 217.533 63.6569 220.657L75.8431 232.843C78.9673 235.967 84.0327 235.967 87.1569 232.843L99.3431 220.657C102.467 217.533 107.533 217.533 110.657 220.657L122.843 232.843C125.967 235.967 131.033 235.967 134.157 232.843L146.343 220.657C149.467 217.533 154.533 217.533 157.657 220.657L169.843 232.843C172.967 235.967 178.033 235.967 181.157 232.843L193.343 220.657C196.467 217.533 201.533 217.533 204.657 220.657L216.843 232.843C219.967 235.967 225.033 235.967 228.157 232.843L240.343 220.657C243.467 217.533 248.533 217.533 251.657 220.657L269.5 238.5" stroke="#F1F3FB" stroke-width="2"/>
                    <path d="M-12.5 258L5.34314 240.157C8.46734 237.033 13.5327 237.033 16.6569 240.157L28.8431 252.343C31.9673 255.467 37.0327 255.467 40.1569 252.343L52.3431 240.157C55.4673 237.033 60.5327 237.033 63.6569 240.157L75.8431 252.343C78.9673 255.467 84.0327 255.467 87.1569 252.343L99.3431 240.157C102.467 237.033 107.533 237.033 110.657 240.157L122.843 252.343C125.967 255.467 131.033 255.467 134.157 252.343L146.343 240.157C149.467 237.033 154.533 237.033 157.657 240.157L169.843 252.343C172.967 255.467 178.033 255.467 181.157 252.343L193.343 240.157C196.467 237.033 201.533 237.033 204.657 240.157L216.843 252.343C219.967 255.467 225.033 255.467 228.157 252.343L240.343 240.157C243.467 237.033 248.533 237.033 251.657 240.157L269.5 258" stroke="#F1F3FB" stroke-width="2"/>
                    <path opacity="0.64" d="M-12.5 277.5L5.34314 259.657C8.46734 256.533 13.5327 256.533 16.6569 259.657L28.8431 271.843C31.9673 274.967 37.0327 274.967 40.1569 271.843L52.3431 259.657C55.4673 256.533 60.5327 256.533 63.6569 259.657L75.8431 271.843C78.9673 274.967 84.0327 274.967 87.1569 271.843L99.3431 259.657C102.467 256.533 107.533 256.533 110.657 259.657L122.843 271.843C125.967 274.967 131.033 274.967 134.157 271.843L146.343 259.657C149.467 256.533 154.533 256.533 157.657 259.657L169.843 271.843C172.967 274.967 178.033 274.967 181.157 271.843L193.343 259.657C196.467 256.533 201.533 256.533 204.657 259.657L216.843 271.843C219.967 274.967 225.033 274.967 228.157 271.843L240.343 259.657C243.467 256.533 248.533 256.533 251.657 259.657L269.5 277.5" stroke="#F1F3FB" stroke-width="2"/>
                </g>
            </g>
            <g clip-path="url(#clip1_1352_22712)">
                <g filter="url(#filter0_bddddi_1352_22712)">
                    <path d="M144.326 96.4599L96.7427 49.6769L64.6311 81.2496L56.0563 72.8191L47.8855 80.8515L56.4602 89.2831L49.1606 96.4599L56.4753 103.652L47.8844 112.098L56.0553 120.132L64.6462 111.689L96.7427 143.243L144.326 96.4599Z" fill="white" fill-opacity="0.24"/>
                </g>
                <path d="M129.045 96.4557L89.0898 135.739L57.4466 104.623L41.6724 120.132L33.5016 112.098L49.2747 96.5891L49.1433 96.46L49.2585 96.3467L33.5005 80.8536L41.6714 72.8202L57.4273 88.3132L89.0898 57.1831L129.045 96.4557Z" fill="#163BBF"/>
                <g opacity="0.24">
                    <path d="M75.7569 107.25L74.369 105.886L75.0633 105.203L83.1972 97.2093L83.9606 96.4591L83.1958 95.7102L75.0453 87.7294L74.3518 87.0503L75.7407 85.6854L76.4378 86.3682L86.7374 96.4569L76.4521 106.566L75.7569 107.25ZM69.5295 101.208L64.7347 96.4652L69.5323 91.7378L69.5572 91.7622L69.5588 91.7607L70.2561 92.4435L74.363 96.4652L70.2688 100.49L69.574 101.173L69.5695 101.169L69.5295 101.208Z" fill="white"/>
                    <path d="M69.5332 93.2361L66.2546 96.4667L69.5277 99.7045L69.5652 99.6672L69.5739 99.6758L72.8384 96.4667L69.5601 93.2565L69.557 93.2595L69.5332 93.2361ZM81.9771 78.0593L100.698 96.4589L81.9771 114.86L69.574 102.67L69.5314 102.712L63.2148 96.4638L69.5314 90.2395L69.5573 90.265L81.9771 78.0593ZM81.9771 111.998L97.7871 96.4589L81.9771 80.9202L77.1978 85.6181L88.2619 96.4556L77.2135 107.315L81.9771 111.998ZM74.3019 104.454L82.4358 96.4605L74.2853 88.4796L71.0161 91.6933L75.8876 96.4638L71.0302 101.239L74.3019 104.454Z" fill="white"/>
                    <path d="M63.5942 96.4603L81.9561 114.513L100.318 96.4603L81.9561 78.4072L63.5942 96.4603Z" stroke="white"/>
                </g>
            </g>
            <path fill-rule="evenodd" clip-rule="evenodd" d="M198.5 135.5C198.5 132.186 201.186 129.5 204.5 129.5C207.814 129.5 210.5 132.186 210.5 135.5C210.5 138.814 207.814 141.5 204.5 141.5C201.186 141.5 198.5 138.814 198.5 135.5ZM204.5 125.5C198.977 125.5 194.5 129.977 194.5 135.5C194.5 140.338 197.936 144.373 202.5 145.3V213C202.5 216.314 199.814 219 196.5 219H171C167.686 219 165 216.314 165 213V184.328L172.086 191.414L174.914 188.586L164.414 178.086L161 174.672V179.5V213C161 218.523 165.477 223 171 223H196.5C202.023 223 206.5 218.523 206.5 213V145.3C211.064 144.373 214.5 140.338 214.5 135.5C214.5 129.977 210.023 125.5 204.5 125.5Z" fill="#272D33"/>
            <g filter="url(#filter1_bddddi_1352_22712)">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M199.281 123.462C198.796 124.528 198.5 125.945 198.5 127.5C198.5 130.814 199.843 133.5 201.5 133.5C202.232 133.5 202.904 132.975 203.425 132.103C203.758 132.359 204.121 132.5 204.5 132.5C204.879 132.5 205.242 132.359 205.575 132.103C206.096 132.975 206.768 133.5 207.5 133.5C209.157 133.5 210.5 130.814 210.5 127.5C210.5 125.945 210.204 124.528 209.719 123.462C210.216 122.588 210.5 121.577 210.5 120.5C210.5 119.407 210.208 118.383 209.697 117.5C210.208 116.617 210.5 115.593 210.5 114.5C210.5 113.194 210.083 111.985 209.374 111C210.083 110.015 210.5 108.806 210.5 107.5C210.5 106.472 210.241 105.504 209.785 104.658C210.241 104.164 210.5 103.6 210.5 103C210.5 101.929 209.676 100.971 208.378 100.329L214.543 96.3396L213.457 94.6604L205.842 99.5878C205.729 99.5728 205.615 99.5597 205.5 99.5484V0.5H203.5V99.5484C200.662 99.8261 198.5 101.266 198.5 103C198.5 103.6 198.759 104.164 199.215 104.658C198.759 105.504 198.5 106.472 198.5 107.5C198.5 108.806 198.917 110.015 199.626 111C198.917 111.985 198.5 113.194 198.5 114.5C198.5 115.593 198.792 116.617 199.303 117.5C198.792 118.383 198.5 119.407 198.5 120.5C198.5 121.577 198.784 122.588 199.281 123.462Z" fill="white" fill-opacity="0.24"/>
            </g>
            <g filter="url(#filter2_bddddi_1352_22712)">
                <circle cx="185.5" cy="169.5" r="8" fill="white" fill-opacity="0.24"/>
            </g>
            <circle cx="184.5" cy="166.5" r="2" fill="#272D33"/>
            <g filter="url(#filter3_bddddi_1352_22712)">
                <path d="M175.5 176.5C175.5 173.739 177.739 171.5 180.5 171.5H210.5C213.261 171.5 215.5 173.739 215.5 176.5C215.5 179.261 213.261 181.5 210.5 181.5H180.5C177.739 181.5 175.5 179.261 175.5 176.5Z" fill="#AD285F" fill-opacity="0.8"/>
            </g>
            <path d="M201.5 171.5V181.5" stroke="#E17C9C" stroke-width="2"/>
            <path d="M207.5 171.5L207.5 181.5" stroke="#E17C9C" stroke-width="2"/>
            <g filter="url(#filter4_bddddi_1352_22712)">
                <circle cx="192.5" cy="170.5" r="8" fill="white" fill-opacity="0.72"/>
            </g>
            <circle cx="188.5" cy="167.5" r="2" fill="#272D33"/>
            <g filter="url(#filter5_bddddi_1352_22712)">
                <rect x="188.5" y="181.5" width="27" height="11" rx="5.5" fill="#AD285F" fill-opacity="0.8"/>
            </g>
            <g filter="url(#filter6_bddddi_1352_22712)">
                <rect x="191.5" y="192.5" width="23" height="11" rx="5.5" fill="#AD285F" fill-opacity="0.8"/>
            </g>
            <g filter="url(#filter7_bddddi_1352_22712)">
                <rect x="197.5" y="203.5" width="23" height="11" rx="5.5" fill="#AD285F" fill-opacity="0.8"/>
            </g>
            <path d="M197.5 181.5V192.5" stroke="#E17C9C" stroke-width="2"/>
            <path d="M207.5 192.5L207.5 203.5" stroke="#D65D87" stroke-width="2"/>
            <path d="M214.5 203.5L214.5 214.5" stroke="#D65D87" stroke-width="2"/>
            <path d="M210.5 203.5L210.5 214.5" stroke="#E17C9C" stroke-width="2"/>
            <path d="M201.5 192.5L201.5 203.5" stroke="#E17C9C" stroke-width="2"/>
            <path d="M203.5 203.5L203.5 214.5" stroke="#E17C9C" stroke-width="2"/>
            <path d="M203.5 181.5L203.5 192.5" stroke="#D65D87" stroke-width="2"/>
            <path d="M209.5 181.5V192.5" stroke="#E17C9C" stroke-width="2"/>
        </g>
    </g>
    <defs>
        <filter id="filter0_bddddi_1352_22712" x="23.8844" y="25.6769" width="144.442" height="151.566" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feGaussianBlur in="BackgroundImageFix" stdDeviation="12"/>
            <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1352_22712"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="2"/>
            <feGaussianBlur stdDeviation="1.5"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.08 0"/>
            <feBlend mode="normal" in2="effect1_backgroundBlur_1352_22712" result="effect2_dropShadow_1352_22712"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="6"/>
            <feGaussianBlur stdDeviation="3"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.07 0"/>
            <feBlend mode="normal" in2="effect2_dropShadow_1352_22712" result="effect3_dropShadow_1352_22712"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="14"/>
            <feGaussianBlur stdDeviation="4"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.04 0"/>
            <feBlend mode="normal" in2="effect3_dropShadow_1352_22712" result="effect4_dropShadow_1352_22712"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="24"/>
            <feGaussianBlur stdDeviation="5"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.01 0"/>
            <feBlend mode="normal" in2="effect4_dropShadow_1352_22712" result="effect5_dropShadow_1352_22712"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect5_dropShadow_1352_22712" result="shape"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="2"/>
            <feGaussianBlur stdDeviation="2"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.12 0"/>
            <feBlend mode="normal" in2="shape" result="effect6_innerShadow_1352_22712"/>
        </filter>
        <filter id="filter1_bddddi_1352_22712" x="174.5" y="-23.5" width="64.0432" height="192" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feGaussianBlur in="BackgroundImageFix" stdDeviation="12"/>
            <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1352_22712"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="2"/>
            <feGaussianBlur stdDeviation="1.5"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.803922 0 0 0 0 0.823529 0 0 0 0 0.854902 0 0 0 0.16 0"/>
            <feBlend mode="normal" in2="effect1_backgroundBlur_1352_22712" result="effect2_dropShadow_1352_22712"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="6"/>
            <feGaussianBlur stdDeviation="3"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.803922 0 0 0 0 0.823529 0 0 0 0 0.854902 0 0 0 0.14 0"/>
            <feBlend mode="normal" in2="effect2_dropShadow_1352_22712" result="effect3_dropShadow_1352_22712"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="14"/>
            <feGaussianBlur stdDeviation="4"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.803922 0 0 0 0 0.823529 0 0 0 0 0.854902 0 0 0 0.08 0"/>
            <feBlend mode="normal" in2="effect3_dropShadow_1352_22712" result="effect4_dropShadow_1352_22712"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="25"/>
            <feGaussianBlur stdDeviation="5"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.803922 0 0 0 0 0.823529 0 0 0 0 0.854902 0 0 0 0.02 0"/>
            <feBlend mode="normal" in2="effect4_dropShadow_1352_22712" result="effect5_dropShadow_1352_22712"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect5_dropShadow_1352_22712" result="shape"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="2"/>
            <feGaussianBlur stdDeviation="2"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.321569 0 0 0 0 0.376471 0 0 0 0 0.443137 0 0 0 0.12 0"/>
            <feBlend mode="normal" in2="shape" result="effect6_innerShadow_1352_22712"/>
        </filter>
        <filter id="filter2_bddddi_1352_22712" x="153.5" y="137.5" width="64" height="75" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feGaussianBlur in="BackgroundImageFix" stdDeviation="12"/>
            <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1352_22712"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="2"/>
            <feGaussianBlur stdDeviation="1.5"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.803922 0 0 0 0 0.823529 0 0 0 0 0.854902 0 0 0 0.16 0"/>
            <feBlend mode="normal" in2="effect1_backgroundBlur_1352_22712" result="effect2_dropShadow_1352_22712"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="6"/>
            <feGaussianBlur stdDeviation="3"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.803922 0 0 0 0 0.823529 0 0 0 0 0.854902 0 0 0 0.14 0"/>
            <feBlend mode="normal" in2="effect2_dropShadow_1352_22712" result="effect3_dropShadow_1352_22712"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="14"/>
            <feGaussianBlur stdDeviation="4"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.803922 0 0 0 0 0.823529 0 0 0 0 0.854902 0 0 0 0.08 0"/>
            <feBlend mode="normal" in2="effect3_dropShadow_1352_22712" result="effect4_dropShadow_1352_22712"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="25"/>
            <feGaussianBlur stdDeviation="5"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.803922 0 0 0 0 0.823529 0 0 0 0 0.854902 0 0 0 0.02 0"/>
            <feBlend mode="normal" in2="effect4_dropShadow_1352_22712" result="effect5_dropShadow_1352_22712"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect5_dropShadow_1352_22712" result="shape"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="2"/>
            <feGaussianBlur stdDeviation="2"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.321569 0 0 0 0 0.376471 0 0 0 0 0.443137 0 0 0 0.12 0"/>
            <feBlend mode="normal" in2="shape" result="effect6_innerShadow_1352_22712"/>
        </filter>
        <filter id="filter3_bddddi_1352_22712" x="151.5" y="147.5" width="88" height="68" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feGaussianBlur in="BackgroundImageFix" stdDeviation="12"/>
            <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1352_22712"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="2"/>
            <feGaussianBlur stdDeviation="1.5"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.678431 0 0 0 0 0.156863 0 0 0 0 0.372549 0 0 0 0.08 0"/>
            <feBlend mode="normal" in2="effect1_backgroundBlur_1352_22712" result="effect2_dropShadow_1352_22712"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="6"/>
            <feGaussianBlur stdDeviation="3"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.678431 0 0 0 0 0.156863 0 0 0 0 0.372549 0 0 0 0.07 0"/>
            <feBlend mode="normal" in2="effect2_dropShadow_1352_22712" result="effect3_dropShadow_1352_22712"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="14"/>
            <feGaussianBlur stdDeviation="4"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.678431 0 0 0 0 0.156863 0 0 0 0 0.372549 0 0 0 0.04 0"/>
            <feBlend mode="normal" in2="effect3_dropShadow_1352_22712" result="effect4_dropShadow_1352_22712"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="24"/>
            <feGaussianBlur stdDeviation="5"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.678431 0 0 0 0 0.156863 0 0 0 0 0.372549 0 0 0 0.01 0"/>
            <feBlend mode="normal" in2="effect4_dropShadow_1352_22712" result="effect5_dropShadow_1352_22712"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect5_dropShadow_1352_22712" result="shape"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="2"/>
            <feGaussianBlur stdDeviation="2"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.533333 0 0 0 0 0.145098 0 0 0 0 0.298039 0 0 0 0.12 0"/>
            <feBlend mode="normal" in2="shape" result="effect6_innerShadow_1352_22712"/>
        </filter>
        <filter id="filter4_bddddi_1352_22712" x="160.5" y="138.5" width="64" height="75" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feGaussianBlur in="BackgroundImageFix" stdDeviation="12"/>
            <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1352_22712"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="2"/>
            <feGaussianBlur stdDeviation="1.5"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.803922 0 0 0 0 0.823529 0 0 0 0 0.854902 0 0 0 0.16 0"/>
            <feBlend mode="normal" in2="effect1_backgroundBlur_1352_22712" result="effect2_dropShadow_1352_22712"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="6"/>
            <feGaussianBlur stdDeviation="3"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.803922 0 0 0 0 0.823529 0 0 0 0 0.854902 0 0 0 0.14 0"/>
            <feBlend mode="normal" in2="effect2_dropShadow_1352_22712" result="effect3_dropShadow_1352_22712"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="14"/>
            <feGaussianBlur stdDeviation="4"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.803922 0 0 0 0 0.823529 0 0 0 0 0.854902 0 0 0 0.08 0"/>
            <feBlend mode="normal" in2="effect3_dropShadow_1352_22712" result="effect4_dropShadow_1352_22712"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="25"/>
            <feGaussianBlur stdDeviation="5"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.803922 0 0 0 0 0.823529 0 0 0 0 0.854902 0 0 0 0.02 0"/>
            <feBlend mode="normal" in2="effect4_dropShadow_1352_22712" result="effect5_dropShadow_1352_22712"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect5_dropShadow_1352_22712" result="shape"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="2"/>
            <feGaussianBlur stdDeviation="2"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.321569 0 0 0 0 0.376471 0 0 0 0 0.443137 0 0 0 0.12 0"/>
            <feBlend mode="normal" in2="shape" result="effect6_innerShadow_1352_22712"/>
        </filter>
        <filter id="filter5_bddddi_1352_22712" x="164.5" y="157.5" width="75" height="69" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feGaussianBlur in="BackgroundImageFix" stdDeviation="12"/>
            <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1352_22712"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="2"/>
            <feGaussianBlur stdDeviation="1.5"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.678431 0 0 0 0 0.156863 0 0 0 0 0.372549 0 0 0 0.08 0"/>
            <feBlend mode="normal" in2="effect1_backgroundBlur_1352_22712" result="effect2_dropShadow_1352_22712"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="6"/>
            <feGaussianBlur stdDeviation="3"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.678431 0 0 0 0 0.156863 0 0 0 0 0.372549 0 0 0 0.07 0"/>
            <feBlend mode="normal" in2="effect2_dropShadow_1352_22712" result="effect3_dropShadow_1352_22712"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="14"/>
            <feGaussianBlur stdDeviation="4"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.678431 0 0 0 0 0.156863 0 0 0 0 0.372549 0 0 0 0.04 0"/>
            <feBlend mode="normal" in2="effect3_dropShadow_1352_22712" result="effect4_dropShadow_1352_22712"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="24"/>
            <feGaussianBlur stdDeviation="5"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.678431 0 0 0 0 0.156863 0 0 0 0 0.372549 0 0 0 0.01 0"/>
            <feBlend mode="normal" in2="effect4_dropShadow_1352_22712" result="effect5_dropShadow_1352_22712"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect5_dropShadow_1352_22712" result="shape"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="2"/>
            <feGaussianBlur stdDeviation="2"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.533333 0 0 0 0 0.145098 0 0 0 0 0.298039 0 0 0 0.12 0"/>
            <feBlend mode="normal" in2="shape" result="effect6_innerShadow_1352_22712"/>
        </filter>
        <filter id="filter6_bddddi_1352_22712" x="167.5" y="168.5" width="71" height="69" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feGaussianBlur in="BackgroundImageFix" stdDeviation="12"/>
            <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1352_22712"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="2"/>
            <feGaussianBlur stdDeviation="1.5"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.678431 0 0 0 0 0.156863 0 0 0 0 0.372549 0 0 0 0.08 0"/>
            <feBlend mode="normal" in2="effect1_backgroundBlur_1352_22712" result="effect2_dropShadow_1352_22712"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="6"/>
            <feGaussianBlur stdDeviation="3"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.678431 0 0 0 0 0.156863 0 0 0 0 0.372549 0 0 0 0.07 0"/>
            <feBlend mode="normal" in2="effect2_dropShadow_1352_22712" result="effect3_dropShadow_1352_22712"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="14"/>
            <feGaussianBlur stdDeviation="4"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.678431 0 0 0 0 0.156863 0 0 0 0 0.372549 0 0 0 0.04 0"/>
            <feBlend mode="normal" in2="effect3_dropShadow_1352_22712" result="effect4_dropShadow_1352_22712"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="24"/>
            <feGaussianBlur stdDeviation="5"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.678431 0 0 0 0 0.156863 0 0 0 0 0.372549 0 0 0 0.01 0"/>
            <feBlend mode="normal" in2="effect4_dropShadow_1352_22712" result="effect5_dropShadow_1352_22712"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect5_dropShadow_1352_22712" result="shape"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="2"/>
            <feGaussianBlur stdDeviation="2"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.533333 0 0 0 0 0.145098 0 0 0 0 0.298039 0 0 0 0.12 0"/>
            <feBlend mode="normal" in2="shape" result="effect6_innerShadow_1352_22712"/>
        </filter>
        <filter id="filter7_bddddi_1352_22712" x="173.5" y="179.5" width="71" height="69" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feGaussianBlur in="BackgroundImageFix" stdDeviation="12"/>
            <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1352_22712"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="2"/>
            <feGaussianBlur stdDeviation="1.5"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.678431 0 0 0 0 0.156863 0 0 0 0 0.372549 0 0 0 0.08 0"/>
            <feBlend mode="normal" in2="effect1_backgroundBlur_1352_22712" result="effect2_dropShadow_1352_22712"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="6"/>
            <feGaussianBlur stdDeviation="3"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.678431 0 0 0 0 0.156863 0 0 0 0 0.372549 0 0 0 0.07 0"/>
            <feBlend mode="normal" in2="effect2_dropShadow_1352_22712" result="effect3_dropShadow_1352_22712"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="14"/>
            <feGaussianBlur stdDeviation="4"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.678431 0 0 0 0 0.156863 0 0 0 0 0.372549 0 0 0 0.04 0"/>
            <feBlend mode="normal" in2="effect3_dropShadow_1352_22712" result="effect4_dropShadow_1352_22712"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="24"/>
            <feGaussianBlur stdDeviation="5"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.678431 0 0 0 0 0.156863 0 0 0 0 0.372549 0 0 0 0.01 0"/>
            <feBlend mode="normal" in2="effect4_dropShadow_1352_22712" result="effect5_dropShadow_1352_22712"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect5_dropShadow_1352_22712" result="shape"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="2"/>
            <feGaussianBlur stdDeviation="2"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.533333 0 0 0 0 0.145098 0 0 0 0 0.298039 0 0 0 0.12 0"/>
            <feBlend mode="normal" in2="shape" result="effect6_innerShadow_1352_22712"/>
        </filter>
        <radialGradient id="paint0_radial_1352_22712" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(133.5 128.5) rotate(180) scale(133)">
            <stop/>
            <stop offset="0.86"/>
            <stop offset="1" stop-color="white" stop-opacity="0"/>
        </radialGradient>
        <clipPath id="clip0_1352_22712">
            <rect width="256" height="256" fill="white" transform="translate(0.5 0.5)"/>
        </clipPath>
        <clipPath id="clip1_1352_22712">
            <rect width="137.851" height="135.532" fill="white" transform="translate(19.5 28.5)"/>
        </clipPath>
    </defs>
</svg>
