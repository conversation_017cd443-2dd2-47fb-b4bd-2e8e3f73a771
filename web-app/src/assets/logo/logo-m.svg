<svg width="161" height="92" viewBox="0 0 161 92" fill="none" xmlns="http://www.w3.org/2000/svg">
  <foreignObject x="1.256" y="-19.9" width="91.1648" height="75.64"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(12px);clip-path:url(#bgblur_0_1_24_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_ddddi_1_24)" data-figma-bg-blur-radius="24">
  <path d="M37.664 12.764C37.664 14.684 37.344 16.292 36.704 17.588C36.064 18.884 35.12 19.86 33.872 20.516C32.624 21.172 31.088 21.5 29.264 21.5H25.256V4.364H29.768C31.464 4.364 32.896 4.684 34.064 5.324C35.248 5.964 36.144 6.908 36.752 8.156C37.36 9.404 37.664 10.94 37.664 12.764ZM36.2 12.812C36.2 11.196 35.952 9.86 35.456 8.804C34.976 7.732 34.24 6.932 33.248 6.404C32.256 5.86 31.008 5.588 29.504 5.588H26.648V20.276H29.192C31.544 20.276 33.296 19.66 34.448 18.428C35.616 17.18 36.2 15.308 36.2 12.812ZM45.0301 21.5H40.2061V20.636L41.9101 20.372V5.516L40.2061 5.228V4.364H45.0301V5.228L43.3021 5.516V20.372L45.0301 20.636V21.5ZM54.8683 12.788H60.7243V20.732C59.9243 21.068 59.0843 21.324 58.2043 21.5C57.3403 21.66 56.4043 21.74 55.3963 21.74C53.6843 21.74 52.2443 21.396 51.0763 20.708C49.9243 20.004 49.0443 18.996 48.4363 17.684C47.8443 16.372 47.5483 14.796 47.5483 12.956C47.5483 11.212 47.8763 9.684 48.5323 8.372C49.1883 7.044 50.1323 6.004 51.3643 5.252C52.5963 4.484 54.0763 4.1 55.8043 4.1C56.6843 4.1 57.5163 4.188 58.3003 4.364C59.0843 4.524 59.8283 4.78 60.5323 5.132L60.0043 6.356C59.3323 6.02 58.6363 5.772 57.9163 5.612C57.2123 5.436 56.4843 5.348 55.7323 5.348C54.3403 5.348 53.1403 5.668 52.1323 6.308C51.1243 6.948 50.3483 7.836 49.8043 8.972C49.2603 10.108 48.9883 11.436 48.9883 12.956C48.9883 14.604 49.2363 15.988 49.7323 17.108C50.2443 18.228 50.9883 19.076 51.9643 19.652C52.9563 20.228 54.1803 20.516 55.6363 20.516C56.1643 20.516 56.6443 20.492 57.0763 20.444C57.5243 20.38 57.9323 20.3 58.3003 20.204C58.6843 20.108 59.0363 20.004 59.3563 19.892V14.036H54.8683V12.788ZM68.4208 21.5H63.5968V20.636L65.3008 20.372V5.516L63.5968 5.228V4.364H68.4208V5.228L66.6928 5.516V20.372L68.4208 20.636V21.5Z" fill="#C8D1F0"/>
</g>
  <foreignObject x="-13.744" y="-0.876001" width="104.739" height="75.616"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(12px);clip-path:url(#bgblur_1_1_24_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter1_ddddi_1_24)" data-figma-bg-blur-radius="24">
  <path d="M11.648 40.5H10.256V23.364H19.112V24.612H11.648V31.668H18.704V32.916H11.648V40.5ZM25.3895 40.5H20.5655V39.636L22.2695 39.372V24.516L20.5655 24.228V23.364H25.3895V24.228L23.6615 24.516V39.372L25.3895 39.636V40.5ZM37.6996 36.06C37.6996 37.084 37.4596 37.948 36.9796 38.652C36.4996 39.34 35.8356 39.86 34.9876 40.212C34.1556 40.564 33.2036 40.74 32.1316 40.74C31.4756 40.74 30.8756 40.7 30.3316 40.62C29.8036 40.556 29.3236 40.476 28.8916 40.38C28.4756 40.268 28.0836 40.14 27.7156 39.996V38.604C28.3076 38.844 28.9796 39.06 29.7316 39.252C30.4836 39.428 31.2996 39.516 32.1796 39.516C33.0116 39.516 33.7316 39.388 34.3396 39.132C34.9636 38.876 35.4436 38.5 35.7796 38.004C36.1316 37.508 36.3076 36.884 36.3076 36.132C36.3076 35.444 36.1556 34.884 35.8516 34.452C35.5636 34.02 35.1156 33.644 34.5076 33.324C33.9156 32.988 33.1476 32.652 32.2036 32.316C31.5476 32.06 30.9556 31.796 30.4276 31.524C29.8996 31.252 29.4436 30.932 29.0596 30.564C28.6916 30.196 28.4036 29.764 28.1956 29.268C28.0036 28.772 27.9076 28.172 27.9076 27.468C27.9076 26.524 28.1316 25.732 28.5796 25.092C29.0276 24.452 29.6356 23.964 30.4036 23.628C31.1876 23.292 32.0756 23.124 33.0676 23.124C33.8676 23.124 34.6116 23.204 35.2996 23.364C36.0036 23.508 36.6756 23.732 37.3156 24.036L36.8596 25.236C36.2196 24.948 35.5796 24.732 34.9396 24.588C34.2996 24.444 33.6596 24.372 33.0196 24.372C32.2836 24.372 31.6356 24.492 31.0756 24.732C30.5316 24.956 30.0996 25.3 29.7796 25.764C29.4756 26.212 29.3236 26.772 29.3236 27.444C29.3236 28.18 29.4756 28.772 29.7796 29.22C30.0836 29.668 30.5156 30.044 31.0756 30.348C31.6516 30.636 32.3396 30.932 33.1396 31.236C34.0996 31.572 34.9156 31.94 35.5876 32.34C36.2756 32.74 36.7956 33.228 37.1476 33.804C37.5156 34.38 37.6996 35.132 37.6996 36.06ZM47.9163 24.372C46.9403 24.372 46.0603 24.556 45.2763 24.924C44.5083 25.276 43.8523 25.788 43.3083 26.46C42.7803 27.116 42.3723 27.908 42.0843 28.836C41.8123 29.748 41.6763 30.764 41.6763 31.884C41.6763 33.42 41.9003 34.764 42.3483 35.916C42.7963 37.052 43.4683 37.932 44.3643 38.556C45.2603 39.18 46.3723 39.492 47.7003 39.492C48.4843 39.492 49.2043 39.428 49.8603 39.3C50.5163 39.156 51.1323 38.988 51.7083 38.796V40.02C51.1643 40.244 50.5563 40.42 49.8843 40.548C49.2283 40.676 48.4523 40.74 47.5563 40.74C45.9403 40.74 44.5883 40.372 43.5003 39.636C42.4123 38.9 41.5883 37.868 41.0283 36.54C40.4843 35.212 40.2123 33.66 40.2123 31.884C40.2123 30.62 40.3803 29.452 40.7163 28.38C41.0683 27.308 41.5723 26.38 42.2283 25.596C42.8843 24.812 43.6923 24.204 44.6523 23.772C45.6123 23.34 46.7083 23.124 47.9403 23.124C48.7243 23.124 49.4683 23.204 50.1723 23.364C50.8923 23.524 51.5643 23.756 52.1883 24.06L51.6363 25.284C51.0603 24.98 50.4603 24.756 49.8363 24.612C49.2283 24.452 48.5883 24.372 47.9163 24.372ZM66.9948 40.5H65.6028V32.148H56.5308V40.5H55.1388V23.364H56.5308V30.9H65.6028V23.364H66.9948V40.5Z" fill="#163BBF" fill-opacity="0.96"/>
</g>
  <foreignObject x="1.256" y="18.1" width="92.4864" height="75.64"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(12px);clip-path:url(#bgblur_2_1_24_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter2_ddddi_1_24)" data-figma-bg-blur-radius="24">
  <path d="M37.664 50.764C37.664 52.684 37.344 54.292 36.704 55.588C36.064 56.884 35.12 57.86 33.872 58.516C32.624 59.172 31.088 59.5 29.264 59.5H25.256V42.364H29.768C31.464 42.364 32.896 42.684 34.064 43.324C35.248 43.964 36.144 44.908 36.752 46.156C37.36 47.404 37.664 48.94 37.664 50.764ZM36.2 50.812C36.2 49.196 35.952 47.86 35.456 46.804C34.976 45.732 34.24 44.932 33.248 44.404C32.256 43.86 31.008 43.588 29.504 43.588H26.648V58.276H29.192C31.544 58.276 33.296 57.66 34.448 56.428C35.616 55.18 36.2 53.308 36.2 50.812ZM55.0141 50.908C55.0141 52.22 54.8621 53.412 54.5581 54.484C54.2541 55.556 53.7981 56.492 53.1901 57.292C52.5821 58.076 51.8221 58.684 50.9101 59.116C49.9981 59.532 48.9421 59.74 47.7421 59.74C46.5261 59.74 45.4621 59.524 44.5501 59.092C43.6381 58.66 42.8781 58.052 42.2701 57.268C41.6621 56.484 41.2061 55.556 40.9021 54.484C40.6141 53.396 40.4701 52.196 40.4701 50.884C40.4701 49.14 40.7421 47.612 41.2861 46.3C41.8461 44.988 42.6701 43.964 43.7581 43.228C44.8461 42.476 46.1981 42.1 47.8141 42.1C49.3661 42.1 50.6781 42.46 51.7501 43.18C52.8221 43.884 53.6301 44.892 54.1741 46.204C54.7341 47.516 55.0141 49.084 55.0141 50.908ZM41.9341 50.884C41.9341 52.42 42.1421 53.756 42.5581 54.892C42.9741 56.028 43.6141 56.916 44.4781 57.556C45.3421 58.196 46.4381 58.516 47.7661 58.516C49.0941 58.516 50.1821 58.204 51.0301 57.58C51.8941 56.94 52.5261 56.052 52.9261 54.916C53.3421 53.78 53.5501 52.444 53.5501 50.908C53.5501 48.524 53.0701 46.668 52.1101 45.34C51.1501 44.012 49.7181 43.348 47.8141 43.348C46.4861 43.348 45.3821 43.66 44.5021 44.284C43.6381 44.908 42.9901 45.788 42.5581 46.924C42.1421 48.06 41.9341 49.38 41.9341 50.884ZM69.7424 59.5H68.0864L62.0384 50.62L60.0464 52.66V59.5H58.6544V42.364H60.0464V51.268C60.3504 50.9 60.6624 50.532 60.9824 50.164C61.3184 49.78 61.6464 49.396 61.9664 49.012L67.8704 42.364H69.5744L63.0464 49.66L69.7424 59.5Z" fill="#C8D1F0"/>
</g>
  <rect x="78" y="4.5" width="1" height="55" fill="#9FAFE6"/>
  <foreignObject x="72" y="-17" width="97" height="108"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(12px);clip-path:url(#bgblur_3_1_24_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter3_ddddi_1_24)" data-figma-bg-blur-radius="24">
  <path d="M104 23L120 7L145 32L120 57L104 41L100 45L96 41L100 32L96 23L100 19L104 23Z" fill="white" fill-opacity="0.24"/>
</g>
  <foreignObject x="63" y="-12" width="96" height="98"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(12px);clip-path:url(#bgblur_4_1_24_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter4_ddddi_1_24)" data-figma-bg-blur-radius="24">
  <path d="M92 19L100 27L115 12L135 32L115 52L100 37L92 45L87 40L95 32L87 24L92 19Z" fill="#163BBF" fill-opacity="0.96"/>
</g>
  <path d="M122 32L112 42L102 32L112 22L122 32ZM110.061 26.0605L116 32L110.061 37.9395L112 39.8789L119.879 32L112 24.1211L110.061 26.0605ZM107.414 29.4141L110 32L107.414 34.5859L109 36.1719L113.172 32L109 27.8281L107.414 29.4141Z" fill="white" fill-opacity="0.24"/>
  <defs>
    <filter id="filter0_ddddi_1_24" x="1.256" y="-19.9" width="91.1648" height="75.64" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="2"/>
      <feGaussianBlur stdDeviation="1.5"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.08 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1_24"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="6"/>
      <feGaussianBlur stdDeviation="3"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.07 0"/>
      <feBlend mode="normal" in2="effect1_dropShadow_1_24" result="effect2_dropShadow_1_24"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="14"/>
      <feGaussianBlur stdDeviation="4"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.04 0"/>
      <feBlend mode="normal" in2="effect2_dropShadow_1_24" result="effect3_dropShadow_1_24"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="24"/>
      <feGaussianBlur stdDeviation="5"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.01 0"/>
      <feBlend mode="normal" in2="effect3_dropShadow_1_24" result="effect4_dropShadow_1_24"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect4_dropShadow_1_24" result="shape"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="2"/>
      <feGaussianBlur stdDeviation="2"/>
      <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.12 0"/>
      <feBlend mode="normal" in2="shape" result="effect5_innerShadow_1_24"/>
    </filter>
    <clipPath id="bgblur_0_1_24_clip_path" transform="translate(-1.256 19.9)"><path d="M37.664 12.764C37.664 14.684 37.344 16.292 36.704 17.588C36.064 18.884 35.12 19.86 33.872 20.516C32.624 21.172 31.088 21.5 29.264 21.5H25.256V4.364H29.768C31.464 4.364 32.896 4.684 34.064 5.324C35.248 5.964 36.144 6.908 36.752 8.156C37.36 9.404 37.664 10.94 37.664 12.764ZM36.2 12.812C36.2 11.196 35.952 9.86 35.456 8.804C34.976 7.732 34.24 6.932 33.248 6.404C32.256 5.86 31.008 5.588 29.504 5.588H26.648V20.276H29.192C31.544 20.276 33.296 19.66 34.448 18.428C35.616 17.18 36.2 15.308 36.2 12.812ZM45.0301 21.5H40.2061V20.636L41.9101 20.372V5.516L40.2061 5.228V4.364H45.0301V5.228L43.3021 5.516V20.372L45.0301 20.636V21.5ZM54.8683 12.788H60.7243V20.732C59.9243 21.068 59.0843 21.324 58.2043 21.5C57.3403 21.66 56.4043 21.74 55.3963 21.74C53.6843 21.74 52.2443 21.396 51.0763 20.708C49.9243 20.004 49.0443 18.996 48.4363 17.684C47.8443 16.372 47.5483 14.796 47.5483 12.956C47.5483 11.212 47.8763 9.684 48.5323 8.372C49.1883 7.044 50.1323 6.004 51.3643 5.252C52.5963 4.484 54.0763 4.1 55.8043 4.1C56.6843 4.1 57.5163 4.188 58.3003 4.364C59.0843 4.524 59.8283 4.78 60.5323 5.132L60.0043 6.356C59.3323 6.02 58.6363 5.772 57.9163 5.612C57.2123 5.436 56.4843 5.348 55.7323 5.348C54.3403 5.348 53.1403 5.668 52.1323 6.308C51.1243 6.948 50.3483 7.836 49.8043 8.972C49.2603 10.108 48.9883 11.436 48.9883 12.956C48.9883 14.604 49.2363 15.988 49.7323 17.108C50.2443 18.228 50.9883 19.076 51.9643 19.652C52.9563 20.228 54.1803 20.516 55.6363 20.516C56.1643 20.516 56.6443 20.492 57.0763 20.444C57.5243 20.38 57.9323 20.3 58.3003 20.204C58.6843 20.108 59.0363 20.004 59.3563 19.892V14.036H54.8683V12.788ZM68.4208 21.5H63.5968V20.636L65.3008 20.372V5.516L63.5968 5.228V4.364H68.4208V5.228L66.6928 5.516V20.372L68.4208 20.636V21.5Z"/>
    </clipPath><filter id="filter1_ddddi_1_24" x="-13.744" y="-0.876001" width="104.739" height="75.616" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
    <feFlood flood-opacity="0" result="BackgroundImageFix"/>
    <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
    <feOffset dy="2"/>
    <feGaussianBlur stdDeviation="1.5"/>
    <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.08 0"/>
    <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1_24"/>
    <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
    <feOffset dy="6"/>
    <feGaussianBlur stdDeviation="3"/>
    <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.07 0"/>
    <feBlend mode="normal" in2="effect1_dropShadow_1_24" result="effect2_dropShadow_1_24"/>
    <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
    <feOffset dy="14"/>
    <feGaussianBlur stdDeviation="4"/>
    <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.04 0"/>
    <feBlend mode="normal" in2="effect2_dropShadow_1_24" result="effect3_dropShadow_1_24"/>
    <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
    <feOffset dy="24"/>
    <feGaussianBlur stdDeviation="5"/>
    <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.01 0"/>
    <feBlend mode="normal" in2="effect3_dropShadow_1_24" result="effect4_dropShadow_1_24"/>
    <feBlend mode="normal" in="SourceGraphic" in2="effect4_dropShadow_1_24" result="shape"/>
    <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
    <feOffset dy="2"/>
    <feGaussianBlur stdDeviation="2"/>
    <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
    <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.12 0"/>
    <feBlend mode="normal" in2="shape" result="effect5_innerShadow_1_24"/>
  </filter>
    <clipPath id="bgblur_1_1_24_clip_path" transform="translate(13.744 0.876001)"><path d="M11.648 40.5H10.256V23.364H19.112V24.612H11.648V31.668H18.704V32.916H11.648V40.5ZM25.3895 40.5H20.5655V39.636L22.2695 39.372V24.516L20.5655 24.228V23.364H25.3895V24.228L23.6615 24.516V39.372L25.3895 39.636V40.5ZM37.6996 36.06C37.6996 37.084 37.4596 37.948 36.9796 38.652C36.4996 39.34 35.8356 39.86 34.9876 40.212C34.1556 40.564 33.2036 40.74 32.1316 40.74C31.4756 40.74 30.8756 40.7 30.3316 40.62C29.8036 40.556 29.3236 40.476 28.8916 40.38C28.4756 40.268 28.0836 40.14 27.7156 39.996V38.604C28.3076 38.844 28.9796 39.06 29.7316 39.252C30.4836 39.428 31.2996 39.516 32.1796 39.516C33.0116 39.516 33.7316 39.388 34.3396 39.132C34.9636 38.876 35.4436 38.5 35.7796 38.004C36.1316 37.508 36.3076 36.884 36.3076 36.132C36.3076 35.444 36.1556 34.884 35.8516 34.452C35.5636 34.02 35.1156 33.644 34.5076 33.324C33.9156 32.988 33.1476 32.652 32.2036 32.316C31.5476 32.06 30.9556 31.796 30.4276 31.524C29.8996 31.252 29.4436 30.932 29.0596 30.564C28.6916 30.196 28.4036 29.764 28.1956 29.268C28.0036 28.772 27.9076 28.172 27.9076 27.468C27.9076 26.524 28.1316 25.732 28.5796 25.092C29.0276 24.452 29.6356 23.964 30.4036 23.628C31.1876 23.292 32.0756 23.124 33.0676 23.124C33.8676 23.124 34.6116 23.204 35.2996 23.364C36.0036 23.508 36.6756 23.732 37.3156 24.036L36.8596 25.236C36.2196 24.948 35.5796 24.732 34.9396 24.588C34.2996 24.444 33.6596 24.372 33.0196 24.372C32.2836 24.372 31.6356 24.492 31.0756 24.732C30.5316 24.956 30.0996 25.3 29.7796 25.764C29.4756 26.212 29.3236 26.772 29.3236 27.444C29.3236 28.18 29.4756 28.772 29.7796 29.22C30.0836 29.668 30.5156 30.044 31.0756 30.348C31.6516 30.636 32.3396 30.932 33.1396 31.236C34.0996 31.572 34.9156 31.94 35.5876 32.34C36.2756 32.74 36.7956 33.228 37.1476 33.804C37.5156 34.38 37.6996 35.132 37.6996 36.06ZM47.9163 24.372C46.9403 24.372 46.0603 24.556 45.2763 24.924C44.5083 25.276 43.8523 25.788 43.3083 26.46C42.7803 27.116 42.3723 27.908 42.0843 28.836C41.8123 29.748 41.6763 30.764 41.6763 31.884C41.6763 33.42 41.9003 34.764 42.3483 35.916C42.7963 37.052 43.4683 37.932 44.3643 38.556C45.2603 39.18 46.3723 39.492 47.7003 39.492C48.4843 39.492 49.2043 39.428 49.8603 39.3C50.5163 39.156 51.1323 38.988 51.7083 38.796V40.02C51.1643 40.244 50.5563 40.42 49.8843 40.548C49.2283 40.676 48.4523 40.74 47.5563 40.74C45.9403 40.74 44.5883 40.372 43.5003 39.636C42.4123 38.9 41.5883 37.868 41.0283 36.54C40.4843 35.212 40.2123 33.66 40.2123 31.884C40.2123 30.62 40.3803 29.452 40.7163 28.38C41.0683 27.308 41.5723 26.38 42.2283 25.596C42.8843 24.812 43.6923 24.204 44.6523 23.772C45.6123 23.34 46.7083 23.124 47.9403 23.124C48.7243 23.124 49.4683 23.204 50.1723 23.364C50.8923 23.524 51.5643 23.756 52.1883 24.06L51.6363 25.284C51.0603 24.98 50.4603 24.756 49.8363 24.612C49.2283 24.452 48.5883 24.372 47.9163 24.372ZM66.9948 40.5H65.6028V32.148H56.5308V40.5H55.1388V23.364H56.5308V30.9H65.6028V23.364H66.9948V40.5Z"/>
    </clipPath><filter id="filter2_ddddi_1_24" x="1.256" y="18.1" width="92.4864" height="75.64" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
    <feFlood flood-opacity="0" result="BackgroundImageFix"/>
    <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
    <feOffset dy="2"/>
    <feGaussianBlur stdDeviation="1.5"/>
    <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.08 0"/>
    <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1_24"/>
    <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
    <feOffset dy="6"/>
    <feGaussianBlur stdDeviation="3"/>
    <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.07 0"/>
    <feBlend mode="normal" in2="effect1_dropShadow_1_24" result="effect2_dropShadow_1_24"/>
    <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
    <feOffset dy="14"/>
    <feGaussianBlur stdDeviation="4"/>
    <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.04 0"/>
    <feBlend mode="normal" in2="effect2_dropShadow_1_24" result="effect3_dropShadow_1_24"/>
    <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
    <feOffset dy="24"/>
    <feGaussianBlur stdDeviation="5"/>
    <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.01 0"/>
    <feBlend mode="normal" in2="effect3_dropShadow_1_24" result="effect4_dropShadow_1_24"/>
    <feBlend mode="normal" in="SourceGraphic" in2="effect4_dropShadow_1_24" result="shape"/>
    <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
    <feOffset dy="2"/>
    <feGaussianBlur stdDeviation="2"/>
    <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
    <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.12 0"/>
    <feBlend mode="normal" in2="shape" result="effect5_innerShadow_1_24"/>
  </filter>
    <clipPath id="bgblur_2_1_24_clip_path" transform="translate(-1.256 -18.1)"><path d="M37.664 50.764C37.664 52.684 37.344 54.292 36.704 55.588C36.064 56.884 35.12 57.86 33.872 58.516C32.624 59.172 31.088 59.5 29.264 59.5H25.256V42.364H29.768C31.464 42.364 32.896 42.684 34.064 43.324C35.248 43.964 36.144 44.908 36.752 46.156C37.36 47.404 37.664 48.94 37.664 50.764ZM36.2 50.812C36.2 49.196 35.952 47.86 35.456 46.804C34.976 45.732 34.24 44.932 33.248 44.404C32.256 43.86 31.008 43.588 29.504 43.588H26.648V58.276H29.192C31.544 58.276 33.296 57.66 34.448 56.428C35.616 55.18 36.2 53.308 36.2 50.812ZM55.0141 50.908C55.0141 52.22 54.8621 53.412 54.5581 54.484C54.2541 55.556 53.7981 56.492 53.1901 57.292C52.5821 58.076 51.8221 58.684 50.9101 59.116C49.9981 59.532 48.9421 59.74 47.7421 59.74C46.5261 59.74 45.4621 59.524 44.5501 59.092C43.6381 58.66 42.8781 58.052 42.2701 57.268C41.6621 56.484 41.2061 55.556 40.9021 54.484C40.6141 53.396 40.4701 52.196 40.4701 50.884C40.4701 49.14 40.7421 47.612 41.2861 46.3C41.8461 44.988 42.6701 43.964 43.7581 43.228C44.8461 42.476 46.1981 42.1 47.8141 42.1C49.3661 42.1 50.6781 42.46 51.7501 43.18C52.8221 43.884 53.6301 44.892 54.1741 46.204C54.7341 47.516 55.0141 49.084 55.0141 50.908ZM41.9341 50.884C41.9341 52.42 42.1421 53.756 42.5581 54.892C42.9741 56.028 43.6141 56.916 44.4781 57.556C45.3421 58.196 46.4381 58.516 47.7661 58.516C49.0941 58.516 50.1821 58.204 51.0301 57.58C51.8941 56.94 52.5261 56.052 52.9261 54.916C53.3421 53.78 53.5501 52.444 53.5501 50.908C53.5501 48.524 53.0701 46.668 52.1101 45.34C51.1501 44.012 49.7181 43.348 47.8141 43.348C46.4861 43.348 45.3821 43.66 44.5021 44.284C43.6381 44.908 42.9901 45.788 42.5581 46.924C42.1421 48.06 41.9341 49.38 41.9341 50.884ZM69.7424 59.5H68.0864L62.0384 50.62L60.0464 52.66V59.5H58.6544V42.364H60.0464V51.268C60.3504 50.9 60.6624 50.532 60.9824 50.164C61.3184 49.78 61.6464 49.396 61.9664 49.012L67.8704 42.364H69.5744L63.0464 49.66L69.7424 59.5Z"/>
    </clipPath><filter id="filter3_ddddi_1_24" x="72" y="-17" width="97" height="108" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
    <feFlood flood-opacity="0" result="BackgroundImageFix"/>
    <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
    <feOffset dy="2"/>
    <feGaussianBlur stdDeviation="1.5"/>
    <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.08 0"/>
    <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1_24"/>
    <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
    <feOffset dy="6"/>
    <feGaussianBlur stdDeviation="3"/>
    <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.07 0"/>
    <feBlend mode="normal" in2="effect1_dropShadow_1_24" result="effect2_dropShadow_1_24"/>
    <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
    <feOffset dy="14"/>
    <feGaussianBlur stdDeviation="4"/>
    <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.04 0"/>
    <feBlend mode="normal" in2="effect2_dropShadow_1_24" result="effect3_dropShadow_1_24"/>
    <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
    <feOffset dy="24"/>
    <feGaussianBlur stdDeviation="5"/>
    <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.01 0"/>
    <feBlend mode="normal" in2="effect3_dropShadow_1_24" result="effect4_dropShadow_1_24"/>
    <feBlend mode="normal" in="SourceGraphic" in2="effect4_dropShadow_1_24" result="shape"/>
    <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
    <feOffset dy="2"/>
    <feGaussianBlur stdDeviation="2"/>
    <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
    <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.12 0"/>
    <feBlend mode="normal" in2="shape" result="effect5_innerShadow_1_24"/>
  </filter>
    <clipPath id="bgblur_3_1_24_clip_path" transform="translate(-72 17)"><path d="M104 23L120 7L145 32L120 57L104 41L100 45L96 41L100 32L96 23L100 19L104 23Z"/>
    </clipPath><filter id="filter4_ddddi_1_24" x="63" y="-12" width="96" height="98" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
    <feFlood flood-opacity="0" result="BackgroundImageFix"/>
    <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
    <feOffset dy="2"/>
    <feGaussianBlur stdDeviation="1.5"/>
    <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.08 0"/>
    <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1_24"/>
    <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
    <feOffset dy="6"/>
    <feGaussianBlur stdDeviation="3"/>
    <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.07 0"/>
    <feBlend mode="normal" in2="effect1_dropShadow_1_24" result="effect2_dropShadow_1_24"/>
    <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
    <feOffset dy="14"/>
    <feGaussianBlur stdDeviation="4"/>
    <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.04 0"/>
    <feBlend mode="normal" in2="effect2_dropShadow_1_24" result="effect3_dropShadow_1_24"/>
    <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
    <feOffset dy="24"/>
    <feGaussianBlur stdDeviation="5"/>
    <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.01 0"/>
    <feBlend mode="normal" in2="effect3_dropShadow_1_24" result="effect4_dropShadow_1_24"/>
    <feBlend mode="normal" in="SourceGraphic" in2="effect4_dropShadow_1_24" result="shape"/>
    <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
    <feOffset dy="2"/>
    <feGaussianBlur stdDeviation="2"/>
    <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
    <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.12 0"/>
    <feBlend mode="normal" in2="shape" result="effect5_innerShadow_1_24"/>
  </filter>
    <clipPath id="bgblur_4_1_24_clip_path" transform="translate(-63 12)"><path d="M92 19L100 27L115 12L135 32L115 52L100 37L92 45L87 40L95 32L87 24L92 19Z"/>
    </clipPath></defs>
</svg>
