# Statische Mandantenkonfiguration

Dieses Dokument umfasst alle statischen Werte in der Mandantenkonfiguration. Im folgenden sind die Keys aufgelistet in ihren jeweiligen Namespaces.
Beispielwerte sind unter _backend/register-service/src/main/resources/config/tenant/default.yaml_ zu finden.

## Common

| Key                 | Beschreibung                                        | Benutzt in                    |
| ------------------- | --------------------------------------------------- | ----------------------------- |
| `common.cancel`     | Wert für Buttons, die die aktuelle Aktion abbrechen |                               |
| `common.start_page` | Wert für Buttons, die zur Startseite führen         | Profile Header, Digitize Page |

## Header

| Key                             | Beschreibung                                                         | Ben<PERSON><PERSON> in                        |
| ------------------------------- | -------------------------------------------------------------------- | --------------------------------- |
| `header.federal_state_logo.url` | URL zum Logo des Bundeslands des Mandanten                           | Header, FederalStateLogoComponent |
| `header.user.government_office` | Behördenname im Nutzerprofil, siehe Template Replacement             | Header                            |
| `header.user.display_name`      | Anzeigename des eingeloggten Nutzers, verwendet Template Replacement | Header                            |

## Search

| Key                      | Beschreibung          | Benutzt in          |
| ------------------------ | --------------------- | ------------------- |
| `search.bar.placeholder` |                       | Homepage, SearchBar |
| `search.bar.button`      | Text im Suchen-Button | Homepage, SearchBar |

## Homepage

| Key                                     | Beschreibung | Benutzt in |
| --------------------------------------- | ------------ | ---------- |
| `home_page.title`                       |              | Homepage   |
| `home_page.add_section.title `          |              | Homepage   |
| `home_page.add_section.vacation_button` |              | Homepage   |
| `home_page.add_section.digitize_button` |              | Homepage   |

## Digitize

| Key              | Beschreibung                                | Benutzt in    |
| ---------------- | ------------------------------------------- | ------------- |
| `digitize.title` | Im Profile Header als Überschrift angezeigt | Digitize Page |

## Profile

| Key                                                  | Beschreibung | Benutzt in                        |
| ---------------------------------------------------- | ------------ | --------------------------------- |
| `profile.title`                                      |              | Personal Data Form, Digitize Page |
| `profile.first_name`                                 |              | Personal Data Form, Digitize Page |
| `profile.first_name_placeholder`                     |              | Personal Data Form, Digitize Page |
| `profile.last_name`                                  |              | Personal Data Form, Digitize Page |
| `profile.last_name_placeholder`                      |              | Personal Data Form, Digitize Page |
| `profile.birth_name`                                 |              | Personal Data Form, Digitize Page |
| `profile.birth_name_sub_label`                       |              | Personal Data Form, Digitize Page |
| `profile.birth_name_placeholder`                     |              | Personal Data Form, Digitize Page |
| `profile.birth_date`                                 |              | Personal Data Form, Digitize Page |
| `profile.birth_date_placeholder`                     |              | Personal Data Form, Digitize Page |
| `profile.birth_place`                                |              | Personal Data Form, Digitize Page |
| `profile.birth_place_placeholder`                    |              | Personal Data Form, Digitize Page |
| `profile.nationality`                                |              | Personal Data Form, Digitize Page |
| `profile.address.title`                              |              | Personal Data Form, Digitize Page |
| `profile.address.no_permanent_residence_title`       |              | Personal Data Form, Digitize Page |
| `profile.address.no_permanent_residence_description` |              | Personal Data Form, Digitize Page |
| `profile.address.street`                             |              | Personal Data Form, Digitize Page |
| `profile.address.street.placeholder`                 |              | Personal Data Form, Digitize Page |
| `profile.address.street_number`                      |              | Personal Data Form, Digitize Page |
| `profile.address.street_number.placeholder`          |              | Personal Data Form, Digitize Page |
| `profile.address.postcode`                           |              | Personal Data Form, Digitize Page |
| `profile.address.postcode.placeholder`               |              | Personal Data Form, Digitize Page |
| `profile.address.city`                               |              | Personal Data Form, Digitize Page |
| `profile.address.city.placeholder`                   |              | Personal Data Form, Digitize Page |
| `profile.address.detail`                             |              | Personal Data Form, Digitize Page |
| `profile.address.detail.placeholder`                 |              | Personal Data Form, Digitize Page |

## Consent

| Key                      | Beschreibung | Benutzt in                        |
| ------------------------ | ------------ | --------------------------------- |
| `consent.gdpr.label`     |              | Personal Data Form, Digitize Page |
| `consent.gdpr.sub_label` |              | Personal Data Form, Digitize Page |
