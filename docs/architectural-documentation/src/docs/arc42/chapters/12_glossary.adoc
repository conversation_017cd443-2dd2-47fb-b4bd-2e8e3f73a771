:jbake-title: Glossar
:jbake-type: page_toc
:jbake-status: published
:jbake-menu: arc42
:jbake-order: 12
:filename: /chapters/12_glossary.adoc
ifndef::imagesdir[:imagesdir: ../../images]
ifndef::c4dir[:c4dir: ../structurizr/diagrams]
ifdef::env-vscode[]
:c4dir: ../../structurizr/diagrams
:source-highlighter: highlight.js
endif::[]

:toc:


[[section-glossary]]
== Glossar

// Source: https://confluence.adesso.de/display/Fischereiregister/Glossar

[cols="e,2e" options="header"]
|===
|Begriff |Definition

|[[Application-Programming-Interface,Application Programming Interface]]Application Programming Interface
|Programmierschnittstelle oder auch Anwendungsschnittstelle, die es fremder Software ermöglicht, ein System zu nutzen. In unserem Fall das Register.

|[[App,App]]App
|Hier im Sinne von „mobile App“ eine Software für eine Anwendung für mobile Betriebssysteme, also Mobiltelefone oder Tablets.

|Audit-Protokoll
|Ein Audit-Protokoll ist eine Aufzeichnung aller für ein System rele-
vanten Änderungen an einer Datenbank, also wer welche Ände-
rung wann und wo vorgenommen hat.

|[[Backlog,Backlog]]Backlog
|Eine Sammlung von Anforderungen. Speziell in der agilen Softwareentwicklung ist der Product Backlog die Sammlung der Anforderungen an die Software, die entwickelt werden soll.

Der Backlog ist nicht fix, sondern wird dynamisch angepasst.

|[[Business-Process-and-Modeling-Notation,Business Process and Modeling Notation]]Business Process and Modeling Notation
|BPMN (Business Process and Modeling Notation) ist eine Methode, um Geschäftsprozesse so grafisch darzustellen, dass Auftraggeber und Auftragnehmer ein gemeinsames Verständnis von den Prozessen haben. BPMN 2.0 ist die aktuelle Version dieser Methode.

|[[Clickdummy,Clickdummy]]Clickdummy
|Ein Clickdummy ist ein interaktiver Prototyp einer geplanten Software. An ihm kann man z.B. das Design der Bedienoberfläche erarbeiten bzw. veranschaulichen oder Arbeitsabläufe darstellen.

|[[Einer-für-Alle-Prinzip,"Einer für Alle"-Prinzip]]"Einer für Alle"-Prinzip
|Das "Einer für Alle"-Prinzip im OZG-Kontext bedeutet, dass ein Land oder eine Allianz aus mehreren Ländern eine Leistung zentral entwickelt und betreibt – und diese anschließend anderen Ländern und Kommunen zur Verfügung stellt, die den Dienst dann mitnutzen können.

|[[Fachverfahren,Fachverfahren]]Fachverfahren
|Ein Fachverfahren ist eine Software, in der eine oder mehrere fachliche Tätigkeiten abgebildet werden, z. B. das Bearbeiten von Anträgen.

|[[Fischereiabgabe,Fischereiabgabe]]Fischereiabgabe
|Eine Fischereiabgabe ist eine jährliche, monetäre Abgabe an das Bundesland, im dem der Fischfang durchgeführt werden soll.

|[[Fischereimarke,Fischereimarke]]Fischereimarke
|Eine Fischereimarke weist analog die Zahlung einer Fischereiabgabe nach.

|[[Fischereischeinprüfung,Fischereischeinprüfung]]Fischereischeinprüfung
|Eine Fischereischeinprüfung ist eine verpflichtende Prüfung über die notwendigen Kenntnisse zum Fischfang. Sie ist Voraussetzung zum Erhalt des Fischereischeins. (Synonym in vielen BL: Fischerprüfung).

|[[Fischereischein,Fischereischein]]Fischereischein
|Ein Fischereischein ist der fischereiliche Sachkundenachweis; mit Ausnahme des BL NI ist ein Fischereischein in allen BL Voraussetzung für den Fischfang.

|[[ID,ID]]ID
|Ein Identifikator ist ein einmaliges, eindeutiges Merkmal eines Objektes. Es lässt eine Unterscheidung von ansonsten identischen Objekten zu.

|[[Kontrolleur,Kontrolleur]]Kontrolleur
|Ein Kontrolleur ist eine natürliche Person, die u.a. überprüft, ob fischende Personen einen gültigen Fischereischein besitzen und die Fischereiabgabe bezahlt haben. Außerdem überprüft ein Kontrolleur, ob geltendes Recht beim Ausüben von Fischfang eingehalten wird.

|[[Fischereiaufseher,Fischereiaufseher]]Fischereiaufseher
|siehe <<Kontrolleur>>

|[[Kontroll-App,Kontroll-App]]Kontroll-App
|tbd.

|[[Nutzerkonto-Bund,Nutzerkonto Bund]]Nutzerkonto Bund
|ein OZG-konformes Nutzerkonto für Bundesverwaltungsangelegenheiten. Es kann auch als Zugang zu Verwaltungsleistungen der Bundesländer genutzt werden, sofern das Bundesland das unterstützt (wie Hessen, Saarland und Sachsen-Anhalt).

|[[BundID,BundID]]BundID
|siehe <<Nutzerkonto Bund>>

Je nach Anmeldeverfahren ist das Vertrauensniveau hoch (Online-Ausweis), substanziell (ELSTER-Zertifikat) oder basishaft (Nutzername und Passwort).

|[[Onlinedienst,Onlinedienst]]Onlinedienst
|Ein Onlinedienst ist eine Software, die zur digitalen Antragstellung über das Internet genutzt wird. Hier betrachten wir Onlinedienste im Serviceportal SH.

|[[Payment,Payment]]Payment
|Mit Payment ist der Zahlungsprozess einer Gebühr oder Abgabe in einem Onlinedienst gemeint.

|[[Prüfungs-ID,Prüfungs-ID]]Prüfungs-ID
|Eine Prüfungs- bzw. Fischereischein-ID ist ein eindeutiger Identifikator einer Prüfung bzw. eines Fischereischeins.

|[[Fischereischein-ID,Fischereischein-ID]]Fischereischein-ID
|siehe <<Prüfungs-ID>>

|[[QR-Code,QR-Code]]QR-Code
|„Quick Response“-Code, besteht aus einer quadratischen Matrix aus schwarzen und weißen Quadraten oder Punkten, die die kodierten Daten darstellen. Aufgrund einer automatischen Fehlerkorrektur ist dieses Verfahren sehr robust und daher weit verbreitet.

|[[Register,Register]]Register
|Ein Register ist ein zentraler Speicherort für Informationen. Die Informationen sind in der Regel zur Suche aufbereitet und können von unterschiedlichen Akteuren aufgerufen werden.

|[[Selbstauskunft-zu-Versagungsgründen,Selbstauskunft zu Versagungsgründen]]Selbstauskunft zu Versagungsgründen
|Um den Fischereischein zu erhalten, muss der Antragsteller bestätigen, dass es keine Gründe gibt, die dem entgegenstehen. Die Gründe sind im Landesfischereigesetz genannt (in anderen BL als SH ggf. in abweichenden Rechtsgrundlagen).

|[[ServicekontoPlus,Servicekonto Plus]]Servicekonto Plus 
|Das Servicekonto (+) ist das OZG-konforme Nutzerkonto des Serviceportals SH. Servicekonto und Servicekonto+ unterscheiden sich darin, dass das Servicekonto+ mithilfe des digitalen Personalausweises identifiziert wurde und damit eindeutig zur im Ausweis ausgewiesenen Person gehört. Dadurch hat es hohes Vertrauensniveau. Das (einfache) Servicekonto hat durch Anmeldung mit Nutzernamen und Passwort nur basishaftes Vertrauensniveau.
Servicekonten verfügen immer über ein Postfach, über das eine sichere Kommunikation möglich ist.

|[[Servicekonto+,Servicekonto (+)]]Servicekonto (+)
|siehe <<ServicekontoPlus>>

|[[Vertrauensniveau,Vertrauensniveau]]Vertrauensniveau
|Das Vertrauensniveau gibt den Grad der Vertrauenswürdigkeit einer Sitzung eines Nutzers an. Also inwieweit der Dienst davon ausgehen kann, dass das Nutzerkonto tatsächlich der natürlichen oder juristischen Person gehört und dieses zum aktuellen Zeitpunkt nutzt.

|===

