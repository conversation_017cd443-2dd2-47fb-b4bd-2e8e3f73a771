import "@typespec/http";
import "@typespec/openapi3";
import "../common.tsp";
import "../request/CreateRegularFishingLicenseRequest.tsp";
import "../request/CreateVacationFishingLicenseRequest.tsp";
import "../request/CreateLimitedFishingLicenseRequest.tsp";
import "../response/CreateFishingLicenseResponse.tsp";
import "../request/ExtendFishingLicenseRequest.tsp";
import "../response/FishingLicenseResponse.tsp";
import "../model/enum/FederalStateAbbreviation.tsp";

using TypeSpec.Http;

namespace Fischereiregister;

@tag("Fishing license")
@route("/register-entries/{registerEntryId}/fishing-licenses")
interface FishingLicenseController {
  @doc("""
    Adds a new REGULAR type Fishing license to an already existing register entry.
    """)
  @post
  @route("regular")
  createRegular(
    @path registerEntryId: string,
    @body license: CreateRegularFishingLicenseRequest,
  ): WithCreatedErrors<CreateFishingLicenseResponse, "/register-entries/:registerEntryId/fishing-licenses/:licenseNumber"> | NotFoundResponse;

  @doc("""
  Adds a new LIMTED type fishing license to an already exisiting register entry.

  This api endpoint will return 400, if limited licenses are not available in the tenant of the logged in user.
  """)
  @post()
  @route("limited")
  createLimited(
    @path registerEntryId: string,
    @body createLimitedRequest: CreateLimitedFishingLicenseRequest,
  ): WithCreatedErrors<CreateLimitedLicenseResponse, "/register-entries/:registerEntryId/fishing-licenses/:licenseNumber"> | NotFoundResponse;

  @doc("""
  Adds a new VACATION type fishing license to an already exisiting register entry.
  """)
  @post()
  @route("vacation")
  createVacation(
    @path registerEntryId: string,
    @body createVacationRequest: CreateVacationFishingLicenseRequest,

  ): WithCreatedErrors<CreateFishingLicenseResponse, "/register-entries/:registerEntryId/fishing-licenses/:licenseNumber"> | NotFoundResponse;

  @route("{licenseNumber}:extend")
  @doc("""
    Extends the fishing license by adding the amount by which to extend. Only vacation type licenses are extendable, but not in every tenant.
    
    The correct fee has to be set in order to extend the fishing license. 
    
    The extension period of the license will be set automatically according to the tenant / federal state of the logged in user.
    """)
  @post
  extend(
    @path registerEntryId: string,
    @path licenseNumber: string,
    @body submission: ExtendFishingLicenseRequest,
  ): WithCreatedErrorsNoLocationNorBody | NotFoundResponse;

  @route("{licenseNumber}")
  @doc("Fetches a license")
  @get
  getSingle(
    @path registerEntryId: string,
    @path licenseNumber: string,
  ): WithStandardErrors<FishingLicenseResponse>;
}
