import "@typespec/http";
import "@typespec/openapi3";
import "../response/S3FileResponse.tsp";
import "../common.tsp";
import "../Fischereiregister.tsp";

using TypeSpec.Http;

namespace Fischereiregister;

@tag("S3 Storage")
@route("/storage")
interface S3Controller {
    @doc("Gets a file from the S3 storage.")
    @get
    @route("/")
    get(
        @doc("The path to the object in the S3 bucket")
        @query
        objectPath: string
    ):
        | S3FileResponse
        | NotFoundResponse
        | ServerErrorResponse
        | UnauthorizedResponse;
}
