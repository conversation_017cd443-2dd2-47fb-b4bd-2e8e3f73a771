import "@typespec/http";
import "@typespec/openapi3";
import "../response/NationalityResponse.tsp";
import "../common.tsp";
import "../Fischereiregister.tsp";

using TypeSpec.Http;

namespace Fischereiregister;

@tag("Nationalities")
@route("/nationalities")
interface NationalityController {
    @doc("Gets all nationalities.")
    @get
    @route("/")
    getAll(): SuccessResponse<NationalityResponse> | ServerErrorResponse | UnauthorizedResponse;
}
