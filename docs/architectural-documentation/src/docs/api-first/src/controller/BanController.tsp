import "@typespec/http";
import "@typespec/openapi3";
import "../common.tsp";
import "../request/TemporaryBanRequest.tsp";
import "../request/PermanentBanRequest.tsp";
import "../Fischereiregister.tsp";
import "../response/CreateBanResponse.tsp";

using TypeSpec.Http;


namespace Fischereiregister;

@tag("Ban")
@route("register-entries/{registerEntryId}/ban")
interface BanController {
    @route("permanent")
    @doc("Overwrites the current ban or sets a new permanent ban if no ban is active.")
    @post
    createPermanent(
        @path registerEntryId: string,
        @body ban: PermanentBanRequest,
    ): WithCreatedErrorsNoLocation<CreateBanResponse> | NotFoundResponse;

    @route("temporary")
    @doc("Overwrites the current ban or sets a new temporary ban if no ban is active.")
    @post
    createTemporary(
        @path registerEntryId: string,
        @body ban: TemporaryBanRequest,
    ): WithCreatedErrorsNoLocation<CreateBanResponse> | NotFoundResponse;

    @doc("Deletes the current ban.")
    @delete
    delete(@path registerEntryId: string):
        | NoContentResponse
        | NotFoundResponse
        | UnauthorizedResponse
        | ServerErrorResponse;
}
