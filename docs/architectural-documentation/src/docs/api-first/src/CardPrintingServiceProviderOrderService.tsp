import "@typespec/http";
import "@typespec/rest";
import "@typespec/versioning";
import "@typespec/openapi3";
import "./request/FishingLicenseOrderRequest.tsp";

using TypeSpec.Http;
using TypeSpec.Versioning;

@service({
  title: "Card Printing Service Provider Order Service API",
})
@useAuth(
  KeycloakAuthWithAuthorizationCode<[
    // New Scopes:
    "register"
  ]>
)
@versioned(FischereiregisterCardPrintingServiceProvider.Versions)
@doc("""
  Über diese Schnittstelle initiiert das Register einen einzelnen Druck- bzw. Herstellungsauftrag.
  Die Schnittstelle ist vom Druckdienstleister bzw. Kartenhersteller zu implementierten bzw. 
  zur Verfügung zu stellen.
  
  Als Anlage steht ebenfalls eine technische Beschreibung dieser Schnittstelle im OpenAPI-Format
  zur Verfügung.
  """)
namespace FischereiregisterCardPrintingServiceProvider;
enum Versions {
  `0.0.3`,
}

//  Models

model CardPrintingOrderServiceError<Status> extends Response<Status> {
  @doc("Eindeutiger Verweis auf den Fehler") errorId: string;
  @doc("menschlich lesbarer Fehlertext (deutsch)") errorNote: string;
}

model CardPrintingOrderServiceSuccess {
  @doc("eindeutige Bestell-Nr.") orderId: string;
}

// Aliases

alias WithCardPrintingOrderServiceStandardErrors<T> =
  | SuccessResponse<T>
  | CardPrintingOrderServiceError<400>
  | CardPrintingOrderServiceError<500>
  | UnauthorizedResponse;

// Endpoints

@tag("Card Printing Service-Provider")
@route("/fishing-license-order")
@useAuth(KeycloakAuthWithAuthorizationCode<["register"]>)
interface ServiceFishingLicenseOrderController {
  @doc("Erstellen einer Kartenbestellung")
  @post
  create(
    @body content: FishingLicenseOrderRequest,
  ): WithCardPrintingOrderServiceStandardErrors<CardPrintingOrderServiceSuccess>;

  @doc("Abbrechen einer Kartenbestellung")
  @delete
  delete(@path orderId: string):
    | AcceptedResponse
    | NotFoundResponse
    | UnauthorizedResponse
    | ServerErrorResponse;
}
