import "@typespec/http";
import "@typespec/rest";
import "@typespec/versioning";
import "@typespec/openapi3";
import "./AddressOS.tsp";

@doc("Represents the personal data of a person")
model PersonWithAddressOS {
    title?: string;
    firstname: string;
    lastname: string;
    birthdate: string;
    birthname?: string;
    birthplace: string;

    @doc("The Nationality is not mandatory since it does not exist for preliminary entries (created by an examiner).")
    nationality: string;

    @doc("Address information of the person.")
    address: AddressOS;
}
