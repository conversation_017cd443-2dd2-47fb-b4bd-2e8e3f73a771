import "./enum/FederalStateAbbreviation.tsp";
import "./enum/LimitedLicenseApplicationStatus.tsp";

@doc("Represents the application for a LIMTIED type fishing license. ")
model LimitedLicenseApplication {
    @doc("The unique identifier of the limited license application.")
    id: string;

    @doc("The status of the limited license application.")
    status: LimitedLicenseApplicationStatus;

    @doc("The date when the application was created.")
    createdAt: string;

    @doc("The federal state where the application was submitted.")
    federalState: FederalStateAbbreviation;

    @doc("URL to the secured disability certificate file. The URL should be secured and only accessible by authorized government officials.")
    disabilityCertificateFileURL: string;
}