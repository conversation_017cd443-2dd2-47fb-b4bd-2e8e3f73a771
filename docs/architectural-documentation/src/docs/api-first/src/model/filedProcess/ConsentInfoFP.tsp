@doc("Contains information about consent or other neccessary to be confirmed information connected to a request to the API.")
model ConsentInfoFP {
    @doc("Indicates if the submission was made by a third party.")
    submittedByThirdParty: boolean;

    @doc("Indicates if GDPR terms have been accepted.")
    gdprAccepted: boolean;

    @doc("Indicates if self-disclosure terms have been accepted.")
    selfDisclosureAccepted?: boolean;

    @doc("Indicates if a disability certificate was verified, if neccessary. Is set to true, when no verification is needed.")
    disabilityCertificateVerified?: boolean;

    @doc("""
    This is a flag necessary to be seen, when the jurisdiction of a register entry changes.

    If this is not the case, this option is not neccessary to be set.
    """)
    proofOfMoveVerified?: boolean;
}
