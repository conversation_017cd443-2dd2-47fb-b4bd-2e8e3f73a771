import "./WithOnlineServiceContextOS.tsp";
import "../WithConsentInformation.tsp";
import "../WithValidityPeriod.tsp";
import "../WithFederalState.tsp";
import "../../model/os/FeeOS.tsp";

model ExtendVacationLicenseRequestOS {
    @doc("License number connected to the register entry.")
    licenseNumber: string;

    @doc("Transaction id of the payment.")
    transactionId: string;

    @doc("Fee that is payed together with this request.")
    fee: FeeOS;

    ...WithFederalState;
    ...WithValidityPeriod;
    ...WithOnlineServiceContextOS;
    ...WithConsentInformation;
}
