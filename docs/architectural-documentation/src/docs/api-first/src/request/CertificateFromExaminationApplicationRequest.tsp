import "../model/es/PersonES.tsp";
import "../model/enum/FederalStateAbbreviation.tsp";
import "./WithFederalState.tsp";

model CertificateFromExaminationApplicationRequest {
    person: PersonES;
    certificate: {
        // This will most likely contain more information, since the keycloak user is not per examiner in the examination software

        @doc("When the Proof was passed, format DD.MM.YYYY")
        passedOn: string;
    };

    ...WithFederalState;
}
