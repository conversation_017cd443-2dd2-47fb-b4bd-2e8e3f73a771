import "../model/enum/LicenseType.tsp";
import "../model/enum/LicenseValidationStatus.tsp";

@doc("Represents a Response Object for respective Validation Request")
model ValidationResponse {
    @doc("The Fishing License Number")
    licenseNumber: string;

    @doc("The Type of the Fishing License")
    licenseType: LicenseType;

    @doc("False if the License is expired or the person is banned from fishing, true otherwise")
    licenseStatus: LicenseValidationStatus;

    @doc("additional Note to the License Status") licenseNote: string;

    @doc("True if the payment status of the License is valid, false otherwise")
    taxValid: boolean; // unpaid

    @doc("additional Note to the Payment Status") taxNote: string;
    @doc("The Person Title (like Dr.). If the person has no title, and empty string is delivered") title: string;
    @doc("the Person Given Names") givenNames: string;
    @doc("the Person Surname") surname: string;
    @doc("the Person Birthdate") birthdate: string;
}
