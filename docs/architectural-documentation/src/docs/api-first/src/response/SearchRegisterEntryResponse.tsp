import "../model/Jurisdiction.tsp";

alias SearchRegisterEntryResponse = SearchItem[];

model SearchItem {
    @doc("Register Entry Identifier")
    registerId: string;

    @doc("Minified version of the personal data (without address information)")
    person: {
        @doc("title") title?: string;
        @doc("firstname") firstname: string;
        @doc("lastname") lastname: string;
        @doc("birthdate, formatted as following: DD.MM.YYYY") birthdate: string;
        @doc("birthname") birthname?: string;
        @doc("birthplace") birthplace?: string;
    };

    @doc("Contains information when the fishing licenses attached to this register entry have been issued.")
    fishingLicenses: {
        @doc("16-digit identification number for fishing licenses.")
        number: string;
    }[];

    qualificationsProofs: {
        @doc("16-digit identification number for fishing certificates. Always starts with the letters PC.")
        fishingCertificateId: string;
    }[];
}
