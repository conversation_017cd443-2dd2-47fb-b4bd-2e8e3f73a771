<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_0rge5bw" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.36.1" modeler:executionPlatform="Camunda Cloud" modeler:executionPlatformVersion="8.7.0">
  <bpmn:collaboration id="Collaboration_14tajol">
    <bpmn:participant id="Participant_0e12w15" name="Ban" processRef="Process_0y5zlf2" />
  </bpmn:collaboration>
  <bpmn:process id="Process_0y5zlf2" isExecutable="true">
    <bpmn:laneSet id="LaneSet_1u8aafr">
      <bpmn:lane id="Lane_0j9uhbw">
        <bpmn:flowNodeRef>StartEvent_1</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Activity_1po3b83</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Event_1hpq2jp</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Event_12ruiof</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Event_1t7yrbw</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Activity_0tei5tf</bpmn:flowNodeRef>
      </bpmn:lane>
      <bpmn:lane id="Lane_08di39u">
        <bpmn:flowNodeRef>Event_01c57uj</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Activity_1dt8q9t</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Event_0jc0jkc</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Event_13e2sxy</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Activity_01ow753</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Event_0px8cuw</bpmn:flowNodeRef>
      </bpmn:lane>
      <bpmn:lane id="Lane_0n0l9w3">
        <bpmn:flowNodeRef>Event_0fex4ul</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Gateway_0jnafrl</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Activity_1c5j0x8</bpmn:flowNodeRef>
      </bpmn:lane>
    </bpmn:laneSet>
    <bpmn:startEvent id="StartEvent_1">
      <bpmn:outgoing>Flow_0txghil</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="Flow_0txghil" sourceRef="StartEvent_1" targetRef="Activity_1po3b83" />
    <bpmn:serviceTask id="Activity_1po3b83" name="BanController">
      <bpmn:incoming>Flow_0txghil</bpmn:incoming>
      <bpmn:outgoing>Flow_01i0y2s</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:startEvent id="Event_1hpq2jp">
      <bpmn:outgoing>Flow_01nb547</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="Flow_01nb547" sourceRef="Event_1hpq2jp" targetRef="Activity_0tei5tf" />
    <bpmn:intermediateThrowEvent id="Event_12ruiof" name="BanTemporarilyCommand">
      <bpmn:incoming>Flow_01i0y2s</bpmn:incoming>
      <bpmn:outgoing>Flow_12y79jy</bpmn:outgoing>
      <bpmn:messageEventDefinition id="MessageEventDefinition_0rjhpv5" />
    </bpmn:intermediateThrowEvent>
    <bpmn:sequenceFlow id="Flow_01i0y2s" sourceRef="Activity_1po3b83" targetRef="Event_12ruiof" />
    <bpmn:sequenceFlow id="Flow_03h5788" sourceRef="Activity_0tei5tf" targetRef="Event_1t7yrbw" />
    <bpmn:intermediateThrowEvent id="Event_1t7yrbw" name="BanPermanentlyCommand">
      <bpmn:incoming>Flow_03h5788</bpmn:incoming>
      <bpmn:outgoing>Flow_0aoqjcd</bpmn:outgoing>
      <bpmn:messageEventDefinition id="MessageEventDefinition_1v5q8zx" />
    </bpmn:intermediateThrowEvent>
    <bpmn:sequenceFlow id="Flow_0aoqjcd" sourceRef="Event_1t7yrbw" targetRef="Event_01c57uj" />
    <bpmn:sequenceFlow id="Flow_12y79jy" sourceRef="Event_12ruiof" targetRef="Event_01c57uj" />
    <bpmn:serviceTask id="Activity_0tei5tf" name="BanConroller">
      <bpmn:incoming>Flow_01nb547</bpmn:incoming>
      <bpmn:outgoing>Flow_03h5788</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_16us71r" sourceRef="Event_01c57uj" targetRef="Activity_1dt8q9t" />
    <bpmn:sequenceFlow id="Flow_0305kjn" sourceRef="Activity_1dt8q9t" targetRef="Event_0jc0jkc" />
    <bpmn:intermediateCatchEvent id="Event_01c57uj">
      <bpmn:incoming>Flow_0aoqjcd</bpmn:incoming>
      <bpmn:incoming>Flow_12y79jy</bpmn:incoming>
      <bpmn:outgoing>Flow_16us71r</bpmn:outgoing>
      <bpmn:messageEventDefinition id="MessageEventDefinition_0hi1xoo" />
    </bpmn:intermediateCatchEvent>
    <bpmn:serviceTask id="Activity_1dt8q9t" name="RegisterEntry">
      <bpmn:incoming>Flow_16us71r</bpmn:incoming>
      <bpmn:outgoing>Flow_0305kjn</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:intermediateThrowEvent id="Event_0jc0jkc" name="BannedEvent">
      <bpmn:incoming>Flow_0305kjn</bpmn:incoming>
      <bpmn:outgoing>Flow_1isu2tr</bpmn:outgoing>
      <bpmn:messageEventDefinition id="MessageEventDefinition_0vbuo3d" />
    </bpmn:intermediateThrowEvent>
    <bpmn:sequenceFlow id="Flow_1isu2tr" sourceRef="Event_0jc0jkc" targetRef="Event_0fex4ul" />
    <bpmn:sequenceFlow id="Flow_0s5t3pb" sourceRef="Event_0fex4ul" targetRef="Gateway_0jnafrl" />
    <bpmn:sequenceFlow id="Flow_1q3vsm3" sourceRef="Gateway_0jnafrl" targetRef="Event_13e2sxy" />
    <bpmn:intermediateCatchEvent id="Event_13e2sxy">
      <bpmn:incoming>Flow_1q3vsm3</bpmn:incoming>
      <bpmn:outgoing>Flow_0yc9kmg</bpmn:outgoing>
      <bpmn:messageEventDefinition id="MessageEventDefinition_18vejf6" />
    </bpmn:intermediateCatchEvent>
    <bpmn:serviceTask id="Activity_01ow753" name="RegisterEntry">
      <bpmn:incoming>Flow_0yc9kmg</bpmn:incoming>
      <bpmn:outgoing>Flow_0uzsisf</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0yc9kmg" sourceRef="Event_13e2sxy" targetRef="Activity_01ow753" />
    <bpmn:endEvent id="Event_0px8cuw">
      <bpmn:incoming>Flow_0uzsisf</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0uzsisf" sourceRef="Activity_01ow753" targetRef="Event_0px8cuw" />
    <bpmn:intermediateCatchEvent id="Event_0fex4ul">
      <bpmn:incoming>Flow_1isu2tr</bpmn:incoming>
      <bpmn:outgoing>Flow_0s5t3pb</bpmn:outgoing>
      <bpmn:messageEventDefinition id="MessageEventDefinition_041jiak" />
    </bpmn:intermediateCatchEvent>
    <bpmn:parallelGateway id="Gateway_0jnafrl">
      <bpmn:incoming>Flow_0s5t3pb</bpmn:incoming>
      <bpmn:outgoing>Flow_1q3vsm3</bpmn:outgoing>
      <bpmn:outgoing>Flow_0o598zr</bpmn:outgoing>
    </bpmn:parallelGateway>
    <bpmn:task id="Activity_1c5j0x8">
      <bpmn:incoming>Flow_0o598zr</bpmn:incoming>
    </bpmn:task>
    <bpmn:sequenceFlow id="Flow_0o598zr" sourceRef="Gateway_0jnafrl" targetRef="Activity_1c5j0x8" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Collaboration_14tajol">
      <bpmndi:BPMNShape id="Participant_0e12w15_di" bpmnElement="Participant_0e12w15" isHorizontal="true">
        <dc:Bounds x="132" y="110" width="1028" height="610" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_0j9uhbw_di" bpmnElement="Lane_0j9uhbw" isHorizontal="true">
        <dc:Bounds x="162" y="110" width="998" height="250" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_08di39u_di" bpmnElement="Lane_08di39u" isHorizontal="true">
        <dc:Bounds x="162" y="360" width="998" height="120" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_0n0l9w3_di" bpmnElement="Lane_0n0l9w3" isHorizontal="true">
        <dc:Bounds x="162" y="480" width="998" height="240" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="StartEvent_1_di" bpmnElement="StartEvent_1">
        <dc:Bounds x="182" y="162" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1po3b83_di" bpmnElement="Activity_1po3b83">
        <dc:Bounds x="280" y="140" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1hpq2jp_di" bpmnElement="Event_1hpq2jp">
        <dc:Bounds x="182" y="282" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_12ruiof_di" bpmnElement="Event_12ruiof">
        <dc:Bounds x="442" y="162" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="421" y="124.5" width="77" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1t7yrbw_di" bpmnElement="Event_1t7yrbw">
        <dc:Bounds x="442" y="282" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="421" y="244.5" width="77" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0tei5tf_di" bpmnElement="Activity_0tei5tf">
        <dc:Bounds x="280" y="260" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_01c57uj_di" bpmnElement="Event_01c57uj">
        <dc:Bounds x="442" y="402" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1dt8q9t_di" bpmnElement="Activity_1dt8q9t">
        <dc:Bounds x="530" y="380" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0jc0jkc_di" bpmnElement="Event_0jc0jkc">
        <dc:Bounds x="682" y="402" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="666" y="378" width="67" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_13e2sxy_di" bpmnElement="Event_13e2sxy">
        <dc:Bounds x="782" y="402" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1tr68bp" bpmnElement="Activity_01ow753">
        <dc:Bounds x="870" y="380" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0px8cuw_di" bpmnElement="Event_0px8cuw">
        <dc:Bounds x="1022" y="402" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0fex4ul_di" bpmnElement="Event_0fex4ul">
        <dc:Bounds x="682" y="542" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0jnafrl_di" bpmnElement="Gateway_0jnafrl">
        <dc:Bounds x="775" y="535" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1c5j0x8_di" bpmnElement="Activity_1c5j0x8">
        <dc:Bounds x="890" y="520" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_0txghil_di" bpmnElement="Flow_0txghil">
        <di:waypoint x="218" y="180" />
        <di:waypoint x="280" y="180" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_01nb547_di" bpmnElement="Flow_01nb547">
        <di:waypoint x="218" y="300" />
        <di:waypoint x="280" y="300" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_01i0y2s_di" bpmnElement="Flow_01i0y2s">
        <di:waypoint x="380" y="180" />
        <di:waypoint x="442" y="180" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_03h5788_di" bpmnElement="Flow_03h5788">
        <di:waypoint x="380" y="300" />
        <di:waypoint x="442" y="300" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0aoqjcd_di" bpmnElement="Flow_0aoqjcd">
        <di:waypoint x="460" y="318" />
        <di:waypoint x="460" y="402" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_12y79jy_di" bpmnElement="Flow_12y79jy">
        <di:waypoint x="478" y="180" />
        <di:waypoint x="520" y="180" />
        <di:waypoint x="520" y="340" />
        <di:waypoint x="460" y="340" />
        <di:waypoint x="460" y="402" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_16us71r_di" bpmnElement="Flow_16us71r">
        <di:waypoint x="478" y="420" />
        <di:waypoint x="530" y="420" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0305kjn_di" bpmnElement="Flow_0305kjn">
        <di:waypoint x="630" y="420" />
        <di:waypoint x="682" y="420" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1isu2tr_di" bpmnElement="Flow_1isu2tr">
        <di:waypoint x="700" y="438" />
        <di:waypoint x="700" y="542" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0s5t3pb_di" bpmnElement="Flow_0s5t3pb">
        <di:waypoint x="718" y="560" />
        <di:waypoint x="775" y="560" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1q3vsm3_di" bpmnElement="Flow_1q3vsm3">
        <di:waypoint x="800" y="535" />
        <di:waypoint x="800" y="438" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0yc9kmg_di" bpmnElement="Flow_0yc9kmg">
        <di:waypoint x="818" y="420" />
        <di:waypoint x="870" y="420" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0uzsisf_di" bpmnElement="Flow_0uzsisf">
        <di:waypoint x="970" y="420" />
        <di:waypoint x="1022" y="420" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0o598zr_di" bpmnElement="Flow_0o598zr">
        <di:waypoint x="825" y="560" />
        <di:waypoint x="890" y="560" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
