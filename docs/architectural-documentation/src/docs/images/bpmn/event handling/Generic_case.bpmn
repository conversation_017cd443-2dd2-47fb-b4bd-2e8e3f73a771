<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_1prd8mo" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.36.1" modeler:executionPlatform="Camunda Cloud" modeler:executionPlatformVersion="8.7.0">
  <bpmn:collaboration id="Collaboration_1heijwu">
    <bpmn:participant id="Participant_1mj9yvm" name="General Case" processRef="Process_1ym35ia" />
  </bpmn:collaboration>
  <bpmn:process id="Process_1ym35ia" isExecutable="true">
    <bpmn:laneSet id="LaneSet_1qyqb0a">
      <bpmn:lane id="Lane_0xrwvpv">
        <bpmn:flowNodeRef>Gateway_00zkx50</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Activity_0cymk8u</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Event_1r0m2xs</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Event_1qqr54o</bpmn:flowNodeRef>
      </bpmn:lane>
      <bpmn:lane id="Lane_1hyemlq">
        <bpmn:flowNodeRef>Activity_1w7qzs8</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Activity_0wnvvba</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Activity_1qef1k1</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Activity_0x3dwzb</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Event_1uohq3j</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Event_1ni80by</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Activity_078et1z</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Activity_0o9dzkz</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Event_060p3aw</bpmn:flowNodeRef>
      </bpmn:lane>
    </bpmn:laneSet>
    <bpmn:serviceTask id="Activity_1w7qzs8" name="RegisterEntryViewHandler">
      <bpmn:incoming>Flow_1ahgxfz</bpmn:incoming>
      <bpmn:outgoing>Flow_1lvw11h</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_0wnvvba" name="RegisterEntryViewService">
      <bpmn:incoming>Flow_1lvw11h</bpmn:incoming>
      <bpmn:outgoing>Flow_1xttbxv</bpmn:outgoing>
      <bpmn:dataOutputAssociation id="DataOutputAssociation_189zys4">
        <bpmn:targetRef>DataStoreReference_0kbef7o</bpmn:targetRef>
      </bpmn:dataOutputAssociation>
    </bpmn:serviceTask>
    <bpmn:dataStoreReference id="DataStoreReference_0kbef7o" name="RegisterEntryView" />
    <bpmn:serviceTask id="Activity_1qef1k1" name="GdprEventViewHandler">
      <bpmn:incoming>Flow_1y5mos7</bpmn:incoming>
      <bpmn:outgoing>Flow_1jt3y6n</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_0x3dwzb" name="GdprEventViewService">
      <bpmn:incoming>Flow_1jt3y6n</bpmn:incoming>
      <bpmn:outgoing>Flow_1nc8xvi</bpmn:outgoing>
      <bpmn:dataOutputAssociation id="DataOutputAssociation_17p3vop">
        <bpmn:targetRef>DataStoreReference_0132rdp</bpmn:targetRef>
      </bpmn:dataOutputAssociation>
    </bpmn:serviceTask>
    <bpmn:dataStoreReference id="DataStoreReference_0132rdp" name="GdprEventView" />
    <bpmn:endEvent id="Event_1uohq3j">
      <bpmn:incoming>Flow_1nc8xvi</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:endEvent id="Event_1ni80by">
      <bpmn:incoming>Flow_1xttbxv</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="Activity_078et1z" name="FiledProcessViewHandler">
      <bpmn:incoming>Flow_1n2nwrh</bpmn:incoming>
      <bpmn:outgoing>Flow_1sq8f28</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_0o9dzkz" name="FiledProcessViewService">
      <bpmn:incoming>Flow_1sq8f28</bpmn:incoming>
      <bpmn:outgoing>Flow_1e2cv8r</bpmn:outgoing>
      <bpmn:dataOutputAssociation id="DataOutputAssociation_1mtue80">
        <bpmn:targetRef>DataStoreReference_08oup82</bpmn:targetRef>
      </bpmn:dataOutputAssociation>
    </bpmn:serviceTask>
    <bpmn:endEvent id="Event_060p3aw">
      <bpmn:incoming>Flow_1e2cv8r</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:dataStoreReference id="DataStoreReference_08oup82" name="FiledProcessView" />
    <bpmn:parallelGateway id="Gateway_00zkx50">
      <bpmn:incoming>Flow_1ylz1cf</bpmn:incoming>
      <bpmn:outgoing>Flow_0uj6mlz</bpmn:outgoing>
      <bpmn:outgoing>Flow_1ahgxfz</bpmn:outgoing>
      <bpmn:outgoing>Flow_1y5mos7</bpmn:outgoing>
      <bpmn:outgoing>Flow_1n2nwrh</bpmn:outgoing>
    </bpmn:parallelGateway>
    <bpmn:serviceTask id="Activity_0cymk8u" name="RegisterEntry">
      <bpmn:incoming>Flow_0uj6mlz</bpmn:incoming>
      <bpmn:outgoing>Flow_1jks46q</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="Event_1r0m2xs">
      <bpmn:incoming>Flow_1jks46q</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:startEvent id="Event_1qqr54o" name="Event">
      <bpmn:outgoing>Flow_1ylz1cf</bpmn:outgoing>
      <bpmn:messageEventDefinition id="MessageEventDefinition_186jx1p" />
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="Flow_1ahgxfz" sourceRef="Gateway_00zkx50" targetRef="Activity_1w7qzs8" />
    <bpmn:sequenceFlow id="Flow_1lvw11h" sourceRef="Activity_1w7qzs8" targetRef="Activity_0wnvvba" />
    <bpmn:sequenceFlow id="Flow_1xttbxv" sourceRef="Activity_0wnvvba" targetRef="Event_1ni80by" />
    <bpmn:sequenceFlow id="Flow_1y5mos7" sourceRef="Gateway_00zkx50" targetRef="Activity_1qef1k1" />
    <bpmn:sequenceFlow id="Flow_1jt3y6n" sourceRef="Activity_1qef1k1" targetRef="Activity_0x3dwzb" />
    <bpmn:sequenceFlow id="Flow_1nc8xvi" sourceRef="Activity_0x3dwzb" targetRef="Event_1uohq3j" />
    <bpmn:sequenceFlow id="Flow_1n2nwrh" sourceRef="Gateway_00zkx50" targetRef="Activity_078et1z" />
    <bpmn:sequenceFlow id="Flow_1sq8f28" sourceRef="Activity_078et1z" targetRef="Activity_0o9dzkz" />
    <bpmn:sequenceFlow id="Flow_1e2cv8r" sourceRef="Activity_0o9dzkz" targetRef="Event_060p3aw" />
    <bpmn:sequenceFlow id="Flow_1ylz1cf" sourceRef="Event_1qqr54o" targetRef="Gateway_00zkx50" />
    <bpmn:sequenceFlow id="Flow_0uj6mlz" sourceRef="Gateway_00zkx50" targetRef="Activity_0cymk8u" />
    <bpmn:sequenceFlow id="Flow_1jks46q" sourceRef="Activity_0cymk8u" targetRef="Event_1r0m2xs" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Collaboration_1heijwu">
      <bpmndi:BPMNShape id="Participant_1mj9yvm_di" bpmnElement="Participant_1mj9yvm" isHorizontal="true">
        <dc:Bounds x="132" y="80" width="678" height="800" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_0xrwvpv_di" bpmnElement="Lane_0xrwvpv" isHorizontal="true">
        <dc:Bounds x="162" y="80" width="648" height="140" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_1hyemlq_di" bpmnElement="Lane_1hyemlq" isHorizontal="true">
        <dc:Bounds x="162" y="220" width="648" height="660" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1w7qzs8_di" bpmnElement="Activity_1w7qzs8">
        <dc:Bounds x="400" y="250" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1kzkggo" bpmnElement="Activity_0wnvvba">
        <dc:Bounds x="570" y="250" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="DataStoreReference_0kbef7o_di" bpmnElement="DataStoreReference_0kbef7o">
        <dc:Bounds x="595" y="355" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="579" y="412" width="83" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0sml0cw" bpmnElement="Activity_1qef1k1">
        <dc:Bounds x="400" y="470" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1dvlwoi" bpmnElement="Activity_0x3dwzb">
        <dc:Bounds x="570" y="470" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1ci317b" bpmnElement="DataStoreReference_0132rdp">
        <dc:Bounds x="595" y="575" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="582" y="632" width="78" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1uohq3j_di" bpmnElement="Event_1uohq3j">
        <dc:Bounds x="742" y="492" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1ni80by_di" bpmnElement="Event_1ni80by">
        <dc:Bounds x="742" y="272" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1cspefr" bpmnElement="Activity_078et1z">
        <dc:Bounds x="400" y="690" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0e5k4k3" bpmnElement="Activity_0o9dzkz">
        <dc:Bounds x="570" y="690" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_060p3aw_di" bpmnElement="Event_060p3aw">
        <dc:Bounds x="742" y="712" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_04x6szu" bpmnElement="DataStoreReference_08oup82">
        <dc:Bounds x="595" y="795" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="577" y="852" width="89" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_00zkx50_di" bpmnElement="Gateway_00zkx50">
        <dc:Bounds x="285" y="125" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0cymk8u_di" bpmnElement="Activity_0cymk8u">
        <dc:Bounds x="400" y="110" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1r0m2xs_di" bpmnElement="Event_1r0m2xs">
        <dc:Bounds x="602" y="132" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1qqr54o_di" bpmnElement="Event_1qqr54o">
        <dc:Bounds x="192" y="132" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="195" y="175" width="30" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1ahgxfz_di" bpmnElement="Flow_1ahgxfz">
        <di:waypoint x="310" y="175" />
        <di:waypoint x="310" y="290" />
        <di:waypoint x="400" y="290" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1lvw11h_di" bpmnElement="Flow_1lvw11h">
        <di:waypoint x="500" y="290" />
        <di:waypoint x="570" y="290" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1xttbxv_di" bpmnElement="Flow_1xttbxv">
        <di:waypoint x="670" y="290" />
        <di:waypoint x="742" y="290" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1y5mos7_di" bpmnElement="Flow_1y5mos7">
        <di:waypoint x="310" y="175" />
        <di:waypoint x="310" y="510" />
        <di:waypoint x="400" y="510" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1jt3y6n_di" bpmnElement="Flow_1jt3y6n">
        <di:waypoint x="500" y="510" />
        <di:waypoint x="570" y="510" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1nc8xvi_di" bpmnElement="Flow_1nc8xvi">
        <di:waypoint x="670" y="510" />
        <di:waypoint x="742" y="510" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1n2nwrh_di" bpmnElement="Flow_1n2nwrh">
        <di:waypoint x="310" y="175" />
        <di:waypoint x="310" y="730" />
        <di:waypoint x="400" y="730" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1sq8f28_di" bpmnElement="Flow_1sq8f28">
        <di:waypoint x="500" y="730" />
        <di:waypoint x="570" y="730" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1e2cv8r_di" bpmnElement="Flow_1e2cv8r">
        <di:waypoint x="670" y="730" />
        <di:waypoint x="742" y="730" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ylz1cf_di" bpmnElement="Flow_1ylz1cf">
        <di:waypoint x="228" y="150" />
        <di:waypoint x="285" y="150" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0uj6mlz_di" bpmnElement="Flow_0uj6mlz">
        <di:waypoint x="335" y="150" />
        <di:waypoint x="400" y="150" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1jks46q_di" bpmnElement="Flow_1jks46q">
        <di:waypoint x="500" y="150" />
        <di:waypoint x="602" y="150" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="DataOutputAssociation_189zys4_di" bpmnElement="DataOutputAssociation_189zys4">
        <di:waypoint x="620" y="330" />
        <di:waypoint x="620" y="355" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="DataOutputAssociation_17p3vop_di" bpmnElement="DataOutputAssociation_17p3vop">
        <di:waypoint x="620" y="550" />
        <di:waypoint x="620" y="575" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="DataOutputAssociation_1mtue80_di" bpmnElement="DataOutputAssociation_1mtue80">
        <di:waypoint x="620" y="770" />
        <di:waypoint x="620" y="795" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
